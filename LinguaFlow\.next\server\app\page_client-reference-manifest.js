globalThis.__RSC_MANIFEST = globalThis.__RSC_MANIFEST || {};
globalThis.__RSC_MANIFEST["/page"] = {"moduleLoading":{"prefix":"/_next/","crossOrigin":null},"clientModules":{"[project]/node_modules/next/dist/esm/client/components/layout-router.js <module evaluation>":{"id":"[project]/node_modules/next/dist/client/components/layout-router.js [app-client] (ecmascript)","name":"*","chunks":["static/chunks/node_modules_next_dist_1a6ee436._.js","static/chunks/src_app_favicon_ico_mjs_79b6a596._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/layout-router.js":{"id":"[project]/node_modules/next/dist/client/components/layout-router.js [app-client] (ecmascript)","name":"*","chunks":["static/chunks/node_modules_next_dist_1a6ee436._.js","static/chunks/src_app_favicon_ico_mjs_79b6a596._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/render-from-template-context.js <module evaluation>":{"id":"[project]/node_modules/next/dist/client/components/render-from-template-context.js [app-client] (ecmascript)","name":"*","chunks":["static/chunks/node_modules_next_dist_1a6ee436._.js","static/chunks/src_app_favicon_ico_mjs_79b6a596._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/render-from-template-context.js":{"id":"[project]/node_modules/next/dist/client/components/render-from-template-context.js [app-client] (ecmascript)","name":"*","chunks":["static/chunks/node_modules_next_dist_1a6ee436._.js","static/chunks/src_app_favicon_ico_mjs_79b6a596._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/client-page.js <module evaluation>":{"id":"[project]/node_modules/next/dist/client/components/client-page.js [app-client] (ecmascript)","name":"*","chunks":["static/chunks/node_modules_next_dist_1a6ee436._.js","static/chunks/src_app_favicon_ico_mjs_79b6a596._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/client-page.js":{"id":"[project]/node_modules/next/dist/client/components/client-page.js [app-client] (ecmascript)","name":"*","chunks":["static/chunks/node_modules_next_dist_1a6ee436._.js","static/chunks/src_app_favicon_ico_mjs_79b6a596._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/client-segment.js <module evaluation>":{"id":"[project]/node_modules/next/dist/client/components/client-segment.js [app-client] (ecmascript)","name":"*","chunks":["static/chunks/node_modules_next_dist_1a6ee436._.js","static/chunks/src_app_favicon_ico_mjs_79b6a596._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/client-segment.js":{"id":"[project]/node_modules/next/dist/client/components/client-segment.js [app-client] (ecmascript)","name":"*","chunks":["static/chunks/node_modules_next_dist_1a6ee436._.js","static/chunks/src_app_favicon_ico_mjs_79b6a596._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/http-access-fallback/error-boundary.js <module evaluation>":{"id":"[project]/node_modules/next/dist/client/components/http-access-fallback/error-boundary.js [app-client] (ecmascript)","name":"*","chunks":["static/chunks/node_modules_next_dist_1a6ee436._.js","static/chunks/src_app_favicon_ico_mjs_79b6a596._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/http-access-fallback/error-boundary.js":{"id":"[project]/node_modules/next/dist/client/components/http-access-fallback/error-boundary.js [app-client] (ecmascript)","name":"*","chunks":["static/chunks/node_modules_next_dist_1a6ee436._.js","static/chunks/src_app_favicon_ico_mjs_79b6a596._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/metadata/async-metadata.js <module evaluation>":{"id":"[project]/node_modules/next/dist/client/components/metadata/async-metadata.js [app-client] (ecmascript)","name":"*","chunks":["static/chunks/node_modules_next_dist_1a6ee436._.js","static/chunks/src_app_favicon_ico_mjs_79b6a596._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/metadata/async-metadata.js":{"id":"[project]/node_modules/next/dist/client/components/metadata/async-metadata.js [app-client] (ecmascript)","name":"*","chunks":["static/chunks/node_modules_next_dist_1a6ee436._.js","static/chunks/src_app_favicon_ico_mjs_79b6a596._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/error-boundary.js <module evaluation>":{"id":"[project]/node_modules/next/dist/client/components/error-boundary.js [app-client] (ecmascript)","name":"*","chunks":["static/chunks/node_modules_next_dist_1a6ee436._.js","static/chunks/src_app_favicon_ico_mjs_79b6a596._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/error-boundary.js":{"id":"[project]/node_modules/next/dist/client/components/error-boundary.js [app-client] (ecmascript)","name":"*","chunks":["static/chunks/node_modules_next_dist_1a6ee436._.js","static/chunks/src_app_favicon_ico_mjs_79b6a596._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/metadata/metadata-boundary.js <module evaluation>":{"id":"[project]/node_modules/next/dist/client/components/metadata/metadata-boundary.js [app-client] (ecmascript)","name":"*","chunks":["static/chunks/node_modules_next_dist_1a6ee436._.js","static/chunks/src_app_favicon_ico_mjs_79b6a596._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/metadata/metadata-boundary.js":{"id":"[project]/node_modules/next/dist/client/components/metadata/metadata-boundary.js [app-client] (ecmascript)","name":"*","chunks":["static/chunks/node_modules_next_dist_1a6ee436._.js","static/chunks/src_app_favicon_ico_mjs_79b6a596._.js"],"async":false},"[project]/node_modules/next/dist/client/components/error-boundary.js <module evaluation>":{"id":"[project]/node_modules/next/dist/client/components/error-boundary.js [app-client] (ecmascript)","name":"*","chunks":["static/chunks/node_modules_next_dist_1a6ee436._.js","static/chunks/src_app_favicon_ico_mjs_79b6a596._.js"],"async":false},"[project]/node_modules/next/dist/client/components/error-boundary.js":{"id":"[project]/node_modules/next/dist/client/components/error-boundary.js [app-client] (ecmascript)","name":"*","chunks":["static/chunks/node_modules_next_dist_1a6ee436._.js","static/chunks/src_app_favicon_ico_mjs_79b6a596._.js"],"async":false},"[project]/src/components/ui/toaster.tsx <module evaluation>":{"id":"[project]/src/components/ui/toaster.tsx [app-client] (ecmascript)","name":"*","chunks":["static/chunks/src_23f492bb._.js","static/chunks/node_modules_a31e2e15._.js","static/chunks/src_app_layout_tsx_f0e4c1a2._.js"],"async":false},"[project]/src/components/ui/toaster.tsx":{"id":"[project]/src/components/ui/toaster.tsx [app-client] (ecmascript)","name":"*","chunks":["static/chunks/src_23f492bb._.js","static/chunks/node_modules_a31e2e15._.js","static/chunks/src_app_layout_tsx_f0e4c1a2._.js"],"async":false},"[project]/src/contexts/i18n-context.tsx <module evaluation>":{"id":"[project]/src/contexts/i18n-context.tsx [app-client] (ecmascript)","name":"*","chunks":["static/chunks/src_23f492bb._.js","static/chunks/node_modules_a31e2e15._.js","static/chunks/src_app_layout_tsx_f0e4c1a2._.js"],"async":false},"[project]/src/contexts/i18n-context.tsx":{"id":"[project]/src/contexts/i18n-context.tsx [app-client] (ecmascript)","name":"*","chunks":["static/chunks/src_23f492bb._.js","static/chunks/node_modules_a31e2e15._.js","static/chunks/src_app_layout_tsx_f0e4c1a2._.js"],"async":false},"[project]/src/contexts/theme-context.tsx <module evaluation>":{"id":"[project]/src/contexts/theme-context.tsx [app-client] (ecmascript)","name":"*","chunks":["static/chunks/src_23f492bb._.js","static/chunks/node_modules_a31e2e15._.js","static/chunks/src_app_layout_tsx_f0e4c1a2._.js"],"async":false},"[project]/src/contexts/theme-context.tsx":{"id":"[project]/src/contexts/theme-context.tsx [app-client] (ecmascript)","name":"*","chunks":["static/chunks/src_23f492bb._.js","static/chunks/node_modules_a31e2e15._.js","static/chunks/src_app_layout_tsx_f0e4c1a2._.js"],"async":false},"[project]/src/contexts/dictionary-context.tsx <module evaluation>":{"id":"[project]/src/contexts/dictionary-context.tsx [app-client] (ecmascript)","name":"*","chunks":["static/chunks/src_23f492bb._.js","static/chunks/node_modules_a31e2e15._.js","static/chunks/src_app_layout_tsx_f0e4c1a2._.js"],"async":false},"[project]/src/contexts/dictionary-context.tsx":{"id":"[project]/src/contexts/dictionary-context.tsx [app-client] (ecmascript)","name":"*","chunks":["static/chunks/src_23f492bb._.js","static/chunks/node_modules_a31e2e15._.js","static/chunks/src_app_layout_tsx_f0e4c1a2._.js"],"async":false},"[project]/src/contexts/feature-settings-context.tsx <module evaluation>":{"id":"[project]/src/contexts/feature-settings-context.tsx [app-client] (ecmascript)","name":"*","chunks":["static/chunks/src_23f492bb._.js","static/chunks/node_modules_a31e2e15._.js","static/chunks/src_app_layout_tsx_f0e4c1a2._.js"],"async":false},"[project]/src/contexts/feature-settings-context.tsx":{"id":"[project]/src/contexts/feature-settings-context.tsx [app-client] (ecmascript)","name":"*","chunks":["static/chunks/src_23f492bb._.js","static/chunks/node_modules_a31e2e15._.js","static/chunks/src_app_layout_tsx_f0e4c1a2._.js"],"async":false},"[project]/src/app/page.tsx <module evaluation>":{"id":"[project]/src/app/page.tsx [app-client] (ecmascript)","name":"*","chunks":["static/chunks/src_23f492bb._.js","static/chunks/node_modules_a31e2e15._.js","static/chunks/src_app_layout_tsx_f0e4c1a2._.js","static/chunks/node_modules_node-fetch_src_index_fa16faaa.js","static/chunks/src_01424a98._.js","static/chunks/node_modules_next_dist_compiled_stream-http_index_4b0f5eaa.js","static/chunks/node_modules_next_dist_compiled_assert_assert_eb302145.js","static/chunks/node_modules_next_dist_compiled_browserify-zlib_index_65a3892f.js","static/chunks/node_modules_next_dist_compiled_crypto-browserify_index_07270ec0.js","static/chunks/node_modules_next_dist_compiled_5dde6f15._.js","static/chunks/0f475_dist_build_webpack_loaders_next-flight-loader_action-client-wrapper_b74250f7.js","static/chunks/node_modules_zod_lib_18e6bb38._.js","static/chunks/node_modules_@genkit-ai_core_lib_81c4cc2c._.js","static/chunks/node_modules_ajv_dist_250e4da2._.js","static/chunks/node_modules_zod-to-json-schema_dist_0744899c._.js","static/chunks/node_modules_@opentelemetry_api_build_esm_03c46853._.js","static/chunks/node_modules_@opentelemetry_ff6525d0._.js","static/chunks/node_modules_@opentelemetry_ab8c9ec2._.js","static/chunks/node_modules_@opentelemetry_0442fd05._.js","static/chunks/node_modules_@opentelemetry_f56d57f2._.js","static/chunks/node_modules_@opentelemetry_14494b79._.js","static/chunks/node_modules_protobufjs_8dbde028._.js","static/chunks/node_modules_@opentelemetry_otlp-transformer_build_esm_3416722d._.js","static/chunks/node_modules_@grpc_grpc-js_6ca6ef0a._.js","static/chunks/node_modules_handlebars_dist_cjs_20fecba4._.js","static/chunks/node_modules_yaml_browser_efb39508._.js","static/chunks/node_modules_iconv-lite_f639b7eb._.js","static/chunks/node_modules_mime-db_1172ad10._.js","static/chunks/node_modules_@genkit-ai_ai_lib_93078ce1._.js","static/chunks/node_modules_@radix-ui_cf4fda66._.js","static/chunks/node_modules_@floating-ui_9ec1fa39._.js","static/chunks/node_modules_@opentelemetry_22904df5._.js","static/chunks/node_modules_c63b261d._.js","static/chunks/src_app_page_tsx_e3d75067._.js"],"async":false},"[project]/src/app/page.tsx":{"id":"[project]/src/app/page.tsx [app-client] (ecmascript)","name":"*","chunks":["static/chunks/src_23f492bb._.js","static/chunks/node_modules_a31e2e15._.js","static/chunks/src_app_layout_tsx_f0e4c1a2._.js","static/chunks/node_modules_node-fetch_src_index_fa16faaa.js","static/chunks/src_01424a98._.js","static/chunks/node_modules_next_dist_compiled_stream-http_index_4b0f5eaa.js","static/chunks/node_modules_next_dist_compiled_assert_assert_eb302145.js","static/chunks/node_modules_next_dist_compiled_browserify-zlib_index_65a3892f.js","static/chunks/node_modules_next_dist_compiled_crypto-browserify_index_07270ec0.js","static/chunks/node_modules_next_dist_compiled_5dde6f15._.js","static/chunks/0f475_dist_build_webpack_loaders_next-flight-loader_action-client-wrapper_b74250f7.js","static/chunks/node_modules_zod_lib_18e6bb38._.js","static/chunks/node_modules_@genkit-ai_core_lib_81c4cc2c._.js","static/chunks/node_modules_ajv_dist_250e4da2._.js","static/chunks/node_modules_zod-to-json-schema_dist_0744899c._.js","static/chunks/node_modules_@opentelemetry_api_build_esm_03c46853._.js","static/chunks/node_modules_@opentelemetry_ff6525d0._.js","static/chunks/node_modules_@opentelemetry_ab8c9ec2._.js","static/chunks/node_modules_@opentelemetry_0442fd05._.js","static/chunks/node_modules_@opentelemetry_f56d57f2._.js","static/chunks/node_modules_@opentelemetry_14494b79._.js","static/chunks/node_modules_protobufjs_8dbde028._.js","static/chunks/node_modules_@opentelemetry_otlp-transformer_build_esm_3416722d._.js","static/chunks/node_modules_@grpc_grpc-js_6ca6ef0a._.js","static/chunks/node_modules_handlebars_dist_cjs_20fecba4._.js","static/chunks/node_modules_yaml_browser_efb39508._.js","static/chunks/node_modules_iconv-lite_f639b7eb._.js","static/chunks/node_modules_mime-db_1172ad10._.js","static/chunks/node_modules_@genkit-ai_ai_lib_93078ce1._.js","static/chunks/node_modules_@radix-ui_cf4fda66._.js","static/chunks/node_modules_@floating-ui_9ec1fa39._.js","static/chunks/node_modules_@opentelemetry_22904df5._.js","static/chunks/node_modules_c63b261d._.js","static/chunks/src_app_page_tsx_e3d75067._.js"],"async":false}},"ssrModuleMapping":{"[project]/node_modules/next/dist/client/components/layout-router.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/client/components/layout-router.js [app-ssr] (ecmascript)","name":"*","chunks":["server/chunks/ssr/node_modules_ce9aef9d._.js","server/chunks/ssr/[root of the server]__29912de3._.js"],"async":false}},"[project]/node_modules/next/dist/client/components/render-from-template-context.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/client/components/render-from-template-context.js [app-ssr] (ecmascript)","name":"*","chunks":["server/chunks/ssr/node_modules_ce9aef9d._.js","server/chunks/ssr/[root of the server]__29912de3._.js"],"async":false}},"[project]/node_modules/next/dist/client/components/client-page.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/client/components/client-page.js [app-ssr] (ecmascript)","name":"*","chunks":["server/chunks/ssr/node_modules_ce9aef9d._.js","server/chunks/ssr/[root of the server]__29912de3._.js"],"async":false}},"[project]/node_modules/next/dist/client/components/client-segment.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/client/components/client-segment.js [app-ssr] (ecmascript)","name":"*","chunks":["server/chunks/ssr/node_modules_ce9aef9d._.js","server/chunks/ssr/[root of the server]__29912de3._.js"],"async":false}},"[project]/node_modules/next/dist/client/components/http-access-fallback/error-boundary.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/client/components/http-access-fallback/error-boundary.js [app-ssr] (ecmascript)","name":"*","chunks":["server/chunks/ssr/node_modules_ce9aef9d._.js","server/chunks/ssr/[root of the server]__29912de3._.js"],"async":false}},"[project]/node_modules/next/dist/client/components/metadata/async-metadata.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/client/components/metadata/async-metadata.js [app-ssr] (ecmascript)","name":"*","chunks":["server/chunks/ssr/node_modules_ce9aef9d._.js","server/chunks/ssr/[root of the server]__29912de3._.js"],"async":false}},"[project]/node_modules/next/dist/client/components/error-boundary.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/client/components/error-boundary.js [app-ssr] (ecmascript)","name":"*","chunks":["server/chunks/ssr/node_modules_ce9aef9d._.js","server/chunks/ssr/[root of the server]__29912de3._.js"],"async":false}},"[project]/node_modules/next/dist/client/components/metadata/metadata-boundary.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/client/components/metadata/metadata-boundary.js [app-ssr] (ecmascript)","name":"*","chunks":["server/chunks/ssr/node_modules_ce9aef9d._.js","server/chunks/ssr/[root of the server]__29912de3._.js"],"async":false}},"[project]/src/components/ui/toaster.tsx [app-client] (ecmascript)":{"*":{"id":"[project]/src/components/ui/toaster.tsx [app-ssr] (ecmascript)","name":"*","chunks":["server/chunks/ssr/[root of the server]__7f6540fd._.js","server/chunks/ssr/node_modules_f5754146._.js"],"async":false}},"[project]/src/contexts/i18n-context.tsx [app-client] (ecmascript)":{"*":{"id":"[project]/src/contexts/i18n-context.tsx [app-ssr] (ecmascript)","name":"*","chunks":["server/chunks/ssr/[root of the server]__7f6540fd._.js","server/chunks/ssr/node_modules_f5754146._.js"],"async":false}},"[project]/src/contexts/theme-context.tsx [app-client] (ecmascript)":{"*":{"id":"[project]/src/contexts/theme-context.tsx [app-ssr] (ecmascript)","name":"*","chunks":["server/chunks/ssr/[root of the server]__7f6540fd._.js","server/chunks/ssr/node_modules_f5754146._.js"],"async":false}},"[project]/src/contexts/dictionary-context.tsx [app-client] (ecmascript)":{"*":{"id":"[project]/src/contexts/dictionary-context.tsx [app-ssr] (ecmascript)","name":"*","chunks":["server/chunks/ssr/[root of the server]__7f6540fd._.js","server/chunks/ssr/node_modules_f5754146._.js"],"async":false}},"[project]/src/contexts/feature-settings-context.tsx [app-client] (ecmascript)":{"*":{"id":"[project]/src/contexts/feature-settings-context.tsx [app-ssr] (ecmascript)","name":"*","chunks":["server/chunks/ssr/[root of the server]__7f6540fd._.js","server/chunks/ssr/node_modules_f5754146._.js"],"async":false}},"[project]/src/app/page.tsx [app-client] (ecmascript)":{"*":{"id":"[project]/src/app/page.tsx [app-ssr] (ecmascript)","name":"*","chunks":["server/chunks/ssr/[root of the server]__7f6540fd._.js","server/chunks/ssr/node_modules_f5754146._.js","server/chunks/ssr/node_modules_node-fetch_src_index_36b0f17e.js","server/chunks/ssr/[root of the server]__e2bc8fca._.js","server/chunks/ssr/node_modules_zod_lib_9026566a._.js","server/chunks/ssr/node_modules_@genkit-ai_core_lib_a48441cb._.js","server/chunks/ssr/node_modules_ajv_dist_205754b0._.js","server/chunks/ssr/node_modules_zod-to-json-schema_dist_3dc3b583._.js","server/chunks/ssr/node_modules_@opentelemetry_9012a738._.js","server/chunks/ssr/node_modules_@opentelemetry_ff81fcd0._.js","server/chunks/ssr/node_modules_@opentelemetry_5b702915._.js","server/chunks/ssr/node_modules_@opentelemetry_dd9b68ec._.js","server/chunks/ssr/node_modules_@opentelemetry_1a42652d._.js","server/chunks/ssr/node_modules_protobufjs_921afc6f._.js","server/chunks/ssr/node_modules_@opentelemetry_otlp-transformer_build_esm_cb80273e._.js","server/chunks/ssr/node_modules_@grpc_grpc-js_6bfe8f54._.js","server/chunks/ssr/node_modules_handlebars_2e0aef67._.js","server/chunks/ssr/node_modules_yaml_dist_a4de29c8._.js","server/chunks/ssr/node_modules_@genkit-ai_ai_lib_0311b89c._.js","server/chunks/ssr/node_modules_@radix-ui_79c951e4._.js","server/chunks/ssr/node_modules_@floating-ui_90d70670._.js","server/chunks/ssr/node_modules_@opentelemetry_37496377._.js","server/chunks/ssr/node_modules_82822ea5._.js"],"async":false}}},"edgeSSRModuleMapping":{},"rscModuleMapping":{"[project]/node_modules/next/dist/client/components/layout-router.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/client/components/layout-router.js (client reference/proxy)","name":"*","chunks":["server/app/page.js"],"async":false}},"[project]/node_modules/next/dist/client/components/render-from-template-context.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/client/components/render-from-template-context.js (client reference/proxy)","name":"*","chunks":["server/app/page.js"],"async":false}},"[project]/node_modules/next/dist/client/components/client-page.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/client/components/client-page.js (client reference/proxy)","name":"*","chunks":["server/app/page.js"],"async":false}},"[project]/node_modules/next/dist/client/components/client-segment.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/client/components/client-segment.js (client reference/proxy)","name":"*","chunks":["server/app/page.js"],"async":false}},"[project]/node_modules/next/dist/client/components/http-access-fallback/error-boundary.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/client/components/http-access-fallback/error-boundary.js (client reference/proxy)","name":"*","chunks":["server/app/page.js"],"async":false}},"[project]/node_modules/next/dist/client/components/metadata/async-metadata.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/client/components/metadata/async-metadata.js (client reference/proxy)","name":"*","chunks":["server/app/page.js"],"async":false}},"[project]/node_modules/next/dist/client/components/error-boundary.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/client/components/error-boundary.js (client reference/proxy)","name":"*","chunks":["server/app/page.js"],"async":false}},"[project]/node_modules/next/dist/client/components/metadata/metadata-boundary.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/client/components/metadata/metadata-boundary.js (client reference/proxy)","name":"*","chunks":["server/app/page.js"],"async":false}},"[project]/src/components/ui/toaster.tsx [app-client] (ecmascript)":{"*":{"id":"[project]/src/components/ui/toaster.tsx (client reference/proxy)","name":"*","chunks":["server/app/page.js"],"async":false}},"[project]/src/contexts/i18n-context.tsx [app-client] (ecmascript)":{"*":{"id":"[project]/src/contexts/i18n-context.tsx (client reference/proxy)","name":"*","chunks":["server/app/page.js"],"async":false}},"[project]/src/contexts/theme-context.tsx [app-client] (ecmascript)":{"*":{"id":"[project]/src/contexts/theme-context.tsx (client reference/proxy)","name":"*","chunks":["server/app/page.js"],"async":false}},"[project]/src/contexts/dictionary-context.tsx [app-client] (ecmascript)":{"*":{"id":"[project]/src/contexts/dictionary-context.tsx (client reference/proxy)","name":"*","chunks":["server/app/page.js"],"async":false}},"[project]/src/contexts/feature-settings-context.tsx [app-client] (ecmascript)":{"*":{"id":"[project]/src/contexts/feature-settings-context.tsx (client reference/proxy)","name":"*","chunks":["server/app/page.js"],"async":false}},"[project]/src/app/page.tsx [app-client] (ecmascript)":{"*":{"id":"[project]/src/app/page.tsx (client reference/proxy)","name":"*","chunks":["server/app/page.js"],"async":false}}},"edgeRscModuleMapping":{},"entryCSSFiles":{"[project]/src/app/favicon.ico":[],"[project]/src/app/layout":[{"path":"static/chunks/src_app_globals_b805903d.css","inlined":false}],"[project]/src/app/page":[{"path":"static/chunks/src_app_globals_b805903d.css","inlined":false}]},"entryJSFiles":{"[project]/src/app/favicon.ico":["static/chunks/node_modules_next_dist_1a6ee436._.js","static/chunks/src_app_favicon_ico_mjs_79b6a596._.js"],"[project]/src/app/layout":["static/chunks/src_23f492bb._.js","static/chunks/node_modules_a31e2e15._.js","static/chunks/src_app_layout_tsx_f0e4c1a2._.js"],"[project]/src/app/page":["static/chunks/src_23f492bb._.js","static/chunks/node_modules_a31e2e15._.js","static/chunks/src_app_layout_tsx_f0e4c1a2._.js","static/chunks/node_modules_node-fetch_src_index_fa16faaa.js","static/chunks/src_01424a98._.js","static/chunks/node_modules_next_dist_compiled_stream-http_index_4b0f5eaa.js","static/chunks/node_modules_next_dist_compiled_assert_assert_eb302145.js","static/chunks/node_modules_next_dist_compiled_browserify-zlib_index_65a3892f.js","static/chunks/node_modules_next_dist_compiled_crypto-browserify_index_07270ec0.js","static/chunks/node_modules_next_dist_compiled_5dde6f15._.js","static/chunks/0f475_dist_build_webpack_loaders_next-flight-loader_action-client-wrapper_b74250f7.js","static/chunks/node_modules_zod_lib_18e6bb38._.js","static/chunks/node_modules_@genkit-ai_core_lib_81c4cc2c._.js","static/chunks/node_modules_ajv_dist_250e4da2._.js","static/chunks/node_modules_zod-to-json-schema_dist_0744899c._.js","static/chunks/node_modules_@opentelemetry_api_build_esm_03c46853._.js","static/chunks/node_modules_@opentelemetry_ff6525d0._.js","static/chunks/node_modules_@opentelemetry_ab8c9ec2._.js","static/chunks/node_modules_@opentelemetry_0442fd05._.js","static/chunks/node_modules_@opentelemetry_f56d57f2._.js","static/chunks/node_modules_@opentelemetry_14494b79._.js","static/chunks/node_modules_protobufjs_8dbde028._.js","static/chunks/node_modules_@opentelemetry_otlp-transformer_build_esm_3416722d._.js","static/chunks/node_modules_@grpc_grpc-js_6ca6ef0a._.js","static/chunks/node_modules_handlebars_dist_cjs_20fecba4._.js","static/chunks/node_modules_yaml_browser_efb39508._.js","static/chunks/node_modules_iconv-lite_f639b7eb._.js","static/chunks/node_modules_mime-db_1172ad10._.js","static/chunks/node_modules_@genkit-ai_ai_lib_93078ce1._.js","static/chunks/node_modules_@radix-ui_cf4fda66._.js","static/chunks/node_modules_@floating-ui_9ec1fa39._.js","static/chunks/node_modules_@opentelemetry_22904df5._.js","static/chunks/node_modules_c63b261d._.js","static/chunks/src_app_page_tsx_e3d75067._.js"]}}
