self.__RSC_SERVER_MANIFEST="{\n  \"node\": {\n    \"405a5b769b4917cb26288a1cde0db7e6abfe4dec5d\": {\n      \"workers\": {\n        \"app/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/src/ai/flows/language-detection-flow.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/src/ai/flows/text-analysis-flow.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/src/ai/flows/ai-tone-analysis.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE3 => \\\"[project]/src/ai/flows/ai-text-generation.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE4 => \\\"[project]/src/ai/flows/contextual-ai-rephraser.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE5 => \\\"[project]/src/ai/flows/plagiarism-detection-flow.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE6 => \\\"[project]/src/ai/flows/ai-writing-detection-flow.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE7 => \\\"[project]/src/ai/flows/humanize-ai-text-flow.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE8 => \\\"[project]/src/ai/flows/word-toolkit-flow.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE9 => \\\"[project]/src/ai/flows/text-to-speech-flow.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/page\": \"action-browser\"\n      }\n    },\n    \"40702a1ad5de108082e6da297ca632028ddc3d08ab\": {\n      \"workers\": {\n        \"app/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/src/ai/flows/language-detection-flow.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/src/ai/flows/text-analysis-flow.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/src/ai/flows/ai-tone-analysis.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE3 => \\\"[project]/src/ai/flows/ai-text-generation.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE4 => \\\"[project]/src/ai/flows/contextual-ai-rephraser.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE5 => \\\"[project]/src/ai/flows/plagiarism-detection-flow.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE6 => \\\"[project]/src/ai/flows/ai-writing-detection-flow.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE7 => \\\"[project]/src/ai/flows/humanize-ai-text-flow.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE8 => \\\"[project]/src/ai/flows/word-toolkit-flow.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE9 => \\\"[project]/src/ai/flows/text-to-speech-flow.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/page\": \"action-browser\"\n      }\n    },\n    \"40ea03d104afda03838a2dbcbb2a732a52c54f460f\": {\n      \"workers\": {\n        \"app/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/src/ai/flows/language-detection-flow.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/src/ai/flows/text-analysis-flow.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/src/ai/flows/ai-tone-analysis.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE3 => \\\"[project]/src/ai/flows/ai-text-generation.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE4 => \\\"[project]/src/ai/flows/contextual-ai-rephraser.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE5 => \\\"[project]/src/ai/flows/plagiarism-detection-flow.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE6 => \\\"[project]/src/ai/flows/ai-writing-detection-flow.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE7 => \\\"[project]/src/ai/flows/humanize-ai-text-flow.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE8 => \\\"[project]/src/ai/flows/word-toolkit-flow.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE9 => \\\"[project]/src/ai/flows/text-to-speech-flow.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/page\": \"action-browser\"\n      }\n    },\n    \"400593e69d1504c1530b13506f8f47f5d106125416\": {\n      \"workers\": {\n        \"app/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/src/ai/flows/language-detection-flow.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/src/ai/flows/text-analysis-flow.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/src/ai/flows/ai-tone-analysis.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE3 => \\\"[project]/src/ai/flows/ai-text-generation.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE4 => \\\"[project]/src/ai/flows/contextual-ai-rephraser.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE5 => \\\"[project]/src/ai/flows/plagiarism-detection-flow.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE6 => \\\"[project]/src/ai/flows/ai-writing-detection-flow.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE7 => \\\"[project]/src/ai/flows/humanize-ai-text-flow.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE8 => \\\"[project]/src/ai/flows/word-toolkit-flow.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE9 => \\\"[project]/src/ai/flows/text-to-speech-flow.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/page\": \"action-browser\"\n      }\n    },\n    \"40d8e7b7b0e9fa76ebde132c211e3aa1d215479e24\": {\n      \"workers\": {\n        \"app/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/src/ai/flows/language-detection-flow.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/src/ai/flows/text-analysis-flow.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/src/ai/flows/ai-tone-analysis.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE3 => \\\"[project]/src/ai/flows/ai-text-generation.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE4 => \\\"[project]/src/ai/flows/contextual-ai-rephraser.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE5 => \\\"[project]/src/ai/flows/plagiarism-detection-flow.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE6 => \\\"[project]/src/ai/flows/ai-writing-detection-flow.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE7 => \\\"[project]/src/ai/flows/humanize-ai-text-flow.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE8 => \\\"[project]/src/ai/flows/word-toolkit-flow.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE9 => \\\"[project]/src/ai/flows/text-to-speech-flow.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/page\": \"action-browser\"\n      }\n    },\n    \"402ba50d11703164f75228045a1f07daefcb08a9d2\": {\n      \"workers\": {\n        \"app/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/src/ai/flows/language-detection-flow.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/src/ai/flows/text-analysis-flow.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/src/ai/flows/ai-tone-analysis.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE3 => \\\"[project]/src/ai/flows/ai-text-generation.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE4 => \\\"[project]/src/ai/flows/contextual-ai-rephraser.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE5 => \\\"[project]/src/ai/flows/plagiarism-detection-flow.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE6 => \\\"[project]/src/ai/flows/ai-writing-detection-flow.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE7 => \\\"[project]/src/ai/flows/humanize-ai-text-flow.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE8 => \\\"[project]/src/ai/flows/word-toolkit-flow.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE9 => \\\"[project]/src/ai/flows/text-to-speech-flow.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/page\": \"action-browser\"\n      }\n    },\n    \"403048f5f0b78d3b884448d5e85f159cbd190126d6\": {\n      \"workers\": {\n        \"app/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/src/ai/flows/language-detection-flow.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/src/ai/flows/text-analysis-flow.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/src/ai/flows/ai-tone-analysis.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE3 => \\\"[project]/src/ai/flows/ai-text-generation.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE4 => \\\"[project]/src/ai/flows/contextual-ai-rephraser.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE5 => \\\"[project]/src/ai/flows/plagiarism-detection-flow.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE6 => \\\"[project]/src/ai/flows/ai-writing-detection-flow.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE7 => \\\"[project]/src/ai/flows/humanize-ai-text-flow.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE8 => \\\"[project]/src/ai/flows/word-toolkit-flow.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE9 => \\\"[project]/src/ai/flows/text-to-speech-flow.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/page\": \"action-browser\"\n      }\n    },\n    \"400fbf9215415e85b2d17f813ed1ae2aeab513d87a\": {\n      \"workers\": {\n        \"app/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/src/ai/flows/language-detection-flow.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/src/ai/flows/text-analysis-flow.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/src/ai/flows/ai-tone-analysis.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE3 => \\\"[project]/src/ai/flows/ai-text-generation.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE4 => \\\"[project]/src/ai/flows/contextual-ai-rephraser.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE5 => \\\"[project]/src/ai/flows/plagiarism-detection-flow.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE6 => \\\"[project]/src/ai/flows/ai-writing-detection-flow.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE7 => \\\"[project]/src/ai/flows/humanize-ai-text-flow.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE8 => \\\"[project]/src/ai/flows/word-toolkit-flow.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE9 => \\\"[project]/src/ai/flows/text-to-speech-flow.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/page\": \"action-browser\"\n      }\n    },\n    \"40acaae4d04c7eb5976eb550148d96fcd001d283d8\": {\n      \"workers\": {\n        \"app/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/src/ai/flows/language-detection-flow.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/src/ai/flows/text-analysis-flow.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/src/ai/flows/ai-tone-analysis.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE3 => \\\"[project]/src/ai/flows/ai-text-generation.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE4 => \\\"[project]/src/ai/flows/contextual-ai-rephraser.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE5 => \\\"[project]/src/ai/flows/plagiarism-detection-flow.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE6 => \\\"[project]/src/ai/flows/ai-writing-detection-flow.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE7 => \\\"[project]/src/ai/flows/humanize-ai-text-flow.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE8 => \\\"[project]/src/ai/flows/word-toolkit-flow.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE9 => \\\"[project]/src/ai/flows/text-to-speech-flow.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/page\": \"action-browser\"\n      }\n    },\n    \"4097243ff0930e8336fed59e15d28136674ddfaa95\": {\n      \"workers\": {\n        \"app/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/src/ai/flows/language-detection-flow.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/src/ai/flows/text-analysis-flow.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/src/ai/flows/ai-tone-analysis.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE3 => \\\"[project]/src/ai/flows/ai-text-generation.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE4 => \\\"[project]/src/ai/flows/contextual-ai-rephraser.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE5 => \\\"[project]/src/ai/flows/plagiarism-detection-flow.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE6 => \\\"[project]/src/ai/flows/ai-writing-detection-flow.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE7 => \\\"[project]/src/ai/flows/humanize-ai-text-flow.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE8 => \\\"[project]/src/ai/flows/word-toolkit-flow.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE9 => \\\"[project]/src/ai/flows/text-to-speech-flow.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/page\": \"action-browser\"\n      }\n    }\n  },\n  \"edge\": {},\n  \"encryptionKey\": \"Vp1tTEnDLdgyeBqkRj91ZN+kg4fXaxoC+ZykFaY7kFU=\"\n}"