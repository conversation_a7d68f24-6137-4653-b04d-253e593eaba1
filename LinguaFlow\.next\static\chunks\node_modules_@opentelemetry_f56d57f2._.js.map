{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/node_modules/%40opentelemetry/sdk-node/node_modules/%40opentelemetry/sdk-metrics/src/index.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { MetricDescriptor } from './export/MetricData';\n\nexport {\n  Sum,\n  LastValue,\n  Histogram,\n  ExponentialHistogram,\n} from './aggregator/types';\n\nexport {\n  AggregationSelector,\n  AggregationTemporalitySelector,\n} from './export/AggregationSelector';\n\nexport { AggregationTemporality } from './export/AggregationTemporality';\n\nexport {\n  DataPoint,\n  DataPointType,\n  SumMetricData,\n  GaugeMetricData,\n  HistogramMetricData,\n  ExponentialHistogramMetricData,\n  ResourceMetrics,\n  ScopeMetrics,\n  MetricData,\n  MetricDescriptor,\n  CollectionResult,\n} from './export/MetricData';\n\nexport { PushMetricExporter } from './export/MetricExporter';\n\nexport { MetricReader, MetricReaderOptions } from './export/MetricReader';\n\nexport {\n  PeriodicExportingMetricReader,\n  PeriodicExportingMetricReaderOptions,\n} from './export/PeriodicExportingMetricReader';\n\nexport { InMemoryMetricExporter } from './export/InMemoryMetricExporter';\n\nexport { ConsoleMetricExporter } from './export/ConsoleMetricExporter';\n\nexport { MetricCollectOptions, MetricProducer } from './export/MetricProducer';\n\nexport { InstrumentType } from './InstrumentDescriptor';\n/**\n * @deprecated Use {@link MetricDescriptor} instead.\n */\nexport type InstrumentDescriptor = MetricDescriptor;\n\nexport { MeterProvider, MeterProviderOptions } from './MeterProvider';\n\nexport {\n  DefaultAggregation,\n  ExplicitBucketHistogramAggregation,\n  ExponentialHistogramAggregation,\n  DropAggregation,\n  HistogramAggregation,\n  LastValueAggregation,\n  SumAggregation,\n  Aggregation,\n} from './view/Aggregation';\n\nexport { View, ViewOptions } from './view/View';\n\nexport { TimeoutError } from './utils';\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 48, "column": 0}, "map": {"version": 3, "file": "AggregationTemporality.js", "sourceRoot": "", "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/node_modules/%40opentelemetry/sdk-node/node_modules/%40opentelemetry/sdk-metrics/src/export/AggregationTemporality.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n/**\n * AggregationTemporality indicates the way additive quantities are expressed.\n */\nexport enum AggregationTemporality {\n  DELTA,\n  CUMULATIVE,\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG,CAEH;;GAEG;;;AACH,IAAY,sBAGX;AAHD,CAAA,SAAY,sBAAsB;IAChC,sBAAA,CAAA,sBAAA,CAAA,QAAA,GAAA,EAAA,GAAA,OAAK,CAAA;IACL,sBAAA,CAAA,sBAAA,CAAA,aAAA,GAAA,EAAA,GAAA,YAAU,CAAA;AACZ,CAAC,EAHW,sBAAsB,IAAA,CAAtB,sBAAsB,GAAA,CAAA,CAAA,GAGjC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 78, "column": 0}, "map": {"version": 3, "file": "MetricData.js", "sourceRoot": "", "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/node_modules/%40opentelemetry/sdk-node/node_modules/%40opentelemetry/sdk-metrics/src/export/MetricData.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { HrTime, MetricAttributes, ValueType } from '@opentelemetry/api';\nimport { InstrumentationScope } from '@opentelemetry/core';\nimport { IResource } from '@opentelemetry/resources';\nimport { InstrumentType } from '../InstrumentDescriptor';\nimport { AggregationTemporality } from './AggregationTemporality';\nimport { Histogram, ExponentialHistogram } from '../aggregator/types';\n\nexport interface MetricDescriptor {\n  readonly name: string;\n  readonly description: string;\n  readonly unit: string;\n  /**\n   * @deprecated exporter should avoid depending on the type of the instrument\n   * as their resulting aggregator can be re-mapped with views.\n   */\n  readonly type: InstrumentType;\n  readonly valueType: ValueType;\n}\n\n/**\n * Basic metric data fields.\n */\ninterface BaseMetricData {\n  readonly descriptor: MetricDescriptor;\n  readonly aggregationTemporality: AggregationTemporality;\n  /**\n   * DataPointType of the metric instrument.\n   */\n  readonly dataPointType: DataPointType;\n}\n\n/**\n * Represents a metric data aggregated by either a LastValueAggregation or\n * SumAggregation.\n */\nexport interface SumMetricData extends BaseMetricData {\n  readonly dataPointType: DataPointType.SUM;\n  readonly dataPoints: DataPoint<number>[];\n  readonly isMonotonic: boolean;\n}\n\nexport interface GaugeMetricData extends BaseMetricData {\n  readonly dataPointType: DataPointType.GAUGE;\n  readonly dataPoints: DataPoint<number>[];\n}\n\n/**\n * Represents a metric data aggregated by a HistogramAggregation.\n */\nexport interface HistogramMetricData extends BaseMetricData {\n  readonly dataPointType: DataPointType.HISTOGRAM;\n  readonly dataPoints: DataPoint<Histogram>[];\n}\n\n/**\n * Represents a metric data aggregated by a ExponentialHistogramAggregation.\n */\nexport interface ExponentialHistogramMetricData extends BaseMetricData {\n  readonly dataPointType: DataPointType.EXPONENTIAL_HISTOGRAM;\n  readonly dataPoints: DataPoint<ExponentialHistogram>[];\n}\n\n/**\n * Represents an aggregated metric data.\n */\nexport type MetricData =\n  | SumMetricData\n  | GaugeMetricData\n  | HistogramMetricData\n  | ExponentialHistogramMetricData;\n\nexport interface ScopeMetrics {\n  scope: InstrumentationScope;\n  metrics: MetricData[];\n}\n\nexport interface ResourceMetrics {\n  resource: IResource;\n  scopeMetrics: ScopeMetrics[];\n}\n\n/**\n * Represents the collection result of the metrics. If there are any\n * non-critical errors in the collection, like throwing in a single observable\n * callback, these errors are aggregated in the {@link CollectionResult.errors}\n * array and other successfully collected metrics are returned.\n */\nexport interface CollectionResult {\n  /**\n   * Collected metrics.\n   */\n  resourceMetrics: ResourceMetrics;\n  /**\n   * Arbitrary JavaScript exception values.\n   */\n  errors: unknown[];\n}\n\n/**\n * The aggregated point data type.\n */\nexport enum DataPointType {\n  /**\n   * A histogram data point contains a histogram statistics of collected\n   * values with a list of explicit bucket boundaries and statistics such\n   * as min, max, count, and sum of all collected values.\n   */\n  HISTOGRAM,\n  /**\n   * An exponential histogram data point contains a histogram statistics of\n   * collected values where bucket boundaries are automatically calculated\n   * using an exponential function, and statistics such as min, max, count,\n   * and sum of all collected values.\n   */\n  EXPONENTIAL_HISTOGRAM,\n  /**\n   * A gauge metric data point has only a single numeric value.\n   */\n  GAUGE,\n  /**\n   * A sum metric data point has a single numeric value and a\n   * monotonicity-indicator.\n   */\n  SUM,\n}\n\n/**\n * Represents an aggregated point data with start time, end time and their\n * associated attributes and points.\n */\nexport interface DataPoint<T> {\n  /**\n   * The start epoch timestamp of the DataPoint, usually the time when\n   * the metric was created when the preferred AggregationTemporality is\n   * CUMULATIVE, or last collection time otherwise.\n   */\n  readonly startTime: HrTime;\n  /**\n   * The end epoch timestamp when data were collected, usually it represents\n   * the moment when `MetricReader.collect` was called.\n   */\n  readonly endTime: HrTime;\n  /**\n   * The attributes associated with this DataPoint.\n   */\n  readonly attributes: MetricAttributes;\n  /**\n   * The value for this DataPoint. The type of the value is indicated by the\n   * {@link DataPointType}.\n   */\n  readonly value: T;\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG,CAoGH;;GAEG;;;AACH,IAAY,aAuBX;AAvBD,CAAA,SAAY,aAAa;IACvB;;;;OAIG,CACH,aAAA,CAAA,aAAA,CAAA,YAAA,GAAA,EAAA,GAAA,WAAS,CAAA;IACT;;;;;OAKG,CACH,aAAA,CAAA,aAAA,CAAA,wBAAA,GAAA,EAAA,GAAA,uBAAqB,CAAA;IACrB;;OAEG,CACH,aAAA,CAAA,aAAA,CAAA,QAAA,GAAA,EAAA,GAAA,OAAK,CAAA;IACL;;;OAGG,CACH,aAAA,CAAA,aAAA,CAAA,MAAA,GAAA,EAAA,GAAA,KAAG,CAAA;AACL,CAAC,EAvBW,aAAa,IAAA,CAAb,aAAa,GAAA,CAAA,CAAA,GAuBxB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 124, "column": 0}, "map": {"version": 3, "file": "utils.js", "sourceRoot": "", "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/node_modules/%40opentelemetry/sdk-node/node_modules/%40opentelemetry/sdk-metrics/src/utils.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { MetricAttributes } from '@opentelemetry/api';\nimport { InstrumentationScope } from '@opentelemetry/core';\n\nexport type Maybe<T> = T | undefined;\n\nexport function isNotNullish<T>(item: Maybe<T>): item is T {\n  return item !== undefined && item !== null;\n}\n\n/**\n * Converting the unordered attributes into unique identifier string.\n * @param attributes user provided unordered MetricAttributes.\n */\nexport function hashAttributes(attributes: MetricAttributes): string {\n  let keys = Object.keys(attributes);\n  if (keys.length === 0) return '';\n\n  // Return a string that is stable on key orders.\n  keys = keys.sort();\n  return JSON.stringify(keys.map(key => [key, attributes[key]]));\n}\n\n/**\n * Converting the instrumentation scope object to a unique identifier string.\n * @param instrumentationScope\n */\nexport function instrumentationScopeId(\n  instrumentationScope: InstrumentationScope\n): string {\n  return `${instrumentationScope.name}:${instrumentationScope.version ?? ''}:${\n    instrumentationScope.schemaUrl ?? ''\n  }`;\n}\n\n/**\n * Error that is thrown on timeouts.\n */\nexport class TimeoutError extends Error {\n  constructor(message?: string) {\n    super(message);\n\n    // manually adjust prototype to retain `instanceof` functionality when targeting ES5, see:\n    // https://github.com/Microsoft/TypeScript-wiki/blob/main/Breaking-Changes.md#extending-built-ins-like-error-array-and-map-may-no-longer-work\n    Object.setPrototypeOf(this, TimeoutError.prototype);\n  }\n}\n\n/**\n * Adds a timeout to a promise and rejects if the specified timeout has elapsed. Also rejects if the specified promise\n * rejects, and resolves if the specified promise resolves.\n *\n * <p> NOTE: this operation will continue even after it throws a {@link TimeoutError}.\n *\n * @param promise promise to use with timeout.\n * @param timeout the timeout in milliseconds until the returned promise is rejected.\n */\nexport function callWithTimeout<T>(\n  promise: Promise<T>,\n  timeout: number\n): Promise<T> {\n  let timeoutHandle: ReturnType<typeof setTimeout>;\n\n  const timeoutPromise = new Promise<never>(function timeoutFunction(\n    _resolve,\n    reject\n  ) {\n    timeoutHandle = setTimeout(function timeoutHandler() {\n      reject(new TimeoutError('Operation timed out.'));\n    }, timeout);\n  });\n\n  return Promise.race([promise, timeoutPromise]).then(\n    result => {\n      clearTimeout(timeoutHandle);\n      return result;\n    },\n    reason => {\n      clearTimeout(timeoutHandle);\n      throw reason;\n    }\n  );\n}\n\nexport interface PromiseAllSettledFulfillResult<T> {\n  status: 'fulfilled';\n  value: T;\n}\n\nexport interface PromiseAllSettledRejectionResult {\n  status: 'rejected';\n  reason: unknown;\n}\n\nexport type PromiseAllSettledResult<T> =\n  | PromiseAllSettledFulfillResult<T>\n  | PromiseAllSettledRejectionResult;\n\n/**\n * Node.js v12.9 lower and browser compatible `Promise.allSettled`.\n */\nexport async function PromiseAllSettled<T>(\n  promises: Promise<T>[]\n): Promise<PromiseAllSettledResult<T>[]> {\n  return Promise.all(\n    promises.map<Promise<PromiseAllSettledResult<T>>>(async p => {\n      try {\n        const ret = await p;\n        return {\n          status: 'fulfilled',\n          value: ret,\n        };\n      } catch (e) {\n        return {\n          status: 'rejected',\n          reason: e,\n        };\n      }\n    })\n  );\n}\n\nexport function isPromiseAllSettledRejectionResult(\n  it: PromiseAllSettledResult<unknown>\n): it is PromiseAllSettledRejectionResult {\n  return it.status === 'rejected';\n}\n\n/**\n * Node.js v11.0 lower and browser compatible `Array.prototype.flatMap`.\n */\nexport function FlatMap<T, R>(arr: T[], fn: (it: T) => R[]): R[] {\n  const result: R[] = [];\n  arr.forEach(it => {\n    result.push(...fn(it));\n  });\n  return result;\n}\n\nexport function setEquals(lhs: Set<unknown>, rhs: Set<unknown>): boolean {\n  if (lhs.size !== rhs.size) {\n    return false;\n  }\n  for (const item of lhs) {\n    if (!rhs.has(item)) {\n      return false;\n    }\n  }\n  return true;\n}\n\n/**\n * Binary search the sorted array to the find lower bound for the value.\n * @param arr\n * @param value\n * @returns\n */\nexport function binarySearchLB(arr: number[], value: number): number {\n  let lo = 0;\n  let hi = arr.length - 1;\n\n  while (hi - lo > 1) {\n    const mid = Math.trunc((hi + lo) / 2);\n    if (arr[mid] <= value) {\n      lo = mid;\n    } else {\n      hi = mid - 1;\n    }\n  }\n\n  if (arr[hi] <= value) {\n    return hi;\n  } else if (arr[lo] <= value) {\n    return lo;\n  }\n  return -1;\n}\n\nexport function equalsCaseInsensitive(lhs: string, rhs: string): boolean {\n  return lhs.toLowerCase() === rhs.toLowerCase();\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOG,SAAU,YAAY,CAAI,IAAc;IAC5C,OAAO,IAAI,KAAK,SAAS,IAAI,IAAI,KAAK,IAAI,CAAC;AAC7C,CAAC;AAMK,SAAU,cAAc,CAAC,UAA4B;IACzD,IAAI,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;IACnC,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC,EAAE,OAAO,EAAE,CAAC;IAEjC,gDAAgD;IAChD,IAAI,GAAG,IAAI,CAAC,IAAI,EAAE,CAAC;IACnB,OAAO,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,GAAG,CAAC,SAAA,GAAG;QAAI,OAAA;YAAC,GAAG;YAAE,UAAU,CAAC,GAAG,CAAC;SAAC;IAAtB,CAAsB,CAAC,CAAC,CAAC;AACjE,CAAC;AAMK,SAAU,sBAAsB,CACpC,oBAA0C;;IAE1C,OAAU,oBAAoB,CAAC,IAAI,GAAA,MAAA,CAAI,CAAA,KAAA,oBAAoB,CAAC,OAAO,MAAA,QAAA,OAAA,KAAA,IAAA,KAAI,EAAE,IAAA,MAAA,CACvE,CAAA,KAAA,oBAAoB,CAAC,SAAS,MAAA,QAAA,OAAA,KAAA,IAAA,KAAI,EAAE,CACpC,CAAC;AACL,CAAC;AAED;;GAEG,CACH,IAAA,eAAA,SAAA,MAAA;IAAkC,UAAA,cAAA,QAAK;IACrC,SAAA,aAAY,OAAgB;QAA5B,IAAA,QACE,OAAA,IAAA,CAAA,IAAA,EAAM,OAAO,CAAC,IAAA,IAAA,CAKf;QAHC,0FAA0F;QAC1F,6IAA6I;QAC7I,MAAM,CAAC,cAAc,CAAC,KAAI,EAAE,YAAY,CAAC,SAAS,CAAC,CAAC;;IACtD,CAAC;IACH,OAAA,YAAC;AAAD,CAAC,AARD,CAAkC,KAAK,GAQtC;;AAWK,SAAU,eAAe,CAC7B,OAAmB,EACnB,OAAe;IAEf,IAAI,aAA4C,CAAC;IAEjD,IAAM,cAAc,GAAG,IAAI,OAAO,CAAQ,SAAS,eAAe,CAChE,QAAQ,EACR,MAAM;QAEN,aAAa,GAAG,UAAU,CAAC,SAAS,cAAc;YAChD,MAAM,CAAC,IAAI,YAAY,CAAC,sBAAsB,CAAC,CAAC,CAAC;QACnD,CAAC,EAAE,OAAO,CAAC,CAAC;IACd,CAAC,CAAC,CAAC;IAEH,OAAO,OAAO,CAAC,IAAI,CAAC;QAAC,OAAO;QAAE,cAAc;KAAC,CAAC,CAAC,IAAI,CACjD,SAAA,MAAM;QACJ,YAAY,CAAC,aAAa,CAAC,CAAC;QAC5B,OAAO,MAAM,CAAC;IAChB,CAAC,EACD,SAAA,MAAM;QACJ,YAAY,CAAC,aAAa,CAAC,CAAC;QAC5B,MAAM,MAAM,CAAC;IACf,CAAC,CACF,CAAC;AACJ,CAAC;AAmBK,SAAgB,iBAAiB,CACrC,QAAsB;;;;YAEtB,OAAA;gBAAA,EAAA,QAAA;gBAAO,OAAO,CAAC,GAAG,CAChB,QAAQ,CAAC,GAAG,CAAsC,SAAM,CAAC;oBAAA,OAAA,UAAA,OAAA,KAAA,GAAA,KAAA,GAAA;;;;;;;;;;;oCAEzC,OAAA;wCAAA,EAAA,OAAA;wCAAM,CAAC;qCAAA,CAAA;;oCAAb,GAAG,GAAG,GAAA,IAAA,EAAO;oCACnB,OAAA;wCAAA,EAAA,QAAA;wCAAO;4CACL,MAAM,EAAE,WAAW;4CACnB,KAAK,EAAE,GAAG;yCACX;qCAAA,CAAC;;;oCAEF,OAAA;wCAAA,EAAA,QAAA;wCAAO;4CACL,MAAM,EAAE,UAAU;4CAClB,MAAM,EAAE,GAAC;yCACV;qCAAA,CAAC;;;;;;;;iBAEL,CAAC,CACH;aAAA,CAAC;;;CACH;AAEK,SAAU,kCAAkC,CAChD,EAAoC;IAEpC,OAAO,EAAE,CAAC,MAAM,KAAK,UAAU,CAAC;AAClC,CAAC;AAKK,SAAU,OAAO,CAAO,GAAQ,EAAE,EAAkB;IACxD,IAAM,MAAM,GAAQ,EAAE,CAAC;IACvB,GAAG,CAAC,OAAO,CAAC,SAAA,EAAE;QACZ,MAAM,CAAC,IAAI,CAAA,KAAA,CAAX,MAAM,EAAA,cAAA,EAAA,EAAA,OAAS,EAAE,CAAC,EAAE,CAAC,GAAA,QAAE;IACzB,CAAC,CAAC,CAAC;IACH,OAAO,MAAM,CAAC;AAChB,CAAC;AAEK,SAAU,SAAS,CAAC,GAAiB,EAAE,GAAiB;;IAC5D,IAAI,GAAG,CAAC,IAAI,KAAK,GAAG,CAAC,IAAI,EAAE;QACzB,OAAO,KAAK,CAAC;KACd;;QACD,IAAmB,IAAA,QAAA,SAAA,GAAG,CAAA,EAAA,UAAA,MAAA,IAAA,EAAA,EAAA,CAAA,QAAA,IAAA,EAAA,UAAA,MAAA,IAAA,GAAE;YAAnB,IAAM,IAAI,GAAA,QAAA,KAAA;YACb,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE;gBAClB,OAAO,KAAK,CAAC;aACd;SACF;;;;;;;;;;;;IACD,OAAO,IAAI,CAAC;AACd,CAAC;AAQK,SAAU,cAAc,CAAC,GAAa,EAAE,KAAa;IACzD,IAAI,EAAE,GAAG,CAAC,CAAC;IACX,IAAI,EAAE,GAAG,GAAG,CAAC,MAAM,GAAG,CAAC,CAAC;IAExB,MAAO,EAAE,GAAG,EAAE,GAAG,CAAC,CAAE;QAClB,IAAM,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC;QACtC,IAAI,GAAG,CAAC,GAAG,CAAC,IAAI,KAAK,EAAE;YACrB,EAAE,GAAG,GAAG,CAAC;SACV,MAAM;YACL,EAAE,GAAG,GAAG,GAAG,CAAC,CAAC;SACd;KACF;IAED,IAAI,GAAG,CAAC,EAAE,CAAC,IAAI,KAAK,EAAE;QACpB,OAAO,EAAE,CAAC;KACX,MAAM,IAAI,GAAG,CAAC,EAAE,CAAC,IAAI,KAAK,EAAE;QAC3B,OAAO,EAAE,CAAC;KACX;IACD,OAAO,CAAC,CAAC,CAAC;AACZ,CAAC;AAEK,SAAU,qBAAqB,CAAC,GAAW,EAAE,GAAW;IAC5D,OAAO,GAAG,CAAC,WAAW,EAAE,KAAK,GAAG,CAAC,WAAW,EAAE,CAAC;AACjD,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 501, "column": 0}, "map": {"version": 3, "file": "types.js", "sourceRoot": "", "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/node_modules/%40opentelemetry/sdk-node/node_modules/%40opentelemetry/sdk-metrics/src/aggregator/types.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { HrTime, MetricAttributes } from '@opentelemetry/api';\nimport { AggregationTemporality } from '../export/AggregationTemporality';\nimport { MetricData, MetricDescriptor } from '../export/MetricData';\nimport { Maybe } from '../utils';\n\n/** The kind of aggregator. */\nexport enum AggregatorKind {\n  DROP,\n  SUM,\n  LAST_VALUE,\n  HISTOGRAM,\n  EXPONENTIAL_HISTOGRAM,\n}\n\n/** DataPoint value type for SumAggregation. */\nexport type Sum = number;\n\n/** DataPoint value type for LastValueAggregation. */\nexport type LastValue = number;\n\n/** DataPoint value type for HistogramAggregation. */\nexport interface Histogram {\n  /**\n   * Buckets are implemented using two different arrays:\n   *  - boundaries: contains every finite bucket boundary, which are inclusive lower bounds\n   *  - counts: contains event counts for each bucket\n   *\n   * Note that we'll always have n+1 buckets, where n is the number of boundaries.\n   * This is because we need to count events that are below the lowest boundary.\n   *\n   * Example: if we measure the values: [5, 30, 5, 40, 5, 15, 15, 15, 25]\n   *  with the boundaries [ 10, 20, 30 ], we will have the following state:\n   *\n   * buckets: {\n   *\tboundaries: [10, 20, 30],\n   *\tcounts: [3, 3, 1, 2],\n   * }\n   */\n  buckets: {\n    boundaries: number[];\n    counts: number[];\n  };\n  sum?: number;\n  count: number;\n  min?: number;\n  max?: number;\n}\n\n/** DataPoint value type for ExponentialHistogramAggregation. */\nexport interface ExponentialHistogram {\n  count: number;\n  sum?: number;\n  scale: number;\n  zeroCount: number;\n  positive: {\n    offset: number;\n    bucketCounts: number[];\n  };\n  negative: {\n    offset: number;\n    bucketCounts: number[];\n  };\n  min?: number;\n  max?: number;\n}\n\n/**\n * An Aggregator accumulation state.\n */\nexport interface Accumulation {\n  setStartTime(startTime: HrTime): void;\n  record(value: number): void;\n}\n\nexport type AccumulationRecord<T> = [MetricAttributes, T];\n\n/**\n * Base interface for aggregators. Aggregators are responsible for holding\n * aggregated values and taking a snapshot of these values upon export.\n */\nexport interface Aggregator<T> {\n  /** The kind of the aggregator. */\n  kind: AggregatorKind;\n\n  /**\n   * Create a clean state of accumulation.\n   */\n  createAccumulation(startTime: HrTime): T;\n\n  /**\n   * Returns the result of the merge of the given accumulations.\n   *\n   * This should always assume that the accumulations do not overlap and merge together for a new\n   * cumulative report.\n   *\n   * @param previous the previously captured accumulation\n   * @param delta the newly captured (delta) accumulation\n   * @returns the result of the merge of the given accumulations\n   */\n  merge(previous: T, delta: T): T;\n\n  /**\n   * Returns a new DELTA aggregation by comparing two cumulative measurements.\n   *\n   * @param previous the previously captured accumulation\n   * @param current the newly captured (cumulative) accumulation\n   * @returns The resulting delta accumulation\n   */\n  diff(previous: T, current: T): T;\n\n  /**\n   * Returns the {@link MetricData} that this {@link Aggregator} will produce.\n   *\n   * @param descriptor the metric descriptor.\n   * @param aggregationTemporality the temporality of the resulting {@link MetricData}\n   * @param accumulationByAttributes the array of attributes and accumulation pairs.\n   * @param endTime the end time of the metric data.\n   * @return the {@link MetricData} that this {@link Aggregator} will produce.\n   */\n  toMetricData(\n    descriptor: MetricDescriptor,\n    aggregationTemporality: AggregationTemporality,\n    accumulationByAttributes: AccumulationRecord<T>[],\n    endTime: HrTime\n  ): Maybe<MetricData>;\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG,CAOH,4BAAA,EAA8B;;;AAC9B,IAAY,cAMX;AAND,CAAA,SAAY,cAAc;IACxB,cAAA,CAAA,cAAA,CAAA,OAAA,GAAA,EAAA,GAAA,MAAI,CAAA;IACJ,cAAA,CAAA,cAAA,CAAA,MAAA,GAAA,EAAA,GAAA,KAAG,CAAA;IACH,cAAA,CAAA,cAAA,CAAA,aAAA,GAAA,EAAA,GAAA,YAAU,CAAA;IACV,cAAA,CAAA,cAAA,CAAA,YAAA,GAAA,EAAA,GAAA,WAAS,CAAA;IACT,cAAA,CAAA,cAAA,CAAA,wBAAA,GAAA,EAAA,GAAA,uBAAqB,CAAA;AACvB,CAAC,EANW,cAAc,IAAA,CAAd,cAAc,GAAA,CAAA,CAAA,GAMzB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 532, "column": 0}, "map": {"version": 3, "file": "Sum.js", "sourceRoot": "", "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/node_modules/%40opentelemetry/sdk-node/node_modules/%40opentelemetry/sdk-metrics/src/aggregator/Sum.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  Sum,\n  AggregatorKind,\n  Aggregator,\n  Accumulation,\n  AccumulationRecord,\n} from './types';\nimport { HrTime } from '@opentelemetry/api';\nimport {\n  DataPointType,\n  MetricDescriptor,\n  SumMetricData,\n} from '../export/MetricData';\nimport { Maybe } from '../utils';\nimport { AggregationTemporality } from '../export/AggregationTemporality';\n\nexport class SumAccumulation implements Accumulation {\n  constructor(\n    public startTime: HrTime,\n    public monotonic: boolean,\n    private _current: number = 0,\n    public reset = false\n  ) {}\n\n  record(value: number): void {\n    if (this.monotonic && value < 0) {\n      return;\n    }\n    this._current += value;\n  }\n\n  setStartTime(startTime: HrTime): void {\n    this.startTime = startTime;\n  }\n\n  toPointValue(): Sum {\n    return this._current;\n  }\n}\n\n/** Basic aggregator which calculates a Sum from individual measurements. */\nexport class SumAggregator implements Aggregator<SumAccumulation> {\n  public kind: AggregatorKind.SUM = AggregatorKind.SUM;\n\n  constructor(public monotonic: boolean) {}\n\n  createAccumulation(startTime: HrTime) {\n    return new SumAccumulation(startTime, this.monotonic);\n  }\n\n  /**\n   * Returns the result of the merge of the given accumulations.\n   */\n  merge(previous: SumAccumulation, delta: SumAccumulation): SumAccumulation {\n    const prevPv = previous.toPointValue();\n    const deltaPv = delta.toPointValue();\n    if (delta.reset) {\n      return new SumAccumulation(\n        delta.startTime,\n        this.monotonic,\n        deltaPv,\n        delta.reset\n      );\n    }\n    return new SumAccumulation(\n      previous.startTime,\n      this.monotonic,\n      prevPv + deltaPv\n    );\n  }\n\n  /**\n   * Returns a new DELTA aggregation by comparing two cumulative measurements.\n   */\n  diff(previous: SumAccumulation, current: SumAccumulation): SumAccumulation {\n    const prevPv = previous.toPointValue();\n    const currPv = current.toPointValue();\n    /**\n     * If the SumAggregator is a monotonic one and the previous point value is\n     * greater than the current one, a reset is deemed to be happened.\n     * Return the current point value to prevent the value from been reset.\n     */\n    if (this.monotonic && prevPv > currPv) {\n      return new SumAccumulation(\n        current.startTime,\n        this.monotonic,\n        currPv,\n        true\n      );\n    }\n    return new SumAccumulation(\n      current.startTime,\n      this.monotonic,\n      currPv - prevPv\n    );\n  }\n\n  toMetricData(\n    descriptor: MetricDescriptor,\n    aggregationTemporality: AggregationTemporality,\n    accumulationByAttributes: AccumulationRecord<SumAccumulation>[],\n    endTime: HrTime\n  ): Maybe<SumMetricData> {\n    return {\n      descriptor,\n      aggregationTemporality,\n      dataPointType: DataPointType.SUM,\n      dataPoints: accumulationByAttributes.map(([attributes, accumulation]) => {\n        return {\n          attributes,\n          startTime: accumulation.startTime,\n          endTime,\n          value: accumulation.toPointValue(),\n        };\n      }),\n      isMonotonic: this.monotonic,\n    };\n  }\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;;AAEH,OAAO,EAEL,cAAc,GAIf,MAAM,SAAS,CAAC;AAEjB,OAAO,EACL,aAAa,GAGd,MAAM,sBAAsB,CAAC;;;;;;;;;;;;;;;;;;;;;;AAI9B,IAAA,kBAAA;IACE,SAAA,gBACS,SAAiB,EACjB,SAAkB,EACjB,QAAoB,EACrB,KAAa;QADZ,IAAA,aAAA,KAAA,GAAA;YAAA,WAAA,CAAoB;QAAA;QACrB,IAAA,UAAA,KAAA,GAAA;YAAA,QAAA,KAAa;QAAA;QAHb,IAAA,CAAA,SAAS,GAAT,SAAS,CAAQ;QACjB,IAAA,CAAA,SAAS,GAAT,SAAS,CAAS;QACjB,IAAA,CAAA,QAAQ,GAAR,QAAQ,CAAY;QACrB,IAAA,CAAA,KAAK,GAAL,KAAK,CAAQ;IACnB,CAAC;IAEJ,gBAAA,SAAA,CAAA,MAAM,GAAN,SAAO,KAAa;QAClB,IAAI,IAAI,CAAC,SAAS,IAAI,KAAK,GAAG,CAAC,EAAE;YAC/B,OAAO;SACR;QACD,IAAI,CAAC,QAAQ,IAAI,KAAK,CAAC;IACzB,CAAC;IAED,gBAAA,SAAA,CAAA,YAAY,GAAZ,SAAa,SAAiB;QAC5B,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;IAC7B,CAAC;IAED,gBAAA,SAAA,CAAA,YAAY,GAAZ;QACE,OAAO,IAAI,CAAC,QAAQ,CAAC;IACvB,CAAC;IACH,OAAA,eAAC;AAAD,CAAC,AAtBD,IAsBC;;AAED,0EAAA,EAA4E,CAC5E,IAAA,gBAAA;IAGE,SAAA,cAAmB,SAAkB;QAAlB,IAAA,CAAA,SAAS,GAAT,SAAS,CAAS;QAF9B,IAAA,CAAA,IAAI,iPAAuB,iBAAc,CAAC,GAAG,CAAC;IAEb,CAAC;IAEzC,cAAA,SAAA,CAAA,kBAAkB,GAAlB,SAAmB,SAAiB;QAClC,OAAO,IAAI,eAAe,CAAC,SAAS,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC;IACxD,CAAC;IAED;;OAEG,CACH,cAAA,SAAA,CAAA,KAAK,GAAL,SAAM,QAAyB,EAAE,KAAsB;QACrD,IAAM,MAAM,GAAG,QAAQ,CAAC,YAAY,EAAE,CAAC;QACvC,IAAM,OAAO,GAAG,KAAK,CAAC,YAAY,EAAE,CAAC;QACrC,IAAI,KAAK,CAAC,KAAK,EAAE;YACf,OAAO,IAAI,eAAe,CACxB,KAAK,CAAC,SAAS,EACf,IAAI,CAAC,SAAS,EACd,OAAO,EACP,KAAK,CAAC,KAAK,CACZ,CAAC;SACH;QACD,OAAO,IAAI,eAAe,CACxB,QAAQ,CAAC,SAAS,EAClB,IAAI,CAAC,SAAS,EACd,MAAM,GAAG,OAAO,CACjB,CAAC;IACJ,CAAC;IAED;;OAEG,CACH,cAAA,SAAA,CAAA,IAAI,GAAJ,SAAK,QAAyB,EAAE,OAAwB;QACtD,IAAM,MAAM,GAAG,QAAQ,CAAC,YAAY,EAAE,CAAC;QACvC,IAAM,MAAM,GAAG,OAAO,CAAC,YAAY,EAAE,CAAC;QACtC;;;;WAIG,CACH,IAAI,IAAI,CAAC,SAAS,IAAI,MAAM,GAAG,MAAM,EAAE;YACrC,OAAO,IAAI,eAAe,CACxB,OAAO,CAAC,SAAS,EACjB,IAAI,CAAC,SAAS,EACd,MAAM,EACN,IAAI,CACL,CAAC;SACH;QACD,OAAO,IAAI,eAAe,CACxB,OAAO,CAAC,SAAS,EACjB,IAAI,CAAC,SAAS,EACd,MAAM,GAAG,MAAM,CAChB,CAAC;IACJ,CAAC;IAED,cAAA,SAAA,CAAA,YAAY,GAAZ,SACE,UAA4B,EAC5B,sBAA8C,EAC9C,wBAA+D,EAC/D,OAAe;QAEf,OAAO;YACL,UAAU,EAAA,UAAA;YACV,sBAAsB,EAAA,sBAAA;YACtB,aAAa,iPAAE,gBAAa,CAAC,GAAG;YAChC,UAAU,EAAE,wBAAwB,CAAC,GAAG,CAAC,SAAC,EAA0B;oBAA1B,KAAA,OAAA,IAAA,EAA0B,EAAzB,UAAU,GAAA,EAAA,CAAA,EAAA,EAAE,YAAY,GAAA,EAAA,CAAA,EAAA;gBACjE,OAAO;oBACL,UAAU,EAAA,UAAA;oBACV,SAAS,EAAE,YAAY,CAAC,SAAS;oBACjC,OAAO,EAAA,OAAA;oBACP,KAAK,EAAE,YAAY,CAAC,YAAY,EAAE;iBACnC,CAAC;YACJ,CAAC,CAAC;YACF,WAAW,EAAE,IAAI,CAAC,SAAS;SAC5B,CAAC;IACJ,CAAC;IACH,OAAA,aAAC;AAAD,CAAC,AA7ED,IA6EC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 660, "column": 0}, "map": {"version": 3, "file": "Drop.js", "sourceRoot": "", "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/node_modules/%40opentelemetry/sdk-node/node_modules/%40opentelemetry/sdk-metrics/src/aggregator/Drop.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { HrTime } from '@opentelemetry/api';\nimport { AggregationTemporality } from '../export/AggregationTemporality';\nimport { MetricData, MetricDescriptor } from '../export/MetricData';\nimport { Maybe } from '../utils';\nimport { AggregatorKind, Aggregator, AccumulationRecord } from './types';\n\n/** Basic aggregator for None which keeps no recorded value. */\nexport class DropAggregator implements Aggregator<undefined> {\n  kind: AggregatorKind.DROP = AggregatorKind.DROP;\n\n  createAccumulation() {\n    return undefined;\n  }\n\n  merge(_previous: undefined, _delta: undefined) {\n    return undefined;\n  }\n\n  diff(_previous: undefined, _current: undefined) {\n    return undefined;\n  }\n\n  toMetricData(\n    _descriptor: MetricDescriptor,\n    _aggregationTemporality: AggregationTemporality,\n    _accumulationByAttributes: AccumulationRecord<undefined>[],\n    _endTime: HrTime\n  ): Maybe<MetricData> {\n    return undefined;\n  }\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;AAMH,OAAO,EAAE,cAAc,EAAkC,MAAM,SAAS,CAAC;;AAEzE,6DAAA,EAA+D,CAC/D,IAAA,iBAAA;IAAA,SAAA;QACE,IAAA,CAAA,IAAI,iPAAwB,iBAAc,CAAC,IAAI,CAAC;IAsBlD,CAAC;IApBC,eAAA,SAAA,CAAA,kBAAkB,GAAlB;QACE,OAAO,SAAS,CAAC;IACnB,CAAC;IAED,eAAA,SAAA,CAAA,KAAK,GAAL,SAAM,SAAoB,EAAE,MAAiB;QAC3C,OAAO,SAAS,CAAC;IACnB,CAAC;IAED,eAAA,SAAA,CAAA,IAAI,GAAJ,SAAK,SAAoB,EAAE,QAAmB;QAC5C,OAAO,SAAS,CAAC;IACnB,CAAC;IAED,eAAA,SAAA,CAAA,YAAY,GAAZ,SACE,WAA6B,EAC7B,uBAA+C,EAC/C,yBAA0D,EAC1D,QAAgB;QAEhB,OAAO,SAAS,CAAC;IACnB,CAAC;IACH,OAAA,cAAC;AAAD,CAAC,AAvBD,IAuBC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 705, "column": 0}, "map": {"version": 3, "file": "LastValue.js", "sourceRoot": "", "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/node_modules/%40opentelemetry/sdk-node/node_modules/%40opentelemetry/sdk-metrics/src/aggregator/LastValue.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  Accumulation,\n  AccumulationRecord,\n  Aggregator,\n  AggregatorKind,\n  LastValue,\n} from './types';\nimport { HrTime } from '@opentelemetry/api';\nimport { millisToHrTime, hrTimeToMicroseconds } from '@opentelemetry/core';\nimport {\n  DataPointType,\n  GaugeMetricData,\n  MetricDescriptor,\n} from '../export/MetricData';\nimport { Maybe } from '../utils';\nimport { AggregationTemporality } from '../export/AggregationTemporality';\n\nexport class LastValueAccumulation implements Accumulation {\n  constructor(\n    public startTime: HrTime,\n    private _current: number = 0,\n    public sampleTime: HrTime = [0, 0]\n  ) {}\n\n  record(value: number): void {\n    this._current = value;\n    this.sampleTime = millisToHrTime(Date.now());\n  }\n\n  setStartTime(startTime: HrTime): void {\n    this.startTime = startTime;\n  }\n\n  toPointValue(): LastValue {\n    return this._current;\n  }\n}\n\n/** Basic aggregator which calculates a LastValue from individual measurements. */\nexport class LastValueAggregator implements Aggregator<LastValueAccumulation> {\n  public kind: AggregatorKind.LAST_VALUE = AggregatorKind.LAST_VALUE;\n\n  createAccumulation(startTime: HrTime) {\n    return new LastValueAccumulation(startTime);\n  }\n\n  /**\n   * Returns the result of the merge of the given accumulations.\n   *\n   * Return the newly captured (delta) accumulation for LastValueAggregator.\n   */\n  merge(\n    previous: LastValueAccumulation,\n    delta: LastValueAccumulation\n  ): LastValueAccumulation {\n    // nanoseconds may lose precisions.\n    const latestAccumulation =\n      hrTimeToMicroseconds(delta.sampleTime) >=\n      hrTimeToMicroseconds(previous.sampleTime)\n        ? delta\n        : previous;\n    return new LastValueAccumulation(\n      previous.startTime,\n      latestAccumulation.toPointValue(),\n      latestAccumulation.sampleTime\n    );\n  }\n\n  /**\n   * Returns a new DELTA aggregation by comparing two cumulative measurements.\n   *\n   * A delta aggregation is not meaningful to LastValueAggregator, just return\n   * the newly captured (delta) accumulation for LastValueAggregator.\n   */\n  diff(\n    previous: LastValueAccumulation,\n    current: LastValueAccumulation\n  ): LastValueAccumulation {\n    // nanoseconds may lose precisions.\n    const latestAccumulation =\n      hrTimeToMicroseconds(current.sampleTime) >=\n      hrTimeToMicroseconds(previous.sampleTime)\n        ? current\n        : previous;\n    return new LastValueAccumulation(\n      current.startTime,\n      latestAccumulation.toPointValue(),\n      latestAccumulation.sampleTime\n    );\n  }\n\n  toMetricData(\n    descriptor: MetricDescriptor,\n    aggregationTemporality: AggregationTemporality,\n    accumulationByAttributes: AccumulationRecord<LastValueAccumulation>[],\n    endTime: HrTime\n  ): Maybe<GaugeMetricData> {\n    return {\n      descriptor,\n      aggregationTemporality,\n      dataPointType: DataPointType.GAUGE,\n      dataPoints: accumulationByAttributes.map(([attributes, accumulation]) => {\n        return {\n          attributes,\n          startTime: accumulation.startTime,\n          endTime,\n          value: accumulation.toPointValue(),\n        };\n      }),\n    };\n  }\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;;AAEH,OAAO,EAIL,cAAc,GAEf,MAAM,SAAS,CAAC;AAEjB,OAAO,EAAE,cAAc,EAAE,oBAAoB,EAAE,MAAM,qBAAqB,CAAC;AAC3E,OAAO,EACL,aAAa,GAGd,MAAM,sBAAsB,CAAC;;;;;;;;;;;;;;;;;;;;;;;AAI9B,IAAA,wBAAA;IACE,SAAA,sBACS,SAAiB,EAChB,QAAoB,EACrB,UAA2B;QAD1B,IAAA,aAAA,KAAA,GAAA;YAAA,WAAA,CAAoB;QAAA;QACrB,IAAA,eAAA,KAAA,GAAA;YAAA,aAAA;gBAAsB,CAAC;gBAAE,CAAC;aAAC;QAAA;QAF3B,IAAA,CAAA,SAAS,GAAT,SAAS,CAAQ;QAChB,IAAA,CAAA,QAAQ,GAAR,QAAQ,CAAY;QACrB,IAAA,CAAA,UAAU,GAAV,UAAU,CAAiB;IACjC,CAAC;IAEJ,sBAAA,SAAA,CAAA,MAAM,GAAN,SAAO,KAAa;QAClB,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC;QACtB,IAAI,CAAC,UAAU,sOAAG,iBAAA,AAAc,EAAC,IAAI,CAAC,GAAG,EAAE,CAAC,CAAC;IAC/C,CAAC;IAED,sBAAA,SAAA,CAAA,YAAY,GAAZ,SAAa,SAAiB;QAC5B,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;IAC7B,CAAC;IAED,sBAAA,SAAA,CAAA,YAAY,GAAZ;QACE,OAAO,IAAI,CAAC,QAAQ,CAAC;IACvB,CAAC;IACH,OAAA,qBAAC;AAAD,CAAC,AAnBD,IAmBC;;AAED,gFAAA,EAAkF,CAClF,IAAA,sBAAA;IAAA,SAAA;QACS,IAAA,CAAA,IAAI,iPAA8B,iBAAc,CAAC,UAAU,CAAC;IAuErE,CAAC;IArEC,oBAAA,SAAA,CAAA,kBAAkB,GAAlB,SAAmB,SAAiB;QAClC,OAAO,IAAI,qBAAqB,CAAC,SAAS,CAAC,CAAC;IAC9C,CAAC;IAED;;;;OAIG,CACH,oBAAA,SAAA,CAAA,KAAK,GAAL,SACE,QAA+B,EAC/B,KAA4B;QAE5B,mCAAmC;QACnC,IAAM,kBAAkB,sOACtB,uBAAA,AAAoB,EAAC,KAAK,CAAC,UAAU,CAAC,uOACtC,uBAAA,AAAoB,EAAC,QAAQ,CAAC,UAAU,CAAC,GACrC,KAAK,GACL,QAAQ,CAAC;QACf,OAAO,IAAI,qBAAqB,CAC9B,QAAQ,CAAC,SAAS,EAClB,kBAAkB,CAAC,YAAY,EAAE,EACjC,kBAAkB,CAAC,UAAU,CAC9B,CAAC;IACJ,CAAC;IAED;;;;;OAKG,CACH,oBAAA,SAAA,CAAA,IAAI,GAAJ,SACE,QAA+B,EAC/B,OAA8B;QAE9B,mCAAmC;QACnC,IAAM,kBAAkB,sOACtB,uBAAA,AAAoB,EAAC,OAAO,CAAC,UAAU,CAAC,uOACxC,uBAAA,AAAoB,EAAC,QAAQ,CAAC,UAAU,CAAC,GACrC,OAAO,GACP,QAAQ,CAAC;QACf,OAAO,IAAI,qBAAqB,CAC9B,OAAO,CAAC,SAAS,EACjB,kBAAkB,CAAC,YAAY,EAAE,EACjC,kBAAkB,CAAC,UAAU,CAC9B,CAAC;IACJ,CAAC;IAED,oBAAA,SAAA,CAAA,YAAY,GAAZ,SACE,UAA4B,EAC5B,sBAA8C,EAC9C,wBAAqE,EACrE,OAAe;QAEf,OAAO;YACL,UAAU,EAAA,UAAA;YACV,sBAAsB,EAAA,sBAAA;YACtB,aAAa,iPAAE,gBAAa,CAAC,KAAK;YAClC,UAAU,EAAE,wBAAwB,CAAC,GAAG,CAAC,SAAC,EAA0B;oBAA1B,KAAA,OAAA,IAAA,EAA0B,EAAzB,UAAU,GAAA,EAAA,CAAA,EAAA,EAAE,YAAY,GAAA,EAAA,CAAA,EAAA;gBACjE,OAAO;oBACL,UAAU,EAAA,UAAA;oBACV,SAAS,EAAE,YAAY,CAAC,SAAS;oBACjC,OAAO,EAAA,OAAA;oBACP,KAAK,EAAE,YAAY,CAAC,YAAY,EAAE;iBACnC,CAAC;YACJ,CAAC,CAAC;SACH,CAAC;IACJ,CAAC;IACH,OAAA,mBAAC;AAAD,CAAC,AAxED,IAwEC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 828, "column": 0}, "map": {"version": 3, "file": "InstrumentDescriptor.js", "sourceRoot": "", "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/node_modules/%40opentelemetry/sdk-node/node_modules/%40opentelemetry/sdk-metrics/src/InstrumentDescriptor.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { MetricOptions, ValueType, diag } from '@opentelemetry/api';\nimport { View } from './view/View';\nimport { equalsCaseInsensitive } from './utils';\n\n/**\n * Supported types of metric instruments.\n */\nexport enum InstrumentType {\n  COUNTER = 'COUNTER',\n  GAUGE = 'GAUGE',\n  HISTOGRAM = 'HISTOGRAM',\n  UP_DOWN_COUNTER = 'UP_DOWN_COUNTER',\n  OBSERVABLE_COUNTER = 'OBSERVABLE_COUNTER',\n  OBSERVABLE_GAUGE = 'OBSERVABLE_GAUGE',\n  OBSERVABLE_UP_DOWN_COUNTER = 'OBSERVABLE_UP_DOWN_COUNTER',\n}\n\n/**\n * An internal interface describing the instrument.\n *\n * This is intentionally distinguished from the public MetricDescriptor (a.k.a. InstrumentDescriptor)\n * which may not contains internal fields like metric advice.\n */\nexport interface InstrumentDescriptor {\n  readonly name: string;\n  readonly description: string;\n  readonly unit: string;\n  readonly type: InstrumentType;\n  readonly valueType: ValueType;\n  /**\n   * @experimental\n   *\n   * This is intentionally not using the API's type as it's only available from @opentelemetry/api 1.7.0 and up.\n   * In SDK 2.0 we'll be able to bump the minimum API version and remove this workaround.\n   */\n  readonly advice: {\n    /**\n     * Hint the explicit bucket boundaries for SDK if the metric has been\n     * aggregated with a HistogramAggregator.\n     */\n    explicitBucketBoundaries?: number[];\n  };\n}\n\nexport function createInstrumentDescriptor(\n  name: string,\n  type: InstrumentType,\n  options?: MetricOptions\n): InstrumentDescriptor {\n  if (!isValidName(name)) {\n    diag.warn(\n      `Invalid metric name: \"${name}\". The metric name should be a ASCII string with a length no greater than 255 characters.`\n    );\n  }\n  return {\n    name,\n    type,\n    description: options?.description ?? '',\n    unit: options?.unit ?? '',\n    valueType: options?.valueType ?? ValueType.DOUBLE,\n    advice: options?.advice ?? {},\n  };\n}\n\nexport function createInstrumentDescriptorWithView(\n  view: View,\n  instrument: InstrumentDescriptor\n): InstrumentDescriptor {\n  return {\n    name: view.name ?? instrument.name,\n    description: view.description ?? instrument.description,\n    type: instrument.type,\n    unit: instrument.unit,\n    valueType: instrument.valueType,\n    advice: instrument.advice,\n  };\n}\n\nexport function isDescriptorCompatibleWith(\n  descriptor: InstrumentDescriptor,\n  otherDescriptor: InstrumentDescriptor\n) {\n  // Names are case-insensitive strings.\n  return (\n    equalsCaseInsensitive(descriptor.name, otherDescriptor.name) &&\n    descriptor.unit === otherDescriptor.unit &&\n    descriptor.type === otherDescriptor.type &&\n    descriptor.valueType === otherDescriptor.valueType\n  );\n}\n\n// ASCII string with a length no greater than 255 characters.\n// NB: the first character counted separately from the rest.\nconst NAME_REGEXP = /^[a-z][a-z0-9_.\\-/]{0,254}$/i;\nexport function isValidName(name: string): boolean {\n  return name.match(NAME_REGEXP) != null;\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;;;;;AAEH,OAAO,EAAiB,SAAS,EAAE,IAAI,EAAE,MAAM,oBAAoB,CAAC;;AAEpE,OAAO,EAAE,qBAAqB,EAAE,MAAM,SAAS,CAAC;;;AAKhD,IAAY,cAQX;AARD,CAAA,SAAY,cAAc;IACxB,cAAA,CAAA,UAAA,GAAA,SAAmB,CAAA;IACnB,cAAA,CAAA,QAAA,GAAA,OAAe,CAAA;IACf,cAAA,CAAA,YAAA,GAAA,WAAuB,CAAA;IACvB,cAAA,CAAA,kBAAA,GAAA,iBAAmC,CAAA;IACnC,cAAA,CAAA,qBAAA,GAAA,oBAAyC,CAAA;IACzC,cAAA,CAAA,mBAAA,GAAA,kBAAqC,CAAA;IACrC,cAAA,CAAA,6BAAA,GAAA,4BAAyD,CAAA;AAC3D,CAAC,EARW,cAAc,IAAA,CAAd,cAAc,GAAA,CAAA,CAAA,GAQzB;AA6BK,SAAU,0BAA0B,CACxC,IAAY,EACZ,IAAoB,EACpB,OAAuB;;IAEvB,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,EAAE;iLACtB,OAAI,CAAC,IAAI,CACP,4BAAyB,IAAI,GAAA,4FAA2F,CACzH,CAAC;KACH;IACD,OAAO;QACL,IAAI,EAAA,IAAA;QACJ,IAAI,EAAA,IAAA;QACJ,WAAW,EAAE,CAAA,KAAA,OAAO,KAAA,QAAP,OAAO,KAAA,KAAA,IAAA,KAAA,IAAP,OAAO,CAAE,WAAW,MAAA,QAAA,OAAA,KAAA,IAAA,KAAI,EAAE;QACvC,IAAI,EAAE,CAAA,KAAA,OAAO,KAAA,QAAP,OAAO,KAAA,KAAA,IAAA,KAAA,IAAP,OAAO,CAAE,IAAI,MAAA,QAAA,OAAA,KAAA,IAAA,KAAI,EAAE;QACzB,SAAS,EAAE,CAAA,KAAA,OAAO,KAAA,QAAP,OAAO,KAAA,KAAA,IAAA,KAAA,IAAP,OAAO,CAAE,SAAS,MAAA,QAAA,OAAA,KAAA,IAAA,oLAAI,YAAS,CAAC,MAAM;QACjD,MAAM,EAAE,CAAA,KAAA,OAAO,KAAA,QAAP,OAAO,KAAA,KAAA,IAAA,KAAA,IAAP,OAAO,CAAE,MAAM,MAAA,QAAA,OAAA,KAAA,IAAA,KAAI,CAAA,CAAE;KAC9B,CAAC;AACJ,CAAC;AAEK,SAAU,kCAAkC,CAChD,IAAU,EACV,UAAgC;;IAEhC,OAAO;QACL,IAAI,EAAE,CAAA,KAAA,IAAI,CAAC,IAAI,MAAA,QAAA,OAAA,KAAA,IAAA,KAAI,UAAU,CAAC,IAAI;QAClC,WAAW,EAAE,CAAA,KAAA,IAAI,CAAC,WAAW,MAAA,QAAA,OAAA,KAAA,IAAA,KAAI,UAAU,CAAC,WAAW;QACvD,IAAI,EAAE,UAAU,CAAC,IAAI;QACrB,IAAI,EAAE,UAAU,CAAC,IAAI;QACrB,SAAS,EAAE,UAAU,CAAC,SAAS;QAC/B,MAAM,EAAE,UAAU,CAAC,MAAM;KAC1B,CAAC;AACJ,CAAC;AAEK,SAAU,0BAA0B,CACxC,UAAgC,EAChC,eAAqC;IAErC,sCAAsC;IACtC,OAAO,oOACL,wBAAA,AAAqB,EAAC,UAAU,CAAC,IAAI,EAAE,eAAe,CAAC,IAAI,CAAC,IAC5D,UAAU,CAAC,IAAI,KAAK,eAAe,CAAC,IAAI,IACxC,UAAU,CAAC,IAAI,KAAK,eAAe,CAAC,IAAI,IACxC,UAAU,CAAC,SAAS,KAAK,eAAe,CAAC,SAAS,CACnD,CAAC;AACJ,CAAC;AAED,6DAA6D;AAC7D,4DAA4D;AAC5D,IAAM,WAAW,GAAG,8BAA8B,CAAC;AAC7C,SAAU,WAAW,CAAC,IAAY;IACtC,OAAO,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,IAAI,IAAI,CAAC;AACzC,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 905, "column": 0}, "map": {"version": 3, "file": "Histogram.js", "sourceRoot": "", "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/node_modules/%40opentelemetry/sdk-node/node_modules/%40opentelemetry/sdk-metrics/src/aggregator/Histogram.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  Accumulation,\n  AccumulationRecord,\n  Aggregator,\n  AggregatorKind,\n} from './types';\nimport {\n  DataPointType,\n  HistogramMetricData,\n  MetricDescriptor,\n} from '../export/MetricData';\nimport { HrTime } from '@opentelemetry/api';\nimport { InstrumentType } from '../InstrumentDescriptor';\nimport { binarySearchLB, Maybe } from '../utils';\nimport { AggregationTemporality } from '../export/AggregationTemporality';\n\n/**\n * Internal value type for HistogramAggregation.\n * Differs from the exported type as undefined sum/min/max complicate arithmetic\n * performed by this aggregation, but are required to be undefined in the exported types.\n */\ninterface InternalHistogram {\n  buckets: {\n    boundaries: number[];\n    counts: number[];\n  };\n  sum: number;\n  count: number;\n  hasMinMax: boolean;\n  min: number;\n  max: number;\n}\n\nfunction createNewEmptyCheckpoint(boundaries: number[]): InternalHistogram {\n  const counts = boundaries.map(() => 0);\n  counts.push(0);\n  return {\n    buckets: {\n      boundaries,\n      counts,\n    },\n    sum: 0,\n    count: 0,\n    hasMinMax: false,\n    min: Infinity,\n    max: -Infinity,\n  };\n}\n\nexport class HistogramAccumulation implements Accumulation {\n  constructor(\n    public startTime: HrTime,\n    private readonly _boundaries: number[],\n    private _recordMinMax = true,\n    private _current: InternalHistogram = createNewEmptyCheckpoint(_boundaries)\n  ) {}\n\n  record(value: number): void {\n    // NaN does not fall into any bucket, is not zero and should not be counted,\n    // NaN is never greater than max nor less than min, therefore return as there's nothing for us to do.\n    if (Number.isNaN(value)) {\n      return;\n    }\n\n    this._current.count += 1;\n    this._current.sum += value;\n\n    if (this._recordMinMax) {\n      this._current.min = Math.min(value, this._current.min);\n      this._current.max = Math.max(value, this._current.max);\n      this._current.hasMinMax = true;\n    }\n\n    const idx = binarySearchLB(this._boundaries, value);\n    this._current.buckets.counts[idx + 1] += 1;\n  }\n\n  setStartTime(startTime: HrTime): void {\n    this.startTime = startTime;\n  }\n\n  toPointValue(): InternalHistogram {\n    return this._current;\n  }\n}\n\n/**\n * Basic aggregator which observes events and counts them in pre-defined buckets\n * and provides the total sum and count of all observations.\n */\nexport class HistogramAggregator implements Aggregator<HistogramAccumulation> {\n  public kind: AggregatorKind.HISTOGRAM = AggregatorKind.HISTOGRAM;\n\n  /**\n   * @param _boundaries sorted upper bounds of recorded values.\n   * @param _recordMinMax If set to true, min and max will be recorded. Otherwise, min and max will not be recorded.\n   */\n  constructor(\n    private readonly _boundaries: number[],\n    private readonly _recordMinMax: boolean\n  ) {}\n\n  createAccumulation(startTime: HrTime) {\n    return new HistogramAccumulation(\n      startTime,\n      this._boundaries,\n      this._recordMinMax\n    );\n  }\n\n  /**\n   * Return the result of the merge of two histogram accumulations. As long as one Aggregator\n   * instance produces all Accumulations with constant boundaries we don't need to worry about\n   * merging accumulations with different boundaries.\n   */\n  merge(\n    previous: HistogramAccumulation,\n    delta: HistogramAccumulation\n  ): HistogramAccumulation {\n    const previousValue = previous.toPointValue();\n    const deltaValue = delta.toPointValue();\n\n    const previousCounts = previousValue.buckets.counts;\n    const deltaCounts = deltaValue.buckets.counts;\n\n    const mergedCounts = new Array(previousCounts.length);\n    for (let idx = 0; idx < previousCounts.length; idx++) {\n      mergedCounts[idx] = previousCounts[idx] + deltaCounts[idx];\n    }\n\n    let min = Infinity;\n    let max = -Infinity;\n\n    if (this._recordMinMax) {\n      if (previousValue.hasMinMax && deltaValue.hasMinMax) {\n        min = Math.min(previousValue.min, deltaValue.min);\n        max = Math.max(previousValue.max, deltaValue.max);\n      } else if (previousValue.hasMinMax) {\n        min = previousValue.min;\n        max = previousValue.max;\n      } else if (deltaValue.hasMinMax) {\n        min = deltaValue.min;\n        max = deltaValue.max;\n      }\n    }\n\n    return new HistogramAccumulation(\n      previous.startTime,\n      previousValue.buckets.boundaries,\n      this._recordMinMax,\n      {\n        buckets: {\n          boundaries: previousValue.buckets.boundaries,\n          counts: mergedCounts,\n        },\n        count: previousValue.count + deltaValue.count,\n        sum: previousValue.sum + deltaValue.sum,\n        hasMinMax:\n          this._recordMinMax &&\n          (previousValue.hasMinMax || deltaValue.hasMinMax),\n        min: min,\n        max: max,\n      }\n    );\n  }\n\n  /**\n   * Returns a new DELTA aggregation by comparing two cumulative measurements.\n   */\n  diff(\n    previous: HistogramAccumulation,\n    current: HistogramAccumulation\n  ): HistogramAccumulation {\n    const previousValue = previous.toPointValue();\n    const currentValue = current.toPointValue();\n\n    const previousCounts = previousValue.buckets.counts;\n    const currentCounts = currentValue.buckets.counts;\n\n    const diffedCounts = new Array(previousCounts.length);\n    for (let idx = 0; idx < previousCounts.length; idx++) {\n      diffedCounts[idx] = currentCounts[idx] - previousCounts[idx];\n    }\n\n    return new HistogramAccumulation(\n      current.startTime,\n      previousValue.buckets.boundaries,\n      this._recordMinMax,\n      {\n        buckets: {\n          boundaries: previousValue.buckets.boundaries,\n          counts: diffedCounts,\n        },\n        count: currentValue.count - previousValue.count,\n        sum: currentValue.sum - previousValue.sum,\n        hasMinMax: false,\n        min: Infinity,\n        max: -Infinity,\n      }\n    );\n  }\n\n  toMetricData(\n    descriptor: MetricDescriptor,\n    aggregationTemporality: AggregationTemporality,\n    accumulationByAttributes: AccumulationRecord<HistogramAccumulation>[],\n    endTime: HrTime\n  ): Maybe<HistogramMetricData> {\n    return {\n      descriptor,\n      aggregationTemporality,\n      dataPointType: DataPointType.HISTOGRAM,\n      dataPoints: accumulationByAttributes.map(([attributes, accumulation]) => {\n        const pointValue = accumulation.toPointValue();\n\n        // determine if instrument allows negative values.\n        const allowsNegativeValues =\n          descriptor.type === InstrumentType.GAUGE ||\n          descriptor.type === InstrumentType.UP_DOWN_COUNTER ||\n          descriptor.type === InstrumentType.OBSERVABLE_GAUGE ||\n          descriptor.type === InstrumentType.OBSERVABLE_UP_DOWN_COUNTER;\n\n        return {\n          attributes,\n          startTime: accumulation.startTime,\n          endTime,\n          value: {\n            min: pointValue.hasMinMax ? pointValue.min : undefined,\n            max: pointValue.hasMinMax ? pointValue.max : undefined,\n            sum: !allowsNegativeValues ? pointValue.sum : undefined,\n            buckets: pointValue.buckets,\n            count: pointValue.count,\n          },\n        };\n      }),\n    };\n  }\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;;AAEH,OAAO,EAIL,cAAc,GACf,MAAM,SAAS,CAAC;AACjB,OAAO,EACL,aAAa,GAGd,MAAM,sBAAsB,CAAC;AAE9B,OAAO,EAAE,cAAc,EAAE,MAAM,yBAAyB,CAAC;AACzD,OAAO,EAAE,cAAc,EAAS,MAAM,UAAU,CAAC;;;;;;;;;;;;;;;;;;;;;;;;AAoBjD,SAAS,wBAAwB,CAAC,UAAoB;IACpD,IAAM,MAAM,GAAG,UAAU,CAAC,GAAG,CAAC;QAAM,OAAA,CAAC;IAAD,CAAC,CAAC,CAAC;IACvC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;IACf,OAAO;QACL,OAAO,EAAE;YACP,UAAU,EAAA,UAAA;YACV,MAAM,EAAA,MAAA;SACP;QACD,GAAG,EAAE,CAAC;QACN,KAAK,EAAE,CAAC;QACR,SAAS,EAAE,KAAK;QAChB,GAAG,EAAE,QAAQ;QACb,GAAG,EAAE,CAAC,QAAQ;KACf,CAAC;AACJ,CAAC;AAED,IAAA,wBAAA;IACE,SAAA,sBACS,SAAiB,EACP,WAAqB,EAC9B,aAAoB,EACpB,QAAmE;QADnE,IAAA,kBAAA,KAAA,GAAA;YAAA,gBAAA,IAAoB;QAAA;QACpB,IAAA,aAAA,KAAA,GAAA;YAAA,WAA8B,wBAAwB,CAAC,WAAW,CAAC;QAAA;QAHpE,IAAA,CAAA,SAAS,GAAT,SAAS,CAAQ;QACP,IAAA,CAAA,WAAW,GAAX,WAAW,CAAU;QAC9B,IAAA,CAAA,aAAa,GAAb,aAAa,CAAO;QACpB,IAAA,CAAA,QAAQ,GAAR,QAAQ,CAA2D;IAC1E,CAAC;IAEJ,sBAAA,SAAA,CAAA,MAAM,GAAN,SAAO,KAAa;QAClB,4EAA4E;QAC5E,qGAAqG;QACrG,IAAI,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE;YACvB,OAAO;SACR;QAED,IAAI,CAAC,QAAQ,CAAC,KAAK,IAAI,CAAC,CAAC;QACzB,IAAI,CAAC,QAAQ,CAAC,GAAG,IAAI,KAAK,CAAC;QAE3B,IAAI,IAAI,CAAC,aAAa,EAAE;YACtB,IAAI,CAAC,QAAQ,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,EAAE,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;YACvD,IAAI,CAAC,QAAQ,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,EAAE,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;YACvD,IAAI,CAAC,QAAQ,CAAC,SAAS,GAAG,IAAI,CAAC;SAChC;QAED,IAAM,GAAG,uOAAG,iBAAA,AAAc,EAAC,IAAI,CAAC,WAAW,EAAE,KAAK,CAAC,CAAC;QACpD,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,MAAM,CAAC,GAAG,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC;IAC7C,CAAC;IAED,sBAAA,SAAA,CAAA,YAAY,GAAZ,SAAa,SAAiB;QAC5B,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;IAC7B,CAAC;IAED,sBAAA,SAAA,CAAA,YAAY,GAAZ;QACE,OAAO,IAAI,CAAC,QAAQ,CAAC;IACvB,CAAC;IACH,OAAA,qBAAC;AAAD,CAAC,AAnCD,IAmCC;;AAED;;;GAGG,CACH,IAAA,sBAAA;IAGE;;;OAGG,CACH,SAAA,oBACmB,WAAqB,EACrB,aAAsB;QADtB,IAAA,CAAA,WAAW,GAAX,WAAW,CAAU;QACrB,IAAA,CAAA,aAAa,GAAb,aAAa,CAAS;QARlC,IAAA,CAAA,IAAI,iPAA6B,iBAAc,CAAC,SAAS,CAAC;IAS9D,CAAC;IAEJ,oBAAA,SAAA,CAAA,kBAAkB,GAAlB,SAAmB,SAAiB;QAClC,OAAO,IAAI,qBAAqB,CAC9B,SAAS,EACT,IAAI,CAAC,WAAW,EAChB,IAAI,CAAC,aAAa,CACnB,CAAC;IACJ,CAAC;IAED;;;;OAIG,CACH,oBAAA,SAAA,CAAA,KAAK,GAAL,SACE,QAA+B,EAC/B,KAA4B;QAE5B,IAAM,aAAa,GAAG,QAAQ,CAAC,YAAY,EAAE,CAAC;QAC9C,IAAM,UAAU,GAAG,KAAK,CAAC,YAAY,EAAE,CAAC;QAExC,IAAM,cAAc,GAAG,aAAa,CAAC,OAAO,CAAC,MAAM,CAAC;QACpD,IAAM,WAAW,GAAG,UAAU,CAAC,OAAO,CAAC,MAAM,CAAC;QAE9C,IAAM,YAAY,GAAG,IAAI,KAAK,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC;QACtD,IAAK,IAAI,GAAG,GAAG,CAAC,EAAE,GAAG,GAAG,cAAc,CAAC,MAAM,EAAE,GAAG,EAAE,CAAE;YACpD,YAAY,CAAC,GAAG,CAAC,GAAG,cAAc,CAAC,GAAG,CAAC,GAAG,WAAW,CAAC,GAAG,CAAC,CAAC;SAC5D;QAED,IAAI,GAAG,GAAG,QAAQ,CAAC;QACnB,IAAI,GAAG,GAAG,CAAC,QAAQ,CAAC;QAEpB,IAAI,IAAI,CAAC,aAAa,EAAE;YACtB,IAAI,aAAa,CAAC,SAAS,IAAI,UAAU,CAAC,SAAS,EAAE;gBACnD,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,aAAa,CAAC,GAAG,EAAE,UAAU,CAAC,GAAG,CAAC,CAAC;gBAClD,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,aAAa,CAAC,GAAG,EAAE,UAAU,CAAC,GAAG,CAAC,CAAC;aACnD,MAAM,IAAI,aAAa,CAAC,SAAS,EAAE;gBAClC,GAAG,GAAG,aAAa,CAAC,GAAG,CAAC;gBACxB,GAAG,GAAG,aAAa,CAAC,GAAG,CAAC;aACzB,MAAM,IAAI,UAAU,CAAC,SAAS,EAAE;gBAC/B,GAAG,GAAG,UAAU,CAAC,GAAG,CAAC;gBACrB,GAAG,GAAG,UAAU,CAAC,GAAG,CAAC;aACtB;SACF;QAED,OAAO,IAAI,qBAAqB,CAC9B,QAAQ,CAAC,SAAS,EAClB,aAAa,CAAC,OAAO,CAAC,UAAU,EAChC,IAAI,CAAC,aAAa,EAClB;YACE,OAAO,EAAE;gBACP,UAAU,EAAE,aAAa,CAAC,OAAO,CAAC,UAAU;gBAC5C,MAAM,EAAE,YAAY;aACrB;YACD,KAAK,EAAE,aAAa,CAAC,KAAK,GAAG,UAAU,CAAC,KAAK;YAC7C,GAAG,EAAE,aAAa,CAAC,GAAG,GAAG,UAAU,CAAC,GAAG;YACvC,SAAS,EACP,IAAI,CAAC,aAAa,IAClB,CAAC,aAAa,CAAC,SAAS,IAAI,UAAU,CAAC,SAAS,CAAC;YACnD,GAAG,EAAE,GAAG;YACR,GAAG,EAAE,GAAG;SACT,CACF,CAAC;IACJ,CAAC;IAED;;OAEG,CACH,oBAAA,SAAA,CAAA,IAAI,GAAJ,SACE,QAA+B,EAC/B,OAA8B;QAE9B,IAAM,aAAa,GAAG,QAAQ,CAAC,YAAY,EAAE,CAAC;QAC9C,IAAM,YAAY,GAAG,OAAO,CAAC,YAAY,EAAE,CAAC;QAE5C,IAAM,cAAc,GAAG,aAAa,CAAC,OAAO,CAAC,MAAM,CAAC;QACpD,IAAM,aAAa,GAAG,YAAY,CAAC,OAAO,CAAC,MAAM,CAAC;QAElD,IAAM,YAAY,GAAG,IAAI,KAAK,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC;QACtD,IAAK,IAAI,GAAG,GAAG,CAAC,EAAE,GAAG,GAAG,cAAc,CAAC,MAAM,EAAE,GAAG,EAAE,CAAE;YACpD,YAAY,CAAC,GAAG,CAAC,GAAG,aAAa,CAAC,GAAG,CAAC,GAAG,cAAc,CAAC,GAAG,CAAC,CAAC;SAC9D;QAED,OAAO,IAAI,qBAAqB,CAC9B,OAAO,CAAC,SAAS,EACjB,aAAa,CAAC,OAAO,CAAC,UAAU,EAChC,IAAI,CAAC,aAAa,EAClB;YACE,OAAO,EAAE;gBACP,UAAU,EAAE,aAAa,CAAC,OAAO,CAAC,UAAU;gBAC5C,MAAM,EAAE,YAAY;aACrB;YACD,KAAK,EAAE,YAAY,CAAC,KAAK,GAAG,aAAa,CAAC,KAAK;YAC/C,GAAG,EAAE,YAAY,CAAC,GAAG,GAAG,aAAa,CAAC,GAAG;YACzC,SAAS,EAAE,KAAK;YAChB,GAAG,EAAE,QAAQ;YACb,GAAG,EAAE,CAAC,QAAQ;SACf,CACF,CAAC;IACJ,CAAC;IAED,oBAAA,SAAA,CAAA,YAAY,GAAZ,SACE,UAA4B,EAC5B,sBAA8C,EAC9C,wBAAqE,EACrE,OAAe;QAEf,OAAO;YACL,UAAU,EAAA,UAAA;YACV,sBAAsB,EAAA,sBAAA;YACtB,aAAa,iPAAE,gBAAa,CAAC,SAAS;YACtC,UAAU,EAAE,wBAAwB,CAAC,GAAG,CAAC,SAAC,EAA0B;oBAA1B,KAAA,OAAA,IAAA,EAA0B,EAAzB,UAAU,GAAA,EAAA,CAAA,EAAA,EAAE,YAAY,GAAA,EAAA,CAAA,EAAA;gBACjE,IAAM,UAAU,GAAG,YAAY,CAAC,YAAY,EAAE,CAAC;gBAE/C,kDAAkD;gBAClD,IAAM,oBAAoB,GACxB,UAAU,CAAC,IAAI,oPAAK,iBAAc,CAAC,KAAK,IACxC,UAAU,CAAC,IAAI,KAAK,gQAAc,CAAC,eAAe,IAClD,UAAU,CAAC,IAAI,oPAAK,iBAAc,CAAC,gBAAgB,IACnD,UAAU,CAAC,IAAI,oPAAK,iBAAc,CAAC,0BAA0B,CAAC;gBAEhE,OAAO;oBACL,UAAU,EAAA,UAAA;oBACV,SAAS,EAAE,YAAY,CAAC,SAAS;oBACjC,OAAO,EAAA,OAAA;oBACP,KAAK,EAAE;wBACL,GAAG,EAAE,UAAU,CAAC,SAAS,CAAC,CAAC,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC,SAAS;wBACtD,GAAG,EAAE,UAAU,CAAC,SAAS,CAAC,CAAC,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC,SAAS;wBACtD,GAAG,EAAE,CAAC,oBAAoB,CAAC,CAAC,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC,SAAS;wBACvD,OAAO,EAAE,UAAU,CAAC,OAAO;wBAC3B,KAAK,EAAE,UAAU,CAAC,KAAK;qBACxB;iBACF,CAAC;YACJ,CAAC,CAAC;SACH,CAAC;IACJ,CAAC;IACH,OAAA,mBAAC;AAAD,CAAC,AAnJD,IAmJC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1117, "column": 0}, "map": {"version": 3, "file": "Buckets.js", "sourceRoot": "", "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/node_modules/%40opentelemetry/sdk-node/node_modules/%40opentelemetry/sdk-metrics/src/aggregator/exponential-histogram/Buckets.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nexport class Buckets {\n  /**\n   * The term index refers to the number of the exponential histogram bucket\n   * used to determine its boundaries. The lower boundary of a bucket is\n   * determined by base ** index and the upper boundary of a bucket is\n   * determined by base ** (index + 1). index values are signed to account\n   * for values less than or equal to 1.\n   *\n   * indexBase is the index of the 0th position in the\n   * backing array, i.e., backing[0] is the count\n   * in the bucket with index `indexBase`.\n   *\n   * indexStart is the smallest index value represented\n   * in the backing array.\n   *\n   * indexEnd is the largest index value represented in\n   * the backing array.\n   */\n  constructor(\n    public backing = new BucketsBacking(),\n    public indexBase = 0,\n    public indexStart = 0,\n    public indexEnd = 0\n  ) {}\n\n  /**\n   * Offset is the bucket index of the smallest entry in the counts array\n   * @returns {number}\n   */\n  get offset(): number {\n    return this.indexStart;\n  }\n\n  /**\n   * Buckets is a view into the backing array.\n   * @returns {number}\n   */\n  get length(): number {\n    if (this.backing.length === 0) {\n      return 0;\n    }\n\n    if (this.indexEnd === this.indexStart && this.at(0) === 0) {\n      return 0;\n    }\n\n    return this.indexEnd - this.indexStart + 1;\n  }\n\n  /**\n   * An array of counts, where count[i] carries the count\n   * of the bucket at index (offset+i).  count[i] is the count of\n   * values greater than base^(offset+i) and less than or equal to\n   * base^(offset+i+1).\n   * @returns {number} The logical counts based on the backing array\n   */\n  counts(): number[] {\n    return Array.from({ length: this.length }, (_, i) => this.at(i));\n  }\n\n  /**\n   * At returns the count of the bucket at a position in the logical\n   * array of counts.\n   * @param position\n   * @returns {number}\n   */\n  at(position: number): number {\n    const bias = this.indexBase - this.indexStart;\n    if (position < bias) {\n      position += this.backing.length;\n    }\n\n    position -= bias;\n    return this.backing.countAt(position);\n  }\n\n  /**\n   * incrementBucket increments the backing array index by `increment`\n   * @param bucketIndex\n   * @param increment\n   */\n  incrementBucket(bucketIndex: number, increment: number) {\n    this.backing.increment(bucketIndex, increment);\n  }\n\n  /**\n   * decrementBucket decrements the backing array index by `decrement`\n   * if decrement is greater than the current value, it's set to 0.\n   * @param bucketIndex\n   * @param decrement\n   */\n  decrementBucket(bucketIndex: number, decrement: number) {\n    this.backing.decrement(bucketIndex, decrement);\n  }\n\n  /**\n   * trim removes leading and / or trailing zero buckets (which can occur\n   * after diffing two histos) and rotates the backing array so that the\n   * smallest non-zero index is in the 0th position of the backing array\n   */\n  trim() {\n    for (let i = 0; i < this.length; i++) {\n      if (this.at(i) !== 0) {\n        this.indexStart += i;\n        break;\n      } else if (i === this.length - 1) {\n        //the entire array is zeroed out\n        this.indexStart = this.indexEnd = this.indexBase = 0;\n        return;\n      }\n    }\n\n    for (let i = this.length - 1; i >= 0; i--) {\n      if (this.at(i) !== 0) {\n        this.indexEnd -= this.length - i - 1;\n        break;\n      }\n    }\n\n    this._rotate();\n  }\n\n  /**\n   * downscale first rotates, then collapses 2**`by`-to-1 buckets.\n   * @param by\n   */\n  downscale(by: number) {\n    this._rotate();\n\n    const size = 1 + this.indexEnd - this.indexStart;\n    const each = 1 << by;\n    let inpos = 0;\n    let outpos = 0;\n\n    for (let pos = this.indexStart; pos <= this.indexEnd; ) {\n      let mod = pos % each;\n      if (mod < 0) {\n        mod += each;\n      }\n      for (let i = mod; i < each && inpos < size; i++) {\n        this._relocateBucket(outpos, inpos);\n        inpos++;\n        pos++;\n      }\n      outpos++;\n    }\n\n    this.indexStart >>= by;\n    this.indexEnd >>= by;\n    this.indexBase = this.indexStart;\n  }\n\n  /**\n   * Clone returns a deep copy of Buckets\n   * @returns {Buckets}\n   */\n  clone(): Buckets {\n    return new Buckets(\n      this.backing.clone(),\n      this.indexBase,\n      this.indexStart,\n      this.indexEnd\n    );\n  }\n\n  /**\n   * _rotate shifts the backing array contents so that indexStart ==\n   * indexBase to simplify the downscale logic.\n   */\n  private _rotate() {\n    const bias = this.indexBase - this.indexStart;\n\n    if (bias === 0) {\n      return;\n    } else if (bias > 0) {\n      this.backing.reverse(0, this.backing.length);\n      this.backing.reverse(0, bias);\n      this.backing.reverse(bias, this.backing.length);\n    } else {\n      // negative bias, this can happen when diffing two histograms\n      this.backing.reverse(0, this.backing.length);\n      this.backing.reverse(0, this.backing.length + bias);\n    }\n    this.indexBase = this.indexStart;\n  }\n\n  /**\n   * _relocateBucket adds the count in counts[src] to counts[dest] and\n   * resets count[src] to zero.\n   */\n  private _relocateBucket(dest: number, src: number) {\n    if (dest === src) {\n      return;\n    }\n    this.incrementBucket(dest, this.backing.emptyBucket(src));\n  }\n}\n\n/**\n * BucketsBacking holds the raw buckets and some utility methods to\n * manage them.\n */\nclass BucketsBacking {\n  constructor(private _counts = [0]) {}\n\n  /**\n   * length returns the physical size of the backing array, which\n   * is >= buckets.length()\n   */\n  get length(): number {\n    return this._counts.length;\n  }\n\n  /**\n   * countAt returns the count in a specific bucket\n   */\n  countAt(pos: number): number {\n    return this._counts[pos];\n  }\n\n  /**\n   * growTo grows a backing array and copies old entries\n   * into their correct new positions.\n   */\n  growTo(newSize: number, oldPositiveLimit: number, newPositiveLimit: number) {\n    const tmp = new Array<number>(newSize).fill(0);\n    tmp.splice(\n      newPositiveLimit,\n      this._counts.length - oldPositiveLimit,\n      ...this._counts.slice(oldPositiveLimit)\n    );\n    tmp.splice(0, oldPositiveLimit, ...this._counts.slice(0, oldPositiveLimit));\n    this._counts = tmp;\n  }\n\n  /**\n   * reverse the items in the backing array in the range [from, limit).\n   */\n  reverse(from: number, limit: number) {\n    const num = Math.floor((from + limit) / 2) - from;\n    for (let i = 0; i < num; i++) {\n      const tmp = this._counts[from + i];\n      this._counts[from + i] = this._counts[limit - i - 1];\n      this._counts[limit - i - 1] = tmp;\n    }\n  }\n\n  /**\n   * emptyBucket empties the count from a bucket, for\n   * moving into another.\n   */\n  emptyBucket(src: number): number {\n    const tmp = this._counts[src];\n    this._counts[src] = 0;\n    return tmp;\n  }\n\n  /**\n   * increments a bucket by `increment`\n   */\n  increment(bucketIndex: number, increment: number) {\n    this._counts[bucketIndex] += increment;\n  }\n\n  /**\n   * decrements a bucket by `decrement`\n   */\n  decrement(bucketIndex: number, decrement: number) {\n    if (this._counts[bucketIndex] >= decrement) {\n      this._counts[bucketIndex] -= decrement;\n    } else {\n      // this should not happen, but we're being defensive against\n      // negative counts.\n      this._counts[bucketIndex] = 0;\n    }\n  }\n\n  /**\n   * clone returns a deep copy of BucketsBacking\n   */\n  clone(): BucketsBacking {\n    return new BucketsBacking([...this._counts]);\n  }\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;;;;;;;;;;;;;;GAcG,CACH,IAAA,UAAA;IACE;;;;;;;;;;;;;;;;OAgBG,CACH,SAAA,QACS,OAA8B,EAC9B,SAAa,EACb,UAAc,EACd,QAAY;QAHZ,IAAA,YAAA,KAAA,GAAA;YAAA,UAAA,IAAc,cAAc,EAAE;QAAA;QAC9B,IAAA,cAAA,KAAA,GAAA;YAAA,YAAA,CAAa;QAAA;QACb,IAAA,eAAA,KAAA,GAAA;YAAA,aAAA,CAAc;QAAA;QACd,IAAA,aAAA,KAAA,GAAA;YAAA,WAAA,CAAY;QAAA;QAHZ,IAAA,CAAA,OAAO,GAAP,OAAO,CAAuB;QAC9B,IAAA,CAAA,SAAS,GAAT,SAAS,CAAI;QACb,IAAA,CAAA,UAAU,GAAV,UAAU,CAAI;QACd,IAAA,CAAA,QAAQ,GAAR,QAAQ,CAAI;IAClB,CAAC;IAMJ,OAAA,cAAA,CAAI,QAAA,SAAA,EAAA,QAAM,EAAA;QAJV;;;WAGG,MACH;YACE,OAAO,IAAI,CAAC,UAAU,CAAC;QACzB,CAAC;;;OAAA;IAMD,OAAA,cAAA,CAAI,QAAA,SAAA,EAAA,QAAM,EAAA;QAJV;;;WAGG,MACH;YACE,IAAI,IAAI,CAAC,OAAO,CAAC,MAAM,KAAK,CAAC,EAAE;gBAC7B,OAAO,CAAC,CAAC;aACV;YAED,IAAI,IAAI,CAAC,QAAQ,KAAK,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC,EAAE;gBACzD,OAAO,CAAC,CAAC;aACV;YAED,OAAO,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,UAAU,GAAG,CAAC,CAAC;QAC7C,CAAC;;;OAAA;IAED;;;;;;OAMG,CACH,QAAA,SAAA,CAAA,MAAM,GAAN;QAAA,IAAA,QAAA,IAAA,CAEC;QADC,OAAO,KAAK,CAAC,IAAI,CAAC;YAAE,MAAM,EAAE,IAAI,CAAC,MAAM;QAAA,CAAE,EAAE,SAAC,CAAC,EAAE,CAAC;YAAK,OAAA,KAAI,CAAC,EAAE,CAAC,CAAC,CAAC;QAAV,CAAU,CAAC,CAAC;IACnE,CAAC;IAED;;;;;OAKG,CACH,QAAA,SAAA,CAAA,EAAE,GAAF,SAAG,QAAgB;QACjB,IAAM,IAAI,GAAG,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,UAAU,CAAC;QAC9C,IAAI,QAAQ,GAAG,IAAI,EAAE;YACnB,QAAQ,IAAI,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC;SACjC;QAED,QAAQ,IAAI,IAAI,CAAC;QACjB,OAAO,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;IACxC,CAAC;IAED;;;;OAIG,CACH,QAAA,SAAA,CAAA,eAAe,GAAf,SAAgB,WAAmB,EAAE,SAAiB;QACpD,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,WAAW,EAAE,SAAS,CAAC,CAAC;IACjD,CAAC;IAED;;;;;OAKG,CACH,QAAA,SAAA,CAAA,eAAe,GAAf,SAAgB,WAAmB,EAAE,SAAiB;QACpD,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,WAAW,EAAE,SAAS,CAAC,CAAC;IACjD,CAAC;IAED;;;;OAIG,CACH,QAAA,SAAA,CAAA,IAAI,GAAJ;QACE,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,EAAE,CAAE;YACpC,IAAI,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC,EAAE;gBACpB,IAAI,CAAC,UAAU,IAAI,CAAC,CAAC;gBACrB,MAAM;aACP,MAAM,IAAI,CAAC,KAAK,IAAI,CAAC,MAAM,GAAG,CAAC,EAAE;gBAChC,gCAAgC;gBAChC,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,SAAS,GAAG,CAAC,CAAC;gBACrD,OAAO;aACR;SACF;QAED,IAAK,IAAI,CAAC,GAAG,IAAI,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,CAAE;YACzC,IAAI,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC,EAAE;gBACpB,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,MAAM,GAAG,CAAC,GAAG,CAAC,CAAC;gBACrC,MAAM;aACP;SACF;QAED,IAAI,CAAC,OAAO,EAAE,CAAC;IACjB,CAAC;IAED;;;OAGG,CACH,QAAA,SAAA,CAAA,SAAS,GAAT,SAAU,EAAU;QAClB,IAAI,CAAC,OAAO,EAAE,CAAC;QAEf,IAAM,IAAI,GAAG,CAAC,GAAG,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,UAAU,CAAC;QACjD,IAAM,IAAI,GAAG,CAAC,IAAI,EAAE,CAAC;QACrB,IAAI,KAAK,GAAG,CAAC,CAAC;QACd,IAAI,MAAM,GAAG,CAAC,CAAC;QAEf,IAAK,IAAI,GAAG,GAAG,IAAI,CAAC,UAAU,EAAE,GAAG,IAAI,IAAI,CAAC,QAAQ,EAAI;YACtD,IAAI,GAAG,GAAG,GAAG,GAAG,IAAI,CAAC;YACrB,IAAI,GAAG,GAAG,CAAC,EAAE;gBACX,GAAG,IAAI,IAAI,CAAC;aACb;YACD,IAAK,IAAI,CAAC,GAAG,GAAG,EAAE,CAAC,GAAG,IAAI,IAAI,KAAK,GAAG,IAAI,EAAE,CAAC,EAAE,CAAE;gBAC/C,IAAI,CAAC,eAAe,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;gBACpC,KAAK,EAAE,CAAC;gBACR,GAAG,EAAE,CAAC;aACP;YACD,MAAM,EAAE,CAAC;SACV;QAED,IAAI,CAAC,UAAU,KAAK,EAAE,CAAC;QACvB,IAAI,CAAC,QAAQ,KAAK,EAAE,CAAC;QACrB,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,UAAU,CAAC;IACnC,CAAC;IAED;;;OAGG,CACH,QAAA,SAAA,CAAA,KAAK,GAAL;QACE,OAAO,IAAI,OAAO,CAChB,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE,EACpB,IAAI,CAAC,SAAS,EACd,IAAI,CAAC,UAAU,EACf,IAAI,CAAC,QAAQ,CACd,CAAC;IACJ,CAAC;IAED;;;OAGG,CACK,QAAA,SAAA,CAAA,OAAO,GAAf;QACE,IAAM,IAAI,GAAG,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,UAAU,CAAC;QAE9C,IAAI,IAAI,KAAK,CAAC,EAAE;YACd,OAAO;SACR,MAAM,IAAI,IAAI,GAAG,CAAC,EAAE;YACnB,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,EAAE,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;YAC7C,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC;YAC9B,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,IAAI,EAAE,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;SACjD,MAAM;YACL,6DAA6D;YAC7D,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,EAAE,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;YAC7C,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,EAAE,IAAI,CAAC,OAAO,CAAC,MAAM,GAAG,IAAI,CAAC,CAAC;SACrD;QACD,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,UAAU,CAAC;IACnC,CAAC;IAED;;;OAGG,CACK,QAAA,SAAA,CAAA,eAAe,GAAvB,SAAwB,IAAY,EAAE,GAAW;QAC/C,IAAI,IAAI,KAAK,GAAG,EAAE;YAChB,OAAO;SACR;QACD,IAAI,CAAC,eAAe,CAAC,IAAI,EAAE,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC,CAAC;IAC5D,CAAC;IACH,OAAA,OAAC;AAAD,CAAC,AApMD,IAoMC;;AAED;;;GAGG,CACH,IAAA,iBAAA;IACE,SAAA,eAAoB,OAAa;QAAb,IAAA,YAAA,KAAA,GAAA;YAAA,UAAA;gBAAW,CAAC;aAAC;QAAA;QAAb,IAAA,CAAA,OAAO,GAAP,OAAO,CAAM;IAAG,CAAC;IAMrC,OAAA,cAAA,CAAI,eAAA,SAAA,EAAA,QAAM,EAAA;QAJV;;;WAGG,MACH;YACE,OAAO,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC;QAC7B,CAAC;;;OAAA;IAED;;OAEG,CACH,eAAA,SAAA,CAAA,OAAO,GAAP,SAAQ,GAAW;QACjB,OAAO,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;IAC3B,CAAC;IAED;;;OAGG,CACH,eAAA,SAAA,CAAA,MAAM,GAAN,SAAO,OAAe,EAAE,gBAAwB,EAAE,gBAAwB;QACxE,IAAM,GAAG,GAAG,IAAI,KAAK,CAAS,OAAO,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAC/C,GAAG,CAAC,MAAM,CAAA,KAAA,CAAV,GAAG,EAAA,cAAA;YACD,gBAAgB;YAChB,IAAI,CAAC,OAAO,CAAC,MAAM,GAAG,gBAAgB;SAAA,EAAA,OACnC,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,gBAAgB,CAAC,GAAA,QACvC;QACF,GAAG,CAAC,MAAM,CAAA,KAAA,CAAV,GAAG,EAAA,cAAA;YAAQ,CAAC;YAAE,gBAAgB;SAAA,EAAA,OAAK,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,EAAE,gBAAgB,CAAC,GAAA,QAAE;QAC5E,IAAI,CAAC,OAAO,GAAG,GAAG,CAAC;IACrB,CAAC;IAED;;OAEG,CACH,eAAA,SAAA,CAAA,OAAO,GAAP,SAAQ,IAAY,EAAE,KAAa;QACjC,IAAM,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,IAAI,GAAG,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC;QAClD,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE,CAAE;YAC5B,IAAM,GAAG,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,GAAG,CAAC,CAAC,CAAC;YACnC,IAAI,CAAC,OAAO,CAAC,IAAI,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC,KAAK,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;YACrD,IAAI,CAAC,OAAO,CAAC,KAAK,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC;SACnC;IACH,CAAC;IAED;;;OAGG,CACH,eAAA,SAAA,CAAA,WAAW,GAAX,SAAY,GAAW;QACrB,IAAM,GAAG,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;QAC9B,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;QACtB,OAAO,GAAG,CAAC;IACb,CAAC;IAED;;OAEG,CACH,eAAA,SAAA,CAAA,SAAS,GAAT,SAAU,WAAmB,EAAE,SAAiB;QAC9C,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,IAAI,SAAS,CAAC;IACzC,CAAC;IAED;;OAEG,CACH,eAAA,SAAA,CAAA,SAAS,GAAT,SAAU,WAAmB,EAAE,SAAiB;QAC9C,IAAI,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,IAAI,SAAS,EAAE;YAC1C,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,IAAI,SAAS,CAAC;SACxC,MAAM;YACL,4DAA4D;YAC5D,mBAAmB;YACnB,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC;SAC/B;IACH,CAAC;IAED;;OAEG,CACH,eAAA,SAAA,CAAA,KAAK,GAAL;QACE,OAAO,IAAI,cAAc,CAAA,cAAA,EAAA,EAAA,OAAK,IAAI,CAAC,OAAO,GAAA,OAAE,CAAC;IAC/C,CAAC;IACH,OAAA,cAAC;AAAD,CAAC,AAjFD,IAiFC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1438, "column": 0}, "map": {"version": 3, "file": "ieee754.js", "sourceRoot": "", "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/node_modules/%40opentelemetry/sdk-node/node_modules/%40opentelemetry/sdk-metrics/src/aggregator/exponential-histogram/mapping/ieee754.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n/**\n * The functions and constants in this file allow us to interact\n * with the internal representation of an IEEE 64-bit floating point\n * number. We need to work with all 64-bits, thus, care needs to be\n * taken when working with Javascript's bitwise operators (<<, >>, &,\n * |, etc) as they truncate operands to 32-bits. In order to work around\n * this we work with the 64-bits as two 32-bit halves, perform bitwise\n * operations on them independently, and combine the results (if needed).\n */\n\nexport const SIGNIFICAND_WIDTH = 52;\n\n/**\n * EXPONENT_MASK is set to 1 for the hi 32-bits of an IEEE 754\n * floating point exponent: 0x7ff00000.\n */\nconst EXPONENT_MASK = 0x7ff00000;\n\n/**\n * SIGNIFICAND_MASK is the mask for the significand portion of the hi 32-bits\n * of an IEEE 754 double-precision floating-point value: 0xfffff\n */\nconst SIGNIFICAND_MASK = 0xfffff;\n\n/**\n * EXPONENT_BIAS is the exponent bias specified for encoding\n * the IEEE 754 double-precision floating point exponent: 1023\n */\nconst EXPONENT_BIAS = 1023;\n\n/**\n * MIN_NORMAL_EXPONENT is the minimum exponent of a normalized\n * floating point: -1022.\n */\nexport const MIN_NORMAL_EXPONENT = -EXPONENT_BIAS + 1;\n\n/**\n * MAX_NORMAL_EXPONENT is the maximum exponent of a normalized\n * floating point: 1023.\n */\nexport const MAX_NORMAL_EXPONENT = EXPONENT_BIAS;\n\n/**\n * MIN_VALUE is the smallest normal number\n */\nexport const MIN_VALUE = Math.pow(2, -1022);\n\n/**\n * getNormalBase2 extracts the normalized base-2 fractional exponent.\n * This returns k for the equation f x 2**k where f is\n * in the range [1, 2).  Note that this function is not called for\n * subnormal numbers.\n * @param {number} value - the value to determine normalized base-2 fractional\n *    exponent for\n * @returns {number} the normalized base-2 exponent\n */\nexport function getNormalBase2(value: number): number {\n  const dv = new DataView(new ArrayBuffer(8));\n  dv.setFloat64(0, value);\n  // access the raw 64-bit float as 32-bit uints\n  const hiBits = dv.getUint32(0);\n  const expBits = (hiBits & EXPONENT_MASK) >> 20;\n  return expBits - EXPONENT_BIAS;\n}\n\n/**\n * GetSignificand returns the 52 bit (unsigned) significand as a signed value.\n * @param {number} value - the floating point number to extract the significand from\n * @returns {number} The 52-bit significand\n */\nexport function getSignificand(value: number): number {\n  const dv = new DataView(new ArrayBuffer(8));\n  dv.setFloat64(0, value);\n  // access the raw 64-bit float as two 32-bit uints\n  const hiBits = dv.getUint32(0);\n  const loBits = dv.getUint32(4);\n  // extract the significand bits from the hi bits and left shift 32 places note:\n  // we can't use the native << operator as it will truncate the result to 32-bits\n  const significandHiBits = (hiBits & SIGNIFICAND_MASK) * Math.pow(2, 32);\n  // combine the hi and lo bits and return\n  return significandHiBits + loBits;\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG,CAEH;;;;;;;;GAQG;;;;;;;;AAEI,IAAM,iBAAiB,GAAG,EAAE,CAAC;AAEpC;;;GAGG,CACH,IAAM,aAAa,GAAG,UAAU,CAAC;AAEjC;;;GAGG,CACH,IAAM,gBAAgB,GAAG,OAAO,CAAC;AAEjC;;;GAGG,CACH,IAAM,aAAa,GAAG,IAAI,CAAC;AAMpB,IAAM,mBAAmB,GAAG,CAAC,aAAa,GAAG,CAAC,CAAC;AAM/C,IAAM,mBAAmB,GAAG,aAAa,CAAC;AAK1C,IAAM,SAAS,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC;AAWtC,SAAU,cAAc,CAAC,KAAa;IAC1C,IAAM,EAAE,GAAG,IAAI,QAAQ,CAAC,IAAI,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC;IAC5C,EAAE,CAAC,UAAU,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC;IACxB,8CAA8C;IAC9C,IAAM,MAAM,GAAG,EAAE,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;IAC/B,IAAM,OAAO,GAAG,CAAC,MAAM,GAAG,aAAa,CAAC,IAAI,EAAE,CAAC;IAC/C,OAAO,OAAO,GAAG,aAAa,CAAC;AACjC,CAAC;AAOK,SAAU,cAAc,CAAC,KAAa;IAC1C,IAAM,EAAE,GAAG,IAAI,QAAQ,CAAC,IAAI,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC;IAC5C,EAAE,CAAC,UAAU,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC;IACxB,kDAAkD;IAClD,IAAM,MAAM,GAAG,EAAE,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;IAC/B,IAAM,MAAM,GAAG,EAAE,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;IAC/B,+EAA+E;IAC/E,gFAAgF;IAChF,IAAM,iBAAiB,GAAG,CAAC,MAAM,GAAG,gBAAgB,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;IACxE,wCAAwC;IACxC,OAAO,iBAAiB,GAAG,MAAM,CAAC;AACpC,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1510, "column": 0}, "map": {"version": 3, "file": "util.js", "sourceRoot": "", "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/node_modules/%40opentelemetry/sdk-node/node_modules/%40opentelemetry/sdk-metrics/src/aggregator/exponential-histogram/util.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n/**\n * Note: other languages provide this as a built in function. This is\n * a naive, but functionally correct implementation. This is used sparingly,\n * when creating a new mapping in a running application.\n *\n * ldexp returns frac × 2**exp. With the following special cases:\n *   ldexp(±0, exp) = ±0\n *   ldexp(±Inf, exp) = ±Inf\n *   ldexp(NaN, exp) = NaN\n * @param frac\n * @param exp\n * @returns {number}\n */\nexport function ldexp(frac: number, exp: number): number {\n  if (\n    frac === 0 ||\n    frac === Number.POSITIVE_INFINITY ||\n    frac === Number.NEGATIVE_INFINITY ||\n    Number.isNaN(frac)\n  ) {\n    return frac;\n  }\n  return frac * Math.pow(2, exp);\n}\n\n/**\n * Computes the next power of two that is greater than or equal to v.\n * This implementation more efficient than, but functionally equivalent\n * to Math.pow(2, Math.ceil(Math.log(x)/Math.log(2))).\n * @param v\n * @returns {number}\n */\nexport function nextGreaterSquare(v: number): number {\n  // The following expression computes the least power-of-two\n  // that is >= v.  There are a number of tricky ways to\n  // do this, see https://stackoverflow.com/questions/466204/rounding-up-to-next-power-of-2\n  v--;\n  v |= v >> 1;\n  v |= v >> 2;\n  v |= v >> 4;\n  v |= v >> 8;\n  v |= v >> 16;\n  v++;\n  return v;\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG,CAEH;;;;;;;;;;;;GAYG;;;;AACG,SAAU,KAAK,CAAC,IAAY,EAAE,GAAW;IAC7C,IACE,IAAI,KAAK,CAAC,IACV,IAAI,KAAK,MAAM,CAAC,iBAAiB,IACjC,IAAI,KAAK,MAAM,CAAC,iBAAiB,IACjC,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,EAClB;QACA,OAAO,IAAI,CAAC;KACb;IACD,OAAO,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC;AACjC,CAAC;AASK,SAAU,iBAAiB,CAAC,CAAS;IACzC,2DAA2D;IAC3D,sDAAsD;IACtD,yFAAyF;IACzF,CAAC,EAAE,CAAC;IACJ,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IACZ,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IACZ,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IACZ,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IACZ,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC;IACb,CAAC,EAAE,CAAC;IACJ,OAAO,CAAC,CAAC;AACX,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1565, "column": 0}, "map": {"version": 3, "file": "types.js", "sourceRoot": "", "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/node_modules/%40opentelemetry/sdk-node/node_modules/%40opentelemetry/sdk-metrics/src/aggregator/exponential-histogram/mapping/types.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nexport class MappingError extends Error {}\n\n/**\n * The mapping interface is used by the exponential histogram to determine\n * where to bucket values. The interface is implemented by ExponentMapping,\n * used for scales [-10, 0] and LogarithmMapping, used for scales [1, 20].\n */\nexport interface Mapping {\n  mapToIndex(value: number): number;\n  lowerBoundary(index: number): number;\n  get scale(): number;\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;AAAA;;;;;;;;;;;;;;GAcG,CACH,IAAA,eAAA,SAAA,MAAA;IAAkC,UAAA,cAAA,QAAK;IAAvC,SAAA;;IAAyC,CAAC;IAAD,OAAA,YAAC;AAAD,CAAC,AAA1C,CAAkC,KAAK,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1617, "column": 0}, "map": {"version": 3, "file": "ExponentMapping.js", "sourceRoot": "", "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/node_modules/%40opentelemetry/sdk-node/node_modules/%40opentelemetry/sdk-metrics/src/aggregator/exponential-histogram/mapping/ExponentMapping.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nimport * as ieee754 from './ieee754';\nimport * as util from '../util';\nimport { Mapping, MappingError } from './types';\n\n/**\n * ExponentMapping implements exponential mapping functions for\n * scales <=0. For scales > 0 LogarithmMapping should be used.\n */\nexport class ExponentMapping implements Mapping {\n  private readonly _shift: number;\n\n  constructor(scale: number) {\n    this._shift = -scale;\n  }\n\n  /**\n   * Maps positive floating point values to indexes corresponding to scale\n   * @param value\n   * @returns {number} index for provided value at the current scale\n   */\n  mapToIndex(value: number): number {\n    if (value < ieee754.MIN_VALUE) {\n      return this._minNormalLowerBoundaryIndex();\n    }\n\n    const exp = ieee754.getNormalBase2(value);\n\n    // In case the value is an exact power of two, compute a\n    // correction of -1. Note, we are using a custom _rightShift\n    // to accommodate a 52-bit argument, which the native bitwise\n    // operators do not support\n    const correction = this._rightShift(\n      ieee754.getSignificand(value) - 1,\n      ieee754.SIGNIFICAND_WIDTH\n    );\n\n    return (exp + correction) >> this._shift;\n  }\n\n  /**\n   * Returns the lower bucket boundary for the given index for scale\n   *\n   * @param index\n   * @returns {number}\n   */\n  lowerBoundary(index: number): number {\n    const minIndex = this._minNormalLowerBoundaryIndex();\n    if (index < minIndex) {\n      throw new MappingError(\n        `underflow: ${index} is < minimum lower boundary: ${minIndex}`\n      );\n    }\n    const maxIndex = this._maxNormalLowerBoundaryIndex();\n    if (index > maxIndex) {\n      throw new MappingError(\n        `overflow: ${index} is > maximum lower boundary: ${maxIndex}`\n      );\n    }\n\n    return util.ldexp(1, index << this._shift);\n  }\n\n  /**\n   * The scale used by this mapping\n   * @returns {number}\n   */\n  get scale(): number {\n    if (this._shift === 0) {\n      return 0;\n    }\n    return -this._shift;\n  }\n\n  private _minNormalLowerBoundaryIndex(): number {\n    let index = ieee754.MIN_NORMAL_EXPONENT >> this._shift;\n    if (this._shift < 2) {\n      index--;\n    }\n\n    return index;\n  }\n\n  private _maxNormalLowerBoundaryIndex(): number {\n    return ieee754.MAX_NORMAL_EXPONENT >> this._shift;\n  }\n\n  private _rightShift(value: number, shift: number): number {\n    return Math.floor(value * Math.pow(2, -shift));\n  }\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;AACH,OAAO,KAAK,OAAO,MAAM,WAAW,CAAC;AACrC,OAAO,KAAK,IAAI,MAAM,SAAS,CAAC;AAChC,OAAO,EAAW,YAAY,EAAE,MAAM,SAAS,CAAC;;;;AAEhD;;;GAGG,CACH,IAAA,kBAAA;IAGE,SAAA,gBAAY,KAAa;QACvB,IAAI,CAAC,MAAM,GAAG,CAAC,KAAK,CAAC;IACvB,CAAC;IAED;;;;OAIG,CACH,gBAAA,SAAA,CAAA,UAAU,GAAV,SAAW,KAAa;QACtB,IAAI,KAAK,0RAAG,OAAO,CAAC,IAAS,EAAE;YAC7B,OAAO,IAAI,CAAC,4BAA4B,EAAE,CAAC;SAC5C;QAED,IAAM,GAAG,8RAAG,OAAO,CAAC,SAAA,AAAc,EAAC,KAAK,CAAC,CAAC;QAE1C,wDAAwD;QACxD,4DAA4D;QAC5D,6DAA6D;QAC7D,2BAA2B;QAC3B,IAAM,UAAU,GAAG,IAAI,CAAC,WAAW,4RACjC,OAAO,CAAC,SAAA,AAAc,EAAC,KAAK,CAAC,GAAG,CAAC,wRACjC,OAAO,CAAC,aAAiB,CAC1B,CAAC;QAEF,OAAO,AAAC,GAAG,GAAG,UAAU,CAAC,GAAI,IAAI,CAAC,MAAM,CAAC;IAC3C,CAAC;IAED;;;;;OAKG,CACH,gBAAA,SAAA,CAAA,aAAa,GAAb,SAAc,KAAa;QACzB,IAAM,QAAQ,GAAG,IAAI,CAAC,4BAA4B,EAAE,CAAC;QACrD,IAAI,KAAK,GAAG,QAAQ,EAAE;YACpB,MAAM,yRAAI,eAAY,CACpB,gBAAc,KAAK,GAAA,mCAAiC,QAAU,CAC/D,CAAC;SACH;QACD,IAAM,QAAQ,GAAG,IAAI,CAAC,4BAA4B,EAAE,CAAC;QACrD,IAAI,KAAK,GAAG,QAAQ,EAAE;YACpB,MAAM,yRAAI,eAAY,CACpB,eAAa,KAAK,GAAA,mCAAiC,QAAU,CAC9D,CAAC;SACH;QAED,OAAO,IAAI,CAAC,gRAAA,AAAK,EAAC,CAAC,EAAE,KAAK,IAAI,IAAI,CAAC,MAAM,CAAC,CAAC;IAC7C,CAAC;IAMD,OAAA,cAAA,CAAI,gBAAA,SAAA,EAAA,OAAK,EAAA;QAJT;;;WAGG,MACH;YACE,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC,EAAE;gBACrB,OAAO,CAAC,CAAC;aACV;YACD,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC;QACtB,CAAC;;;OAAA;IAEO,gBAAA,SAAA,CAAA,4BAA4B,GAApC;QACE,IAAI,KAAK,0RAAG,OAAO,CAAC,cAAmB,IAAI,IAAI,CAAC,MAAM,CAAC;QACvD,IAAI,IAAI,CAAC,MAAM,GAAG,CAAC,EAAE;YACnB,KAAK,EAAE,CAAC;SACT;QAED,OAAO,KAAK,CAAC;IACf,CAAC;IAEO,gBAAA,SAAA,CAAA,4BAA4B,GAApC;QACE,8RAAO,OAAO,CAAC,cAAmB,IAAI,IAAI,CAAC,MAAM,CAAC;IACpD,CAAC;IAEO,gBAAA,SAAA,CAAA,WAAW,GAAnB,SAAoB,KAAa,EAAE,KAAa;QAC9C,OAAO,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC;IACjD,CAAC;IACH,OAAA,eAAC;AAAD,CAAC,AAjFD,IAiFC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1715, "column": 0}, "map": {"version": 3, "file": "LogarithmMapping.js", "sourceRoot": "", "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/node_modules/%40opentelemetry/sdk-node/node_modules/%40opentelemetry/sdk-metrics/src/aggregator/exponential-histogram/mapping/LogarithmMapping.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nimport * as ieee754 from './ieee754';\nimport * as util from '../util';\nimport { Mapping, MappingError } from './types';\n\n/**\n * LogarithmMapping implements exponential mapping functions for scale > 0.\n * For scales <= 0 the exponent mapping should be used.\n */\nexport class LogarithmMapping implements Mapping {\n  private readonly _scale: number;\n  private readonly _scaleFactor: number;\n  private readonly _inverseFactor: number;\n\n  constructor(scale: number) {\n    this._scale = scale;\n    this._scaleFactor = util.ldexp(Math.LOG2E, scale);\n    this._inverseFactor = util.ldexp(Math.LN2, -scale);\n  }\n\n  /**\n   * Maps positive floating point values to indexes corresponding to scale\n   * @param value\n   * @returns {number} index for provided value at the current scale\n   */\n  mapToIndex(value: number): number {\n    if (value <= ieee754.MIN_VALUE) {\n      return this._minNormalLowerBoundaryIndex() - 1;\n    }\n\n    // exact power of two special case\n    if (ieee754.getSignificand(value) === 0) {\n      const exp = ieee754.getNormalBase2(value);\n      return (exp << this._scale) - 1;\n    }\n\n    // non-power of two cases. use Math.floor to round the scaled logarithm\n    const index = Math.floor(Math.log(value) * this._scaleFactor);\n    const maxIndex = this._maxNormalLowerBoundaryIndex();\n    if (index >= maxIndex) {\n      return maxIndex;\n    }\n\n    return index;\n  }\n\n  /**\n   * Returns the lower bucket boundary for the given index for scale\n   *\n   * @param index\n   * @returns {number}\n   */\n  lowerBoundary(index: number): number {\n    const maxIndex = this._maxNormalLowerBoundaryIndex();\n    if (index >= maxIndex) {\n      if (index === maxIndex) {\n        return 2 * Math.exp((index - (1 << this._scale)) / this._scaleFactor);\n      }\n      throw new MappingError(\n        `overflow: ${index} is > maximum lower boundary: ${maxIndex}`\n      );\n    }\n\n    const minIndex = this._minNormalLowerBoundaryIndex();\n    if (index <= minIndex) {\n      if (index === minIndex) {\n        return ieee754.MIN_VALUE;\n      } else if (index === minIndex - 1) {\n        return Math.exp((index + (1 << this._scale)) / this._scaleFactor) / 2;\n      }\n      throw new MappingError(\n        `overflow: ${index} is < minimum lower boundary: ${minIndex}`\n      );\n    }\n\n    return Math.exp(index * this._inverseFactor);\n  }\n\n  /**\n   * The scale used by this mapping\n   * @returns {number}\n   */\n  get scale(): number {\n    return this._scale;\n  }\n\n  private _minNormalLowerBoundaryIndex(): number {\n    return ieee754.MIN_NORMAL_EXPONENT << this._scale;\n  }\n\n  private _maxNormalLowerBoundaryIndex(): number {\n    return ((ieee754.MAX_NORMAL_EXPONENT + 1) << this._scale) - 1;\n  }\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;AACH,OAAO,KAAK,OAAO,MAAM,WAAW,CAAC;AACrC,OAAO,KAAK,IAAI,MAAM,SAAS,CAAC;AAChC,OAAO,EAAW,YAAY,EAAE,MAAM,SAAS,CAAC;;;;AAEhD;;;GAGG,CACH,IAAA,mBAAA;IAKE,SAAA,iBAAY,KAAa;QACvB,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;QACpB,IAAI,CAAC,YAAY,+QAAG,IAAI,CAAC,IAAA,AAAK,EAAC,IAAI,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;QAClD,IAAI,CAAC,cAAc,gRAAG,IAAI,CAAC,GAAA,AAAK,EAAC,IAAI,CAAC,GAAG,EAAE,CAAC,KAAK,CAAC,CAAC;IACrD,CAAC;IAED;;;;OAIG,CACH,iBAAA,SAAA,CAAA,UAAU,GAAV,SAAW,KAAa;QACtB,IAAI,KAAK,2RAAI,OAAO,CAAC,IAAS,EAAE;YAC9B,OAAO,IAAI,CAAC,4BAA4B,EAAE,GAAG,CAAC,CAAC;SAChD;QAED,kCAAkC;QAClC,KAAI,OAAO,CAAC,mSAAA,AAAc,EAAC,KAAK,CAAC,KAAK,CAAC,EAAE;YACvC,IAAM,GAAG,8RAAG,OAAO,CAAC,SAAA,AAAc,EAAC,KAAK,CAAC,CAAC;YAC1C,OAAO,CAAC,GAAG,IAAI,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;SACjC;QAED,uEAAuE;QACvE,IAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,IAAI,CAAC,YAAY,CAAC,CAAC;QAC9D,IAAM,QAAQ,GAAG,IAAI,CAAC,4BAA4B,EAAE,CAAC;QACrD,IAAI,KAAK,IAAI,QAAQ,EAAE;YACrB,OAAO,QAAQ,CAAC;SACjB;QAED,OAAO,KAAK,CAAC;IACf,CAAC;IAED;;;;;OAKG,CACH,iBAAA,SAAA,CAAA,aAAa,GAAb,SAAc,KAAa;QACzB,IAAM,QAAQ,GAAG,IAAI,CAAC,4BAA4B,EAAE,CAAC;QACrD,IAAI,KAAK,IAAI,QAAQ,EAAE;YACrB,IAAI,KAAK,KAAK,QAAQ,EAAE;gBACtB,OAAO,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC,IAAI,IAAI,CAAC,MAAM,CAAC,CAAC,GAAG,IAAI,CAAC,YAAY,CAAC,CAAC;aACvE;YACD,MAAM,IAAI,oSAAY,CACpB,eAAa,KAAK,GAAA,mCAAiC,QAAU,CAC9D,CAAC;SACH;QAED,IAAM,QAAQ,GAAG,IAAI,CAAC,4BAA4B,EAAE,CAAC;QACrD,IAAI,KAAK,IAAI,QAAQ,EAAE;YACrB,IAAI,KAAK,KAAK,QAAQ,EAAE;gBACtB,8RAAO,OAAO,CAAC,IAAS,CAAC;aAC1B,MAAM,IAAI,KAAK,KAAK,QAAQ,GAAG,CAAC,EAAE;gBACjC,OAAO,IAAI,CAAC,GAAG,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC,IAAI,IAAI,CAAC,MAAM,CAAC,CAAC,GAAG,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC;aACvE;YACD,MAAM,wRAAI,gBAAY,CACpB,eAAa,KAAK,GAAA,mCAAiC,QAAU,CAC9D,CAAC;SACH;QAED,OAAO,IAAI,CAAC,GAAG,CAAC,KAAK,GAAG,IAAI,CAAC,cAAc,CAAC,CAAC;IAC/C,CAAC;IAMD,OAAA,cAAA,CAAI,iBAAA,SAAA,EAAA,OAAK,EAAA;QAJT;;;WAGG,MACH;YACE,OAAO,IAAI,CAAC,MAAM,CAAC;QACrB,CAAC;;;OAAA;IAEO,iBAAA,SAAA,CAAA,4BAA4B,GAApC;QACE,OAAO,OAAO,CAAC,qSAAmB,IAAI,IAAI,CAAC,MAAM,CAAC;IACpD,CAAC;IAEO,iBAAA,SAAA,CAAA,4BAA4B,GAApC;QACE,OAAO,CAAC,uRAAC,OAAO,CAAC,cAAmB,GAAG,CAAC,CAAC,GAAI,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;IAChE,CAAC;IACH,OAAA,gBAAC;AAAD,CAAC,AApFD,IAoFC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1818, "column": 0}, "map": {"version": 3, "file": "getMapping.js", "sourceRoot": "", "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/node_modules/%40opentelemetry/sdk-node/node_modules/%40opentelemetry/sdk-metrics/src/aggregator/exponential-histogram/mapping/getMapping.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nimport { ExponentMapping } from './ExponentMapping';\nimport { LogarithmMapping } from './LogarithmMapping';\nimport { MappingError, Mapping } from './types';\n\nconst MIN_SCALE = -10;\nconst MAX_SCALE = 20;\nconst PREBUILT_MAPPINGS = Array.from({ length: 31 }, (_, i) => {\n  if (i > 10) {\n    return new LogarithmMapping(i - 10);\n  }\n  return new ExponentMapping(i - 10);\n});\n\n/**\n * getMapping returns an appropriate mapping for the given scale. For scales -10\n * to 0 the underlying type will be ExponentMapping. For scales 1 to 20 the\n * underlying type will be LogarithmMapping.\n * @param scale a number in the range [-10, 20]\n * @returns {Mapping}\n */\nexport function getMapping(scale: number): Mapping {\n  if (scale > MAX_SCALE || scale < MIN_SCALE) {\n    throw new MappingError(\n      `expected scale >= ${MIN_SCALE} && <= ${MAX_SCALE}, got: ${scale}`\n    );\n  }\n  // mappings are offset by 10. scale -10 is at position 0 and scale 20 is at 30\n  return PREBUILT_MAPPINGS[scale + 10];\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;AACH,OAAO,EAAE,eAAe,EAAE,MAAM,mBAAmB,CAAC;AACpD,OAAO,EAAE,gBAAgB,EAAE,MAAM,oBAAoB,CAAC;AACtD,OAAO,EAAE,YAAY,EAAW,MAAM,SAAS,CAAC;;;;AAEhD,IAAM,SAAS,GAAG,CAAC,EAAE,CAAC;AACtB,IAAM,SAAS,GAAG,EAAE,CAAC;AACrB,IAAM,iBAAiB,GAAG,KAAK,CAAC,IAAI,CAAC;IAAE,MAAM,EAAE,EAAE;AAAA,CAAE,EAAE,SAAC,CAAC,EAAE,CAAC;IACxD,IAAI,CAAC,GAAG,EAAE,EAAE;QACV,OAAO,oSAAI,mBAAgB,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC;KACrC;IACD,OAAO,mSAAI,kBAAe,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC;AACrC,CAAC,CAAC,CAAC;AASG,SAAU,UAAU,CAAC,KAAa;IACtC,IAAI,KAAK,GAAG,SAAS,IAAI,KAAK,GAAG,SAAS,EAAE;QAC1C,MAAM,yRAAI,eAAY,CACpB,uBAAqB,SAAS,GAAA,YAAU,SAAS,GAAA,YAAU,KAAO,CACnE,CAAC;KACH;IACD,8EAA8E;IAC9E,OAAO,iBAAiB,CAAC,KAAK,GAAG,EAAE,CAAC,CAAC;AACvC,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1864, "column": 0}, "map": {"version": 3, "file": "ExponentialHistogram.js", "sourceRoot": "", "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/node_modules/%40opentelemetry/sdk-node/node_modules/%40opentelemetry/sdk-metrics/src/aggregator/ExponentialHistogram.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  Accumulation,\n  AccumulationRecord,\n  Aggregator,\n  AggregatorKind,\n  ExponentialHistogram,\n} from './types';\nimport {\n  DataPointType,\n  ExponentialHistogramMetricData,\n  MetricDescriptor,\n} from '../export/MetricData';\nimport { diag, HrTime } from '@opentelemetry/api';\nimport { InstrumentType } from '../InstrumentDescriptor';\nimport { Maybe } from '../utils';\nimport { AggregationTemporality } from '../export/AggregationTemporality';\nimport { Buckets } from './exponential-histogram/Buckets';\nimport { getMapping } from './exponential-histogram/mapping/getMapping';\nimport { Mapping } from './exponential-histogram/mapping/types';\nimport { nextGreaterSquare } from './exponential-histogram/util';\n\n/**\n * Internal value type for ExponentialHistogramAggregation.\n * Differs from the exported type as undefined sum/min/max complicate arithmetic\n * performed by this aggregation, but are required to be undefined in the exported types.\n */\ninterface InternalHistogram extends ExponentialHistogram {\n  hasMinMax: boolean;\n  min: number;\n  max: number;\n  sum: number;\n}\n\n// HighLow is a utility class used for computing a common scale for\n// two exponential histogram accumulations\nclass HighLow {\n  static combine(h1: HighLow, h2: HighLow): HighLow {\n    return new HighLow(Math.min(h1.low, h2.low), Math.max(h1.high, h2.high));\n  }\n  constructor(\n    public low: number,\n    public high: number\n  ) {}\n}\n\nconst MAX_SCALE = 20;\nconst DEFAULT_MAX_SIZE = 160;\nconst MIN_MAX_SIZE = 2;\n\nexport class ExponentialHistogramAccumulation implements Accumulation {\n  constructor(\n    public startTime: HrTime = startTime,\n    private _maxSize = DEFAULT_MAX_SIZE,\n    private _recordMinMax = true,\n    private _sum = 0,\n    private _count = 0,\n    private _zeroCount = 0,\n    private _min = Number.POSITIVE_INFINITY,\n    private _max = Number.NEGATIVE_INFINITY,\n    private _positive = new Buckets(),\n    private _negative = new Buckets(),\n    private _mapping: Mapping = getMapping(MAX_SCALE)\n  ) {\n    if (this._maxSize < MIN_MAX_SIZE) {\n      diag.warn(`Exponential Histogram Max Size set to ${this._maxSize}, \\\n                changing to the minimum size of: ${MIN_MAX_SIZE}`);\n      this._maxSize = MIN_MAX_SIZE;\n    }\n  }\n\n  /**\n   * record updates a histogram with a single count\n   * @param {Number} value\n   */\n  record(value: number) {\n    this.updateByIncrement(value, 1);\n  }\n\n  /**\n   * Sets the start time for this accumulation\n   * @param {HrTime} startTime\n   */\n  setStartTime(startTime: HrTime): void {\n    this.startTime = startTime;\n  }\n\n  /**\n   * Returns the datapoint representation of this accumulation\n   * @param {HrTime} startTime\n   */\n  toPointValue(): InternalHistogram {\n    return {\n      hasMinMax: this._recordMinMax,\n      min: this.min,\n      max: this.max,\n      sum: this.sum,\n      positive: {\n        offset: this.positive.offset,\n        bucketCounts: this.positive.counts(),\n      },\n      negative: {\n        offset: this.negative.offset,\n        bucketCounts: this.negative.counts(),\n      },\n      count: this.count,\n      scale: this.scale,\n      zeroCount: this.zeroCount,\n    };\n  }\n\n  /**\n   * @returns {Number} The sum of values recorded by this accumulation\n   */\n  get sum(): number {\n    return this._sum;\n  }\n\n  /**\n   * @returns {Number} The minimum value recorded by this accumulation\n   */\n  get min(): number {\n    return this._min;\n  }\n\n  /**\n   * @returns {Number} The maximum value recorded by this accumulation\n   */\n  get max(): number {\n    return this._max;\n  }\n\n  /**\n   * @returns {Number} The count of values recorded by this accumulation\n   */\n  get count(): number {\n    return this._count;\n  }\n\n  /**\n   * @returns {Number} The number of 0 values recorded by this accumulation\n   */\n  get zeroCount(): number {\n    return this._zeroCount;\n  }\n\n  /**\n   * @returns {Number} The scale used by this accumulation\n   */\n  get scale(): number {\n    if (this._count === this._zeroCount) {\n      // all zeros! scale doesn't matter, use zero\n      return 0;\n    }\n    return this._mapping.scale;\n  }\n\n  /**\n   * positive holds the positive values\n   * @returns {Buckets}\n   */\n  get positive(): Buckets {\n    return this._positive;\n  }\n\n  /**\n   * negative holds the negative values by their absolute value\n   * @returns {Buckets}\n   */\n  get negative(): Buckets {\n    return this._negative;\n  }\n\n  /**\n   * updateByIncr supports updating a histogram with a non-negative\n   * increment.\n   * @param value\n   * @param increment\n   */\n  updateByIncrement(value: number, increment: number) {\n    // NaN does not fall into any bucket, is not zero and should not be counted,\n    // NaN is never greater than max nor less than min, therefore return as there's nothing for us to do.\n    if (Number.isNaN(value)) {\n      return;\n    }\n\n    if (value > this._max) {\n      this._max = value;\n    }\n    if (value < this._min) {\n      this._min = value;\n    }\n\n    this._count += increment;\n\n    if (value === 0) {\n      this._zeroCount += increment;\n      return;\n    }\n\n    this._sum += value * increment;\n\n    if (value > 0) {\n      this._updateBuckets(this._positive, value, increment);\n    } else {\n      this._updateBuckets(this._negative, -value, increment);\n    }\n  }\n\n  /**\n   * merge combines data from previous value into self\n   * @param {ExponentialHistogramAccumulation} previous\n   */\n  merge(previous: ExponentialHistogramAccumulation) {\n    if (this._count === 0) {\n      this._min = previous.min;\n      this._max = previous.max;\n    } else if (previous.count !== 0) {\n      if (previous.min < this.min) {\n        this._min = previous.min;\n      }\n      if (previous.max > this.max) {\n        this._max = previous.max;\n      }\n    }\n\n    this.startTime = previous.startTime;\n    this._sum += previous.sum;\n    this._count += previous.count;\n    this._zeroCount += previous.zeroCount;\n\n    const minScale = this._minScale(previous);\n\n    this._downscale(this.scale - minScale);\n\n    this._mergeBuckets(this.positive, previous, previous.positive, minScale);\n    this._mergeBuckets(this.negative, previous, previous.negative, minScale);\n  }\n\n  /**\n   * diff subtracts other from self\n   * @param {ExponentialHistogramAccumulation} other\n   */\n  diff(other: ExponentialHistogramAccumulation) {\n    this._min = Infinity;\n    this._max = -Infinity;\n    this._sum -= other.sum;\n    this._count -= other.count;\n    this._zeroCount -= other.zeroCount;\n\n    const minScale = this._minScale(other);\n\n    this._downscale(this.scale - minScale);\n\n    this._diffBuckets(this.positive, other, other.positive, minScale);\n    this._diffBuckets(this.negative, other, other.negative, minScale);\n  }\n\n  /**\n   * clone returns a deep copy of self\n   * @returns {ExponentialHistogramAccumulation}\n   */\n  clone(): ExponentialHistogramAccumulation {\n    return new ExponentialHistogramAccumulation(\n      this.startTime,\n      this._maxSize,\n      this._recordMinMax,\n      this._sum,\n      this._count,\n      this._zeroCount,\n      this._min,\n      this._max,\n      this.positive.clone(),\n      this.negative.clone(),\n      this._mapping\n    );\n  }\n\n  /**\n   * _updateBuckets maps the incoming value to a bucket index for the current\n   * scale. If the bucket index is outside of the range of the backing array,\n   * it will rescale the backing array and update the mapping for the new scale.\n   */\n  private _updateBuckets(buckets: Buckets, value: number, increment: number) {\n    let index = this._mapping.mapToIndex(value);\n\n    // rescale the mapping if needed\n    let rescalingNeeded = false;\n    let high = 0;\n    let low = 0;\n\n    if (buckets.length === 0) {\n      buckets.indexStart = index;\n      buckets.indexEnd = buckets.indexStart;\n      buckets.indexBase = buckets.indexStart;\n    } else if (\n      index < buckets.indexStart &&\n      buckets.indexEnd - index >= this._maxSize\n    ) {\n      rescalingNeeded = true;\n      low = index;\n      high = buckets.indexEnd;\n    } else if (\n      index > buckets.indexEnd &&\n      index - buckets.indexStart >= this._maxSize\n    ) {\n      rescalingNeeded = true;\n      low = buckets.indexStart;\n      high = index;\n    }\n\n    // rescale and compute index at new scale\n    if (rescalingNeeded) {\n      const change = this._changeScale(high, low);\n      this._downscale(change);\n      index = this._mapping.mapToIndex(value);\n    }\n\n    this._incrementIndexBy(buckets, index, increment);\n  }\n\n  /**\n   * _incrementIndexBy increments the count of the bucket specified by `index`.\n   * If the index is outside of the range [buckets.indexStart, buckets.indexEnd]\n   * the boundaries of the backing array will be adjusted and more buckets will\n   * be added if needed.\n   */\n  private _incrementIndexBy(\n    buckets: Buckets,\n    index: number,\n    increment: number\n  ) {\n    if (increment === 0) {\n      // nothing to do for a zero increment, can happen during a merge operation\n      return;\n    }\n\n    if (buckets.length === 0) {\n      buckets.indexStart = buckets.indexEnd = buckets.indexBase = index;\n    }\n\n    if (index < buckets.indexStart) {\n      const span = buckets.indexEnd - index;\n      if (span >= buckets.backing.length) {\n        this._grow(buckets, span + 1);\n      }\n      buckets.indexStart = index;\n    } else if (index > buckets.indexEnd) {\n      const span = index - buckets.indexStart;\n      if (span >= buckets.backing.length) {\n        this._grow(buckets, span + 1);\n      }\n      buckets.indexEnd = index;\n    }\n\n    let bucketIndex = index - buckets.indexBase;\n    if (bucketIndex < 0) {\n      bucketIndex += buckets.backing.length;\n    }\n    buckets.incrementBucket(bucketIndex, increment);\n  }\n\n  /**\n   * grow resizes the backing array by doubling in size up to maxSize.\n   * This extends the array with a bunch of zeros and copies the\n   * existing counts to the same position.\n   */\n  private _grow(buckets: Buckets, needed: number) {\n    const size = buckets.backing.length;\n    const bias = buckets.indexBase - buckets.indexStart;\n    const oldPositiveLimit = size - bias;\n    let newSize = nextGreaterSquare(needed);\n    if (newSize > this._maxSize) {\n      newSize = this._maxSize;\n    }\n    const newPositiveLimit = newSize - bias;\n    buckets.backing.growTo(newSize, oldPositiveLimit, newPositiveLimit);\n  }\n\n  /**\n   * _changeScale computes how much downscaling is needed by shifting the\n   * high and low values until they are separated by no more than size.\n   */\n  private _changeScale(high: number, low: number): number {\n    let change = 0;\n    while (high - low >= this._maxSize) {\n      high >>= 1;\n      low >>= 1;\n      change++;\n    }\n    return change;\n  }\n\n  /**\n   * _downscale subtracts `change` from the current mapping scale.\n   */\n  private _downscale(change: number) {\n    if (change === 0) {\n      return;\n    }\n    if (change < 0) {\n      // Note: this should be impossible. If we get here it's because\n      // there is a bug in the implementation.\n      throw new Error(`impossible change of scale: ${this.scale}`);\n    }\n    const newScale = this._mapping.scale - change;\n\n    this._positive.downscale(change);\n    this._negative.downscale(change);\n\n    this._mapping = getMapping(newScale);\n  }\n\n  /**\n   * _minScale is used by diff and merge to compute an ideal combined scale\n   */\n  private _minScale(other: ExponentialHistogramAccumulation): number {\n    const minScale = Math.min(this.scale, other.scale);\n\n    const highLowPos = HighLow.combine(\n      this._highLowAtScale(this.positive, this.scale, minScale),\n      this._highLowAtScale(other.positive, other.scale, minScale)\n    );\n\n    const highLowNeg = HighLow.combine(\n      this._highLowAtScale(this.negative, this.scale, minScale),\n      this._highLowAtScale(other.negative, other.scale, minScale)\n    );\n\n    return Math.min(\n      minScale - this._changeScale(highLowPos.high, highLowPos.low),\n      minScale - this._changeScale(highLowNeg.high, highLowNeg.low)\n    );\n  }\n\n  /**\n   * _highLowAtScale is used by diff and merge to compute an ideal combined scale.\n   */\n  private _highLowAtScale(\n    buckets: Buckets,\n    currentScale: number,\n    newScale: number\n  ): HighLow {\n    if (buckets.length === 0) {\n      return new HighLow(0, -1);\n    }\n    const shift = currentScale - newScale;\n    return new HighLow(buckets.indexStart >> shift, buckets.indexEnd >> shift);\n  }\n\n  /**\n   * _mergeBuckets translates index values from another histogram and\n   * adds the values into the corresponding buckets of this histogram.\n   */\n  private _mergeBuckets(\n    ours: Buckets,\n    other: ExponentialHistogramAccumulation,\n    theirs: Buckets,\n    scale: number\n  ) {\n    const theirOffset = theirs.offset;\n    const theirChange = other.scale - scale;\n\n    for (let i = 0; i < theirs.length; i++) {\n      this._incrementIndexBy(\n        ours,\n        (theirOffset + i) >> theirChange,\n        theirs.at(i)\n      );\n    }\n  }\n\n  /**\n   * _diffBuckets translates index values from another histogram and\n   * subtracts the values in the corresponding buckets of this histogram.\n   */\n  private _diffBuckets(\n    ours: Buckets,\n    other: ExponentialHistogramAccumulation,\n    theirs: Buckets,\n    scale: number\n  ) {\n    const theirOffset = theirs.offset;\n    const theirChange = other.scale - scale;\n\n    for (let i = 0; i < theirs.length; i++) {\n      const ourIndex = (theirOffset + i) >> theirChange;\n      let bucketIndex = ourIndex - ours.indexBase;\n      if (bucketIndex < 0) {\n        bucketIndex += ours.backing.length;\n      }\n      ours.decrementBucket(bucketIndex, theirs.at(i));\n    }\n\n    ours.trim();\n  }\n}\n\n/**\n * Aggregator for ExponentialHistogramAccumulations\n */\nexport class ExponentialHistogramAggregator\n  implements Aggregator<ExponentialHistogramAccumulation>\n{\n  public kind: AggregatorKind.EXPONENTIAL_HISTOGRAM =\n    AggregatorKind.EXPONENTIAL_HISTOGRAM;\n\n  /**\n   * @param _maxSize Maximum number of buckets for each of the positive\n   *    and negative ranges, exclusive of the zero-bucket.\n   * @param _recordMinMax If set to true, min and max will be recorded.\n   *    Otherwise, min and max will not be recorded.\n   */\n  constructor(\n    readonly _maxSize: number,\n    private readonly _recordMinMax: boolean\n  ) {}\n\n  createAccumulation(startTime: HrTime) {\n    return new ExponentialHistogramAccumulation(\n      startTime,\n      this._maxSize,\n      this._recordMinMax\n    );\n  }\n\n  /**\n   * Return the result of the merge of two exponential histogram accumulations.\n   */\n  merge(\n    previous: ExponentialHistogramAccumulation,\n    delta: ExponentialHistogramAccumulation\n  ): ExponentialHistogramAccumulation {\n    const result = delta.clone();\n    result.merge(previous);\n\n    return result;\n  }\n\n  /**\n   * Returns a new DELTA aggregation by comparing two cumulative measurements.\n   */\n  diff(\n    previous: ExponentialHistogramAccumulation,\n    current: ExponentialHistogramAccumulation\n  ): ExponentialHistogramAccumulation {\n    const result = current.clone();\n    result.diff(previous);\n\n    return result;\n  }\n\n  toMetricData(\n    descriptor: MetricDescriptor,\n    aggregationTemporality: AggregationTemporality,\n    accumulationByAttributes: AccumulationRecord<ExponentialHistogramAccumulation>[],\n    endTime: HrTime\n  ): Maybe<ExponentialHistogramMetricData> {\n    return {\n      descriptor,\n      aggregationTemporality,\n      dataPointType: DataPointType.EXPONENTIAL_HISTOGRAM,\n      dataPoints: accumulationByAttributes.map(([attributes, accumulation]) => {\n        const pointValue = accumulation.toPointValue();\n\n        // determine if instrument allows negative values.\n        const allowsNegativeValues =\n          descriptor.type === InstrumentType.GAUGE ||\n          descriptor.type === InstrumentType.UP_DOWN_COUNTER ||\n          descriptor.type === InstrumentType.OBSERVABLE_GAUGE ||\n          descriptor.type === InstrumentType.OBSERVABLE_UP_DOWN_COUNTER;\n\n        return {\n          attributes,\n          startTime: accumulation.startTime,\n          endTime,\n          value: {\n            min: pointValue.hasMinMax ? pointValue.min : undefined,\n            max: pointValue.hasMinMax ? pointValue.max : undefined,\n            sum: !allowsNegativeValues ? pointValue.sum : undefined,\n            positive: {\n              offset: pointValue.positive.offset,\n              bucketCounts: pointValue.positive.bucketCounts,\n            },\n            negative: {\n              offset: pointValue.negative.offset,\n              bucketCounts: pointValue.negative.bucketCounts,\n            },\n            count: pointValue.count,\n            scale: pointValue.scale,\n            zeroCount: pointValue.zeroCount,\n          },\n        };\n      }),\n    };\n  }\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;;AAEH,OAAO,EAIL,cAAc,GAEf,MAAM,SAAS,CAAC;AACjB,OAAO,EACL,aAAa,GAGd,MAAM,sBAAsB,CAAC;AAC9B,OAAO,EAAE,IAAI,EAAU,MAAM,oBAAoB,CAAC;AAClD,OAAO,EAAE,cAAc,EAAE,MAAM,yBAAyB,CAAC;AAGzD,OAAO,EAAE,OAAO,EAAE,MAAM,iCAAiC,CAAC;AAC1D,OAAO,EAAE,UAAU,EAAE,MAAM,4CAA4C,CAAC;AAExE,OAAO,EAAE,iBAAiB,EAAE,MAAM,8BAA8B,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;AAcjE,mEAAmE;AACnE,0CAA0C;AAC1C,IAAA,UAAA;IAIE,SAAA,QACS,GAAW,EACX,IAAY;QADZ,IAAA,CAAA,GAAG,GAAH,GAAG,CAAQ;QACX,IAAA,CAAA,IAAI,GAAJ,IAAI,CAAQ;IAClB,CAAC;IANG,QAAA,OAAO,GAAd,SAAe,EAAW,EAAE,EAAW;QACrC,OAAO,IAAI,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,GAAG,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC;IAC3E,CAAC;IAKH,OAAA,OAAC;AAAD,CAAC,AARD,IAQC;AAED,IAAM,SAAS,GAAG,EAAE,CAAC;AACrB,IAAM,gBAAgB,GAAG,GAAG,CAAC;AAC7B,IAAM,YAAY,GAAG,CAAC,CAAC;AAEvB,IAAA,mCAAA;IACE,SAAA,iCACS,SAA6B,EAC5B,QAA2B,EAC3B,aAAoB,EACpB,IAAQ,EACR,MAAU,EACV,UAAc,EACd,IAA+B,EAC/B,IAA+B,EAC/B,SAAyB,EACzB,SAAyB,EACzB,QAAyC;QAV1C,IAAA,cAAA,KAAA,GAAA;YAAA,YAAA,SAA6B;QAAA;QAC5B,IAAA,aAAA,KAAA,GAAA;YAAA,WAAA,gBAA2B;QAAA;QAC3B,IAAA,kBAAA,KAAA,GAAA;YAAA,gBAAA,IAAoB;QAAA;QACpB,IAAA,SAAA,KAAA,GAAA;YAAA,OAAA,CAAQ;QAAA;QACR,IAAA,WAAA,KAAA,GAAA;YAAA,SAAA,CAAU;QAAA;QACV,IAAA,eAAA,KAAA,GAAA;YAAA,aAAA,CAAc;QAAA;QACd,IAAA,SAAA,KAAA,GAAA;YAAA,OAAO,MAAM,CAAC,iBAAiB;QAAA;QAC/B,IAAA,SAAA,KAAA,GAAA;YAAA,OAAO,MAAM,CAAC,iBAAiB;QAAA;QAC/B,IAAA,cAAA,KAAA,GAAA;YAAA,YAAA,gRAAgB,UAAO,EAAE;QAAA;QACzB,IAAA,cAAA,KAAA,GAAA;YAAA,YAAA,gRAAgB,UAAO,EAAE;QAAA;QACzB,IAAA,aAAA,KAAA,GAAA;YAAA,eAAoB,uSAAA,AAAU,EAAC,SAAS,CAAC;QAAA;QAV1C,IAAA,CAAA,SAAS,GAAT,SAAS,CAAoB;QAC5B,IAAA,CAAA,QAAQ,GAAR,QAAQ,CAAmB;QAC3B,IAAA,CAAA,aAAa,GAAb,aAAa,CAAO;QACpB,IAAA,CAAA,IAAI,GAAJ,IAAI,CAAI;QACR,IAAA,CAAA,MAAM,GAAN,MAAM,CAAI;QACV,IAAA,CAAA,UAAU,GAAV,UAAU,CAAI;QACd,IAAA,CAAA,IAAI,GAAJ,IAAI,CAA2B;QAC/B,IAAA,CAAA,IAAI,GAAJ,IAAI,CAA2B;QAC/B,IAAA,CAAA,SAAS,GAAT,SAAS,CAAgB;QACzB,IAAA,CAAA,SAAS,GAAT,SAAS,CAAgB;QACzB,IAAA,CAAA,QAAQ,GAAR,QAAQ,CAAiC;QAEjD,IAAI,IAAI,CAAC,QAAQ,GAAG,YAAY,EAAE;qLAChC,OAAI,CAAC,IAAI,CAAC,2CAAyC,IAAI,CAAC,QAAQ,GAAA,wDACnB,YAAc,CAAC,CAAC;YAC7D,IAAI,CAAC,QAAQ,GAAG,YAAY,CAAC;SAC9B;IACH,CAAC;IAED;;;OAGG,CACH,iCAAA,SAAA,CAAA,MAAM,GAAN,SAAO,KAAa;QAClB,IAAI,CAAC,iBAAiB,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;IACnC,CAAC;IAED;;;OAGG,CACH,iCAAA,SAAA,CAAA,YAAY,GAAZ,SAAa,SAAiB;QAC5B,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;IAC7B,CAAC;IAED;;;OAGG,CACH,iCAAA,SAAA,CAAA,YAAY,GAAZ;QACE,OAAO;YACL,SAAS,EAAE,IAAI,CAAC,aAAa;YAC7B,GAAG,EAAE,IAAI,CAAC,GAAG;YACb,GAAG,EAAE,IAAI,CAAC,GAAG;YACb,GAAG,EAAE,IAAI,CAAC,GAAG;YACb,QAAQ,EAAE;gBACR,MAAM,EAAE,IAAI,CAAC,QAAQ,CAAC,MAAM;gBAC5B,YAAY,EAAE,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE;aACrC;YACD,QAAQ,EAAE;gBACR,MAAM,EAAE,IAAI,CAAC,QAAQ,CAAC,MAAM;gBAC5B,YAAY,EAAE,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE;aACrC;YACD,KAAK,EAAE,IAAI,CAAC,KAAK;YACjB,KAAK,EAAE,IAAI,CAAC,KAAK;YACjB,SAAS,EAAE,IAAI,CAAC,SAAS;SAC1B,CAAC;IACJ,CAAC;IAKD,OAAA,cAAA,CAAI,iCAAA,SAAA,EAAA,KAAG,EAAA;QAHP;;WAEG,MACH;YACE,OAAO,IAAI,CAAC,IAAI,CAAC;QACnB,CAAC;;;OAAA;IAKD,OAAA,cAAA,CAAI,iCAAA,SAAA,EAAA,KAAG,EAAA;QAHP;;WAEG,MACH;YACE,OAAO,IAAI,CAAC,IAAI,CAAC;QACnB,CAAC;;;OAAA;IAKD,OAAA,cAAA,CAAI,iCAAA,SAAA,EAAA,KAAG,EAAA;QAHP;;WAEG,MACH;YACE,OAAO,IAAI,CAAC,IAAI,CAAC;QACnB,CAAC;;;OAAA;IAKD,OAAA,cAAA,CAAI,iCAAA,SAAA,EAAA,OAAK,EAAA;QAHT;;WAEG,MACH;YACE,OAAO,IAAI,CAAC,MAAM,CAAC;QACrB,CAAC;;;OAAA;IAKD,OAAA,cAAA,CAAI,iCAAA,SAAA,EAAA,WAAS,EAAA;QAHb;;WAEG,MACH;YACE,OAAO,IAAI,CAAC,UAAU,CAAC;QACzB,CAAC;;;OAAA;IAKD,OAAA,cAAA,CAAI,iCAAA,SAAA,EAAA,OAAK,EAAA;QAHT;;WAEG,MACH;YACE,IAAI,IAAI,CAAC,MAAM,KAAK,IAAI,CAAC,UAAU,EAAE;gBACnC,4CAA4C;gBAC5C,OAAO,CAAC,CAAC;aACV;YACD,OAAO,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC;QAC7B,CAAC;;;OAAA;IAMD,OAAA,cAAA,CAAI,iCAAA,SAAA,EAAA,UAAQ,EAAA;QAJZ;;;WAGG,MACH;YACE,OAAO,IAAI,CAAC,SAAS,CAAC;QACxB,CAAC;;;OAAA;IAMD,OAAA,cAAA,CAAI,iCAAA,SAAA,EAAA,UAAQ,EAAA;QAJZ;;;WAGG,MACH;YACE,OAAO,IAAI,CAAC,SAAS,CAAC;QACxB,CAAC;;;OAAA;IAED;;;;;OAKG,CACH,iCAAA,SAAA,CAAA,iBAAiB,GAAjB,SAAkB,KAAa,EAAE,SAAiB;QAChD,4EAA4E;QAC5E,qGAAqG;QACrG,IAAI,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE;YACvB,OAAO;SACR;QAED,IAAI,KAAK,GAAG,IAAI,CAAC,IAAI,EAAE;YACrB,IAAI,CAAC,IAAI,GAAG,KAAK,CAAC;SACnB;QACD,IAAI,KAAK,GAAG,IAAI,CAAC,IAAI,EAAE;YACrB,IAAI,CAAC,IAAI,GAAG,KAAK,CAAC;SACnB;QAED,IAAI,CAAC,MAAM,IAAI,SAAS,CAAC;QAEzB,IAAI,KAAK,KAAK,CAAC,EAAE;YACf,IAAI,CAAC,UAAU,IAAI,SAAS,CAAC;YAC7B,OAAO;SACR;QAED,IAAI,CAAC,IAAI,IAAI,KAAK,GAAG,SAAS,CAAC;QAE/B,IAAI,KAAK,GAAG,CAAC,EAAE;YACb,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,SAAS,EAAE,KAAK,EAAE,SAAS,CAAC,CAAC;SACvD,MAAM;YACL,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC,KAAK,EAAE,SAAS,CAAC,CAAC;SACxD;IACH,CAAC;IAED;;;OAGG,CACH,iCAAA,SAAA,CAAA,KAAK,GAAL,SAAM,QAA0C;QAC9C,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC,EAAE;YACrB,IAAI,CAAC,IAAI,GAAG,QAAQ,CAAC,GAAG,CAAC;YACzB,IAAI,CAAC,IAAI,GAAG,QAAQ,CAAC,GAAG,CAAC;SAC1B,MAAM,IAAI,QAAQ,CAAC,KAAK,KAAK,CAAC,EAAE;YAC/B,IAAI,QAAQ,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE;gBAC3B,IAAI,CAAC,IAAI,GAAG,QAAQ,CAAC,GAAG,CAAC;aAC1B;YACD,IAAI,QAAQ,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE;gBAC3B,IAAI,CAAC,IAAI,GAAG,QAAQ,CAAC,GAAG,CAAC;aAC1B;SACF;QAED,IAAI,CAAC,SAAS,GAAG,QAAQ,CAAC,SAAS,CAAC;QACpC,IAAI,CAAC,IAAI,IAAI,QAAQ,CAAC,GAAG,CAAC;QAC1B,IAAI,CAAC,MAAM,IAAI,QAAQ,CAAC,KAAK,CAAC;QAC9B,IAAI,CAAC,UAAU,IAAI,QAAQ,CAAC,SAAS,CAAC;QAEtC,IAAM,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC;QAE1C,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,KAAK,GAAG,QAAQ,CAAC,CAAC;QAEvC,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,QAAQ,EAAE,QAAQ,EAAE,QAAQ,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;QACzE,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,QAAQ,EAAE,QAAQ,EAAE,QAAQ,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;IAC3E,CAAC;IAED;;;OAGG,CACH,iCAAA,SAAA,CAAA,IAAI,GAAJ,SAAK,KAAuC;QAC1C,IAAI,CAAC,IAAI,GAAG,QAAQ,CAAC;QACrB,IAAI,CAAC,IAAI,GAAG,CAAC,QAAQ,CAAC;QACtB,IAAI,CAAC,IAAI,IAAI,KAAK,CAAC,GAAG,CAAC;QACvB,IAAI,CAAC,MAAM,IAAI,KAAK,CAAC,KAAK,CAAC;QAC3B,IAAI,CAAC,UAAU,IAAI,KAAK,CAAC,SAAS,CAAC;QAEnC,IAAM,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;QAEvC,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,KAAK,GAAG,QAAQ,CAAC,CAAC;QAEvC,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,QAAQ,EAAE,KAAK,EAAE,KAAK,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;QAClE,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,QAAQ,EAAE,KAAK,EAAE,KAAK,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;IACpE,CAAC;IAED;;;OAGG,CACH,iCAAA,SAAA,CAAA,KAAK,GAAL;QACE,OAAO,IAAI,gCAAgC,CACzC,IAAI,CAAC,SAAS,EACd,IAAI,CAAC,QAAQ,EACb,IAAI,CAAC,aAAa,EAClB,IAAI,CAAC,IAAI,EACT,IAAI,CAAC,MAAM,EACX,IAAI,CAAC,UAAU,EACf,IAAI,CAAC,IAAI,EACT,IAAI,CAAC,IAAI,EACT,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,EACrB,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,EACrB,IAAI,CAAC,QAAQ,CACd,CAAC;IACJ,CAAC;IAED;;;;OAIG,CACK,iCAAA,SAAA,CAAA,cAAc,GAAtB,SAAuB,OAAgB,EAAE,KAAa,EAAE,SAAiB;QACvE,IAAI,KAAK,GAAG,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;QAE5C,gCAAgC;QAChC,IAAI,eAAe,GAAG,KAAK,CAAC;QAC5B,IAAI,IAAI,GAAG,CAAC,CAAC;QACb,IAAI,GAAG,GAAG,CAAC,CAAC;QAEZ,IAAI,OAAO,CAAC,MAAM,KAAK,CAAC,EAAE;YACxB,OAAO,CAAC,UAAU,GAAG,KAAK,CAAC;YAC3B,OAAO,CAAC,QAAQ,GAAG,OAAO,CAAC,UAAU,CAAC;YACtC,OAAO,CAAC,SAAS,GAAG,OAAO,CAAC,UAAU,CAAC;SACxC,MAAM,IACL,KAAK,GAAG,OAAO,CAAC,UAAU,IAC1B,OAAO,CAAC,QAAQ,GAAG,KAAK,IAAI,IAAI,CAAC,QAAQ,EACzC;YACA,eAAe,GAAG,IAAI,CAAC;YACvB,GAAG,GAAG,KAAK,CAAC;YACZ,IAAI,GAAG,OAAO,CAAC,QAAQ,CAAC;SACzB,MAAM,IACL,KAAK,GAAG,OAAO,CAAC,QAAQ,IACxB,KAAK,GAAG,OAAO,CAAC,UAAU,IAAI,IAAI,CAAC,QAAQ,EAC3C;YACA,eAAe,GAAG,IAAI,CAAC;YACvB,GAAG,GAAG,OAAO,CAAC,UAAU,CAAC;YACzB,IAAI,GAAG,KAAK,CAAC;SACd;QAED,yCAAyC;QACzC,IAAI,eAAe,EAAE;YACnB,IAAM,MAAM,GAAG,IAAI,CAAC,YAAY,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC;YAC5C,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC;YACxB,KAAK,GAAG,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;SACzC;QAED,IAAI,CAAC,iBAAiB,CAAC,OAAO,EAAE,KAAK,EAAE,SAAS,CAAC,CAAC;IACpD,CAAC;IAED;;;;;OAKG,CACK,iCAAA,SAAA,CAAA,iBAAiB,GAAzB,SACE,OAAgB,EAChB,KAAa,EACb,SAAiB;QAEjB,IAAI,SAAS,KAAK,CAAC,EAAE;YACnB,0EAA0E;YAC1E,OAAO;SACR;QAED,IAAI,OAAO,CAAC,MAAM,KAAK,CAAC,EAAE;YACxB,OAAO,CAAC,UAAU,GAAG,OAAO,CAAC,QAAQ,GAAG,OAAO,CAAC,SAAS,GAAG,KAAK,CAAC;SACnE;QAED,IAAI,KAAK,GAAG,OAAO,CAAC,UAAU,EAAE;YAC9B,IAAM,IAAI,GAAG,OAAO,CAAC,QAAQ,GAAG,KAAK,CAAC;YACtC,IAAI,IAAI,IAAI,OAAO,CAAC,OAAO,CAAC,MAAM,EAAE;gBAClC,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE,IAAI,GAAG,CAAC,CAAC,CAAC;aAC/B;YACD,OAAO,CAAC,UAAU,GAAG,KAAK,CAAC;SAC5B,MAAM,IAAI,KAAK,GAAG,OAAO,CAAC,QAAQ,EAAE;YACnC,IAAM,IAAI,GAAG,KAAK,GAAG,OAAO,CAAC,UAAU,CAAC;YACxC,IAAI,IAAI,IAAI,OAAO,CAAC,OAAO,CAAC,MAAM,EAAE;gBAClC,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE,IAAI,GAAG,CAAC,CAAC,CAAC;aAC/B;YACD,OAAO,CAAC,QAAQ,GAAG,KAAK,CAAC;SAC1B;QAED,IAAI,WAAW,GAAG,KAAK,GAAG,OAAO,CAAC,SAAS,CAAC;QAC5C,IAAI,WAAW,GAAG,CAAC,EAAE;YACnB,WAAW,IAAI,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC;SACvC;QACD,OAAO,CAAC,eAAe,CAAC,WAAW,EAAE,SAAS,CAAC,CAAC;IAClD,CAAC;IAED;;;;OAIG,CACK,iCAAA,SAAA,CAAA,KAAK,GAAb,SAAc,OAAgB,EAAE,MAAc;QAC5C,IAAM,IAAI,GAAG,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC;QACpC,IAAM,IAAI,GAAG,OAAO,CAAC,SAAS,GAAG,OAAO,CAAC,UAAU,CAAC;QACpD,IAAM,gBAAgB,GAAG,IAAI,GAAG,IAAI,CAAC;QACrC,IAAI,OAAO,gRAAG,oBAAA,AAAiB,EAAC,MAAM,CAAC,CAAC;QACxC,IAAI,OAAO,GAAG,IAAI,CAAC,QAAQ,EAAE;YAC3B,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC;SACzB;QACD,IAAM,gBAAgB,GAAG,OAAO,GAAG,IAAI,CAAC;QACxC,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC,OAAO,EAAE,gBAAgB,EAAE,gBAAgB,CAAC,CAAC;IACtE,CAAC;IAED;;;OAGG,CACK,iCAAA,SAAA,CAAA,YAAY,GAApB,SAAqB,IAAY,EAAE,GAAW;QAC5C,IAAI,MAAM,GAAG,CAAC,CAAC;QACf,MAAO,IAAI,GAAG,GAAG,IAAI,IAAI,CAAC,QAAQ,CAAE;YAClC,IAAI,KAAK,CAAC,CAAC;YACX,GAAG,KAAK,CAAC,CAAC;YACV,MAAM,EAAE,CAAC;SACV;QACD,OAAO,MAAM,CAAC;IAChB,CAAC;IAED;;OAEG,CACK,iCAAA,SAAA,CAAA,UAAU,GAAlB,SAAmB,MAAc;QAC/B,IAAI,MAAM,KAAK,CAAC,EAAE;YAChB,OAAO;SACR;QACD,IAAI,MAAM,GAAG,CAAC,EAAE;YACd,+DAA+D;YAC/D,wCAAwC;YACxC,MAAM,IAAI,KAAK,CAAC,iCAA+B,IAAI,CAAC,KAAO,CAAC,CAAC;SAC9D;QACD,IAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,KAAK,GAAG,MAAM,CAAC;QAE9C,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;QACjC,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;QAEjC,IAAI,CAAC,QAAQ,GAAG,2SAAA,AAAU,EAAC,QAAQ,CAAC,CAAC;IACvC,CAAC;IAED;;OAEG,CACK,iCAAA,SAAA,CAAA,SAAS,GAAjB,SAAkB,KAAuC;QACvD,IAAM,QAAQ,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC;QAEnD,IAAM,UAAU,GAAG,OAAO,CAAC,OAAO,CAChC,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,KAAK,EAAE,QAAQ,CAAC,EACzD,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,QAAQ,EAAE,KAAK,CAAC,KAAK,EAAE,QAAQ,CAAC,CAC5D,CAAC;QAEF,IAAM,UAAU,GAAG,OAAO,CAAC,OAAO,CAChC,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,KAAK,EAAE,QAAQ,CAAC,EACzD,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,QAAQ,EAAE,KAAK,CAAC,KAAK,EAAE,QAAQ,CAAC,CAC5D,CAAC;QAEF,OAAO,IAAI,CAAC,GAAG,CACb,QAAQ,GAAG,IAAI,CAAC,YAAY,CAAC,UAAU,CAAC,IAAI,EAAE,UAAU,CAAC,GAAG,CAAC,EAC7D,QAAQ,GAAG,IAAI,CAAC,YAAY,CAAC,UAAU,CAAC,IAAI,EAAE,UAAU,CAAC,GAAG,CAAC,CAC9D,CAAC;IACJ,CAAC;IAED;;OAEG,CACK,iCAAA,SAAA,CAAA,eAAe,GAAvB,SACE,OAAgB,EAChB,YAAoB,EACpB,QAAgB;QAEhB,IAAI,OAAO,CAAC,MAAM,KAAK,CAAC,EAAE;YACxB,OAAO,IAAI,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;SAC3B;QACD,IAAM,KAAK,GAAG,YAAY,GAAG,QAAQ,CAAC;QACtC,OAAO,IAAI,OAAO,CAAC,OAAO,CAAC,UAAU,IAAI,KAAK,EAAE,OAAO,CAAC,QAAQ,IAAI,KAAK,CAAC,CAAC;IAC7E,CAAC;IAED;;;OAGG,CACK,iCAAA,SAAA,CAAA,aAAa,GAArB,SACE,IAAa,EACb,KAAuC,EACvC,MAAe,EACf,KAAa;QAEb,IAAM,WAAW,GAAG,MAAM,CAAC,MAAM,CAAC;QAClC,IAAM,WAAW,GAAG,KAAK,CAAC,KAAK,GAAG,KAAK,CAAC;QAExC,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC,EAAE,CAAE;YACtC,IAAI,CAAC,iBAAiB,CACpB,IAAI,EACJ,AAAC,WAAW,GAAG,CAAC,CAAC,GAAI,WAAW,EAChC,MAAM,CAAC,EAAE,CAAC,CAAC,CAAC,CACb,CAAC;SACH;IACH,CAAC;IAED;;;OAGG,CACK,iCAAA,SAAA,CAAA,YAAY,GAApB,SACE,IAAa,EACb,KAAuC,EACvC,MAAe,EACf,KAAa;QAEb,IAAM,WAAW,GAAG,MAAM,CAAC,MAAM,CAAC;QAClC,IAAM,WAAW,GAAG,KAAK,CAAC,KAAK,GAAG,KAAK,CAAC;QAExC,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC,EAAE,CAAE;YACtC,IAAM,QAAQ,GAAG,AAAC,WAAW,GAAG,CAAC,CAAC,GAAI,WAAW,CAAC;YAClD,IAAI,WAAW,GAAG,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC;YAC5C,IAAI,WAAW,GAAG,CAAC,EAAE;gBACnB,WAAW,IAAI,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC;aACpC;YACD,IAAI,CAAC,eAAe,CAAC,WAAW,EAAE,MAAM,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;SACjD;QAED,IAAI,CAAC,IAAI,EAAE,CAAC;IACd,CAAC;IACH,OAAA,gCAAC;AAAD,CAAC,AA9bD,IA8bC;;AAED;;GAEG,CACH,IAAA,iCAAA;IAME;;;;;OAKG,CACH,SAAA,+BACW,QAAgB,EACR,aAAsB;QAD9B,IAAA,CAAA,QAAQ,GAAR,QAAQ,CAAQ;QACR,IAAA,CAAA,aAAa,GAAb,aAAa,CAAS;QAXlC,IAAA,CAAA,IAAI,iPACT,iBAAc,CAAC,qBAAqB,CAAC;IAWpC,CAAC;IAEJ,+BAAA,SAAA,CAAA,kBAAkB,GAAlB,SAAmB,SAAiB;QAClC,OAAO,IAAI,gCAAgC,CACzC,SAAS,EACT,IAAI,CAAC,QAAQ,EACb,IAAI,CAAC,aAAa,CACnB,CAAC;IACJ,CAAC;IAED;;OAEG,CACH,+BAAA,SAAA,CAAA,KAAK,GAAL,SACE,QAA0C,EAC1C,KAAuC;QAEvC,IAAM,MAAM,GAAG,KAAK,CAAC,KAAK,EAAE,CAAC;QAC7B,MAAM,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;QAEvB,OAAO,MAAM,CAAC;IAChB,CAAC;IAED;;OAEG,CACH,+BAAA,SAAA,CAAA,IAAI,GAAJ,SACE,QAA0C,EAC1C,OAAyC;QAEzC,IAAM,MAAM,GAAG,OAAO,CAAC,KAAK,EAAE,CAAC;QAC/B,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QAEtB,OAAO,MAAM,CAAC;IAChB,CAAC;IAED,+BAAA,SAAA,CAAA,YAAY,GAAZ,SACE,UAA4B,EAC5B,sBAA8C,EAC9C,wBAAgF,EAChF,OAAe;QAEf,OAAO;YACL,UAAU,EAAA,UAAA;YACV,sBAAsB,EAAA,sBAAA;YACtB,aAAa,iPAAE,gBAAa,CAAC,qBAAqB;YAClD,UAAU,EAAE,wBAAwB,CAAC,GAAG,CAAC,SAAC,EAA0B;oBAA1B,KAAA,OAAA,IAAA,EAA0B,EAAzB,UAAU,GAAA,EAAA,CAAA,EAAA,EAAE,YAAY,GAAA,EAAA,CAAA,EAAA;gBACjE,IAAM,UAAU,GAAG,YAAY,CAAC,YAAY,EAAE,CAAC;gBAE/C,kDAAkD;gBAClD,IAAM,oBAAoB,GACxB,UAAU,CAAC,IAAI,oPAAK,iBAAc,CAAC,KAAK,IACxC,UAAU,CAAC,IAAI,oPAAK,iBAAc,CAAC,eAAe,IAClD,UAAU,CAAC,IAAI,mPAAK,kBAAc,CAAC,gBAAgB,IACnD,UAAU,CAAC,IAAI,oPAAK,iBAAc,CAAC,0BAA0B,CAAC;gBAEhE,OAAO;oBACL,UAAU,EAAA,UAAA;oBACV,SAAS,EAAE,YAAY,CAAC,SAAS;oBACjC,OAAO,EAAA,OAAA;oBACP,KAAK,EAAE;wBACL,GAAG,EAAE,UAAU,CAAC,SAAS,CAAC,CAAC,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC,SAAS;wBACtD,GAAG,EAAE,UAAU,CAAC,SAAS,CAAC,CAAC,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC,SAAS;wBACtD,GAAG,EAAE,CAAC,oBAAoB,CAAC,CAAC,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC,SAAS;wBACvD,QAAQ,EAAE;4BACR,MAAM,EAAE,UAAU,CAAC,QAAQ,CAAC,MAAM;4BAClC,YAAY,EAAE,UAAU,CAAC,QAAQ,CAAC,YAAY;yBAC/C;wBACD,QAAQ,EAAE;4BACR,MAAM,EAAE,UAAU,CAAC,QAAQ,CAAC,MAAM;4BAClC,YAAY,EAAE,UAAU,CAAC,QAAQ,CAAC,YAAY;yBAC/C;wBACD,KAAK,EAAE,UAAU,CAAC,KAAK;wBACvB,KAAK,EAAE,UAAU,CAAC,KAAK;wBACvB,SAAS,EAAE,UAAU,CAAC,SAAS;qBAChC;iBACF,CAAC;YACJ,CAAC,CAAC;SACH,CAAC;IACJ,CAAC;IACH,OAAA,8BAAC;AAAD,CAAC,AA/FD,IA+FC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2392, "column": 0}, "map": {"version": 3, "file": "Aggregation.js", "sourceRoot": "", "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/node_modules/%40opentelemetry/sdk-node/node_modules/%40opentelemetry/sdk-metrics/src/view/Aggregation.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport * as api from '@opentelemetry/api';\nimport {\n  Aggregator,\n  SumAggregator,\n  DropAggregator,\n  LastValueAggregator,\n  HistogramAggregator,\n  ExponentialHistogramAggregator,\n} from '../aggregator';\nimport { Accumulation } from '../aggregator/types';\nimport { InstrumentDescriptor, InstrumentType } from '../InstrumentDescriptor';\nimport { Maybe } from '../utils';\n\n/**\n * Configures how measurements are combined into metrics for views.\n *\n * Aggregation provides a set of built-in aggregations via static methods.\n */\nexport abstract class Aggregation {\n  abstract createAggregator(\n    instrument: InstrumentDescriptor\n  ): Aggregator<Maybe<Accumulation>>;\n\n  static Drop(): Aggregation {\n    return DROP_AGGREGATION;\n  }\n\n  static Sum(): Aggregation {\n    return SUM_AGGREGATION;\n  }\n\n  static LastValue(): Aggregation {\n    return LAST_VALUE_AGGREGATION;\n  }\n\n  static Histogram(): Aggregation {\n    return HISTOGRAM_AGGREGATION;\n  }\n\n  static ExponentialHistogram(): Aggregation {\n    return EXPONENTIAL_HISTOGRAM_AGGREGATION;\n  }\n\n  static Default(): Aggregation {\n    return DEFAULT_AGGREGATION;\n  }\n}\n\n/**\n * The default drop aggregation.\n */\nexport class DropAggregation extends Aggregation {\n  private static DEFAULT_INSTANCE = new DropAggregator();\n  createAggregator(_instrument: InstrumentDescriptor) {\n    return DropAggregation.DEFAULT_INSTANCE;\n  }\n}\n\n/**\n * The default sum aggregation.\n */\nexport class SumAggregation extends Aggregation {\n  private static MONOTONIC_INSTANCE = new SumAggregator(true);\n  private static NON_MONOTONIC_INSTANCE = new SumAggregator(false);\n  createAggregator(instrument: InstrumentDescriptor) {\n    switch (instrument.type) {\n      case InstrumentType.COUNTER:\n      case InstrumentType.OBSERVABLE_COUNTER:\n      case InstrumentType.HISTOGRAM: {\n        return SumAggregation.MONOTONIC_INSTANCE;\n      }\n      default: {\n        return SumAggregation.NON_MONOTONIC_INSTANCE;\n      }\n    }\n  }\n}\n\n/**\n * The default last value aggregation.\n */\nexport class LastValueAggregation extends Aggregation {\n  private static DEFAULT_INSTANCE = new LastValueAggregator();\n  createAggregator(_instrument: InstrumentDescriptor) {\n    return LastValueAggregation.DEFAULT_INSTANCE;\n  }\n}\n\n/**\n * The default histogram aggregation.\n */\nexport class HistogramAggregation extends Aggregation {\n  private static DEFAULT_INSTANCE = new HistogramAggregator(\n    [0, 5, 10, 25, 50, 75, 100, 250, 500, 750, 1000, 2500, 5000, 7500, 10000],\n    true\n  );\n  createAggregator(_instrument: InstrumentDescriptor) {\n    return HistogramAggregation.DEFAULT_INSTANCE;\n  }\n}\n\n/**\n * The explicit bucket histogram aggregation.\n */\nexport class ExplicitBucketHistogramAggregation extends Aggregation {\n  private _boundaries: number[];\n\n  /**\n   * @param boundaries the bucket boundaries of the histogram aggregation\n   * @param _recordMinMax If set to true, min and max will be recorded. Otherwise, min and max will not be recorded.\n   */\n  constructor(\n    boundaries: number[],\n    private readonly _recordMinMax = true\n  ) {\n    super();\n    if (boundaries == null) {\n      throw new Error(\n        'ExplicitBucketHistogramAggregation should be created with explicit boundaries, if a single bucket histogram is required, please pass an empty array'\n      );\n    }\n    // Copy the boundaries array for modification.\n    boundaries = boundaries.concat();\n    // We need to an ordered set to be able to correctly compute count for each\n    // boundary since we'll iterate on each in order.\n    boundaries = boundaries.sort((a, b) => a - b);\n    // Remove all Infinity from the boundaries.\n    const minusInfinityIndex = boundaries.lastIndexOf(-Infinity);\n    let infinityIndex: number | undefined = boundaries.indexOf(Infinity);\n    if (infinityIndex === -1) {\n      infinityIndex = undefined;\n    }\n    this._boundaries = boundaries.slice(minusInfinityIndex + 1, infinityIndex);\n  }\n\n  createAggregator(_instrument: InstrumentDescriptor) {\n    return new HistogramAggregator(this._boundaries, this._recordMinMax);\n  }\n}\n\nexport class ExponentialHistogramAggregation extends Aggregation {\n  constructor(\n    private readonly _maxSize: number = 160,\n    private readonly _recordMinMax = true\n  ) {\n    super();\n  }\n  createAggregator(_instrument: InstrumentDescriptor) {\n    return new ExponentialHistogramAggregator(\n      this._maxSize,\n      this._recordMinMax\n    );\n  }\n}\n\n/**\n * The default aggregation.\n */\nexport class DefaultAggregation extends Aggregation {\n  private _resolve(instrument: InstrumentDescriptor): Aggregation {\n    // cast to unknown to disable complaints on the (unreachable) fallback.\n    switch (instrument.type as unknown) {\n      case InstrumentType.COUNTER:\n      case InstrumentType.UP_DOWN_COUNTER:\n      case InstrumentType.OBSERVABLE_COUNTER:\n      case InstrumentType.OBSERVABLE_UP_DOWN_COUNTER: {\n        return SUM_AGGREGATION;\n      }\n      case InstrumentType.GAUGE:\n      case InstrumentType.OBSERVABLE_GAUGE: {\n        return LAST_VALUE_AGGREGATION;\n      }\n      case InstrumentType.HISTOGRAM: {\n        if (instrument.advice.explicitBucketBoundaries) {\n          return new ExplicitBucketHistogramAggregation(\n            instrument.advice.explicitBucketBoundaries\n          );\n        }\n        return HISTOGRAM_AGGREGATION;\n      }\n    }\n    api.diag.warn(`Unable to recognize instrument type: ${instrument.type}`);\n    return DROP_AGGREGATION;\n  }\n\n  createAggregator(\n    instrument: InstrumentDescriptor\n  ): Aggregator<Maybe<Accumulation>> {\n    return this._resolve(instrument).createAggregator(instrument);\n  }\n}\n\nconst DROP_AGGREGATION = new DropAggregation();\nconst SUM_AGGREGATION = new SumAggregation();\nconst LAST_VALUE_AGGREGATION = new LastValueAggregation();\nconst HISTOGRAM_AGGREGATION = new HistogramAggregation();\nconst EXPONENTIAL_HISTOGRAM_AGGREGATION = new ExponentialHistogramAggregation();\nconst DEFAULT_AGGREGATION = new DefaultAggregation();\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;;;;;;;;AAEH,OAAO,KAAK,GAAG,MAAM,oBAAoB,CAAC;;;;;AAC1C,OAAO,EAEL,aAAa,EACb,cAAc,EACd,mBAAmB,EACnB,mBAAmB,EACnB,8BAA8B,GAC/B,MAAM,eAAe,CAAC;AAEvB,OAAO,EAAwB,cAAc,EAAE,MAAM,yBAAyB,CAAC;;;;;;;;;;;;;;;;;;;;;;;;AAG/E;;;;GAIG,CACH,IAAA,cAAA;IAAA,SAAA,eA4BA,CAAC;IAvBQ,YAAA,IAAI,GAAX;QACE,OAAO,gBAAgB,CAAC;IAC1B,CAAC;IAEM,YAAA,GAAG,GAAV;QACE,OAAO,eAAe,CAAC;IACzB,CAAC;IAEM,YAAA,SAAS,GAAhB;QACE,OAAO,sBAAsB,CAAC;IAChC,CAAC;IAEM,YAAA,SAAS,GAAhB;QACE,OAAO,qBAAqB,CAAC;IAC/B,CAAC;IAEM,YAAA,oBAAoB,GAA3B;QACE,OAAO,iCAAiC,CAAC;IAC3C,CAAC;IAEM,YAAA,OAAO,GAAd;QACE,OAAO,mBAAmB,CAAC;IAC7B,CAAC;IACH,OAAA,WAAC;AAAD,CAAC,AA5BD,IA4BC;;AAED;;GAEG,CACH,IAAA,kBAAA,SAAA,MAAA;IAAqC,UAAA,iBAAA,QAAW;IAAhD,SAAA;;IAKA,CAAC;IAHC,gBAAA,SAAA,CAAA,gBAAgB,GAAhB,SAAiB,WAAiC;QAChD,OAAO,eAAe,CAAC,gBAAgB,CAAC;IAC1C,CAAC;IAHc,gBAAA,gBAAgB,GAAG,iPAAI,iBAAc,EAAE,CAAC;IAIzD,OAAA,eAAC;CAAA,AALD,CAAqC,WAAW,GAK/C;;AAED;;GAEG,CACH,IAAA,iBAAA,SAAA,MAAA;IAAoC,UAAA,gBAAA,QAAW;IAA/C,SAAA;;IAeA,CAAC;IAZC,eAAA,SAAA,CAAA,gBAAgB,GAAhB,SAAiB,UAAgC;QAC/C,OAAQ,UAAU,CAAC,IAAI,EAAE;YACvB,oPAAK,iBAAc,CAAC,OAAO,CAAC;YAC5B,oPAAK,iBAAc,CAAC,kBAAkB,CAAC;YACvC,oPAAK,iBAAc,CAAC,SAAS,CAAC;gBAAC;oBAC7B,OAAO,cAAc,CAAC,kBAAkB,CAAC;iBAC1C;YACD,OAAO,CAAC;gBAAC;oBACP,OAAO,cAAc,CAAC,sBAAsB,CAAC;iBAC9C;SACF;IACH,CAAC;IAbc,eAAA,kBAAkB,GAAG,gPAAI,gBAAa,CAAC,IAAI,CAAC,CAAC;IAC7C,eAAA,sBAAsB,GAAG,gPAAI,gBAAa,CAAC,KAAK,CAAC,CAAC;IAanE,OAAA,cAAC;CAAA,AAfD,CAAoC,WAAW,GAe9C;;AAED;;GAEG,CACH,IAAA,uBAAA,SAAA,MAAA;IAA0C,UAAA,sBAAA,QAAW;IAArD,SAAA;;IAKA,CAAC;IAHC,qBAAA,SAAA,CAAA,gBAAgB,GAAhB,SAAiB,WAAiC;QAChD,OAAO,oBAAoB,CAAC,gBAAgB,CAAC;IAC/C,CAAC;IAHc,qBAAA,gBAAgB,GAAG,sPAAI,sBAAmB,EAAE,CAAC;IAI9D,OAAA,oBAAC;CAAA,AALD,CAA0C,WAAW,GAKpD;;AAED;;GAEG,CACH,IAAA,uBAAA,SAAA,MAAA;IAA0C,UAAA,sBAAA,QAAW;IAArD,SAAA;;IAQA,CAAC;IAHC,qBAAA,SAAA,CAAA,gBAAgB,GAAhB,SAAiB,WAAiC;QAChD,OAAO,oBAAoB,CAAC,gBAAgB,CAAC;IAC/C,CAAC;IANc,qBAAA,gBAAgB,GAAG,sPAAI,sBAAmB,CACvD;QAAC,CAAC;QAAE,CAAC;QAAE,EAAE;QAAE,EAAE;QAAE,EAAE;QAAE,EAAE;QAAE,GAAG;QAAE,GAAG;QAAE,GAAG;QAAE,GAAG;QAAE,IAAI;QAAE,IAAI;QAAE,IAAI;QAAE,IAAI;QAAE,KAAK;KAAC,EACzE,IAAI,CACL,CAAC;IAIJ,OAAA,oBAAC;CAAA,AARD,CAA0C,WAAW,GAQpD;;AAED;;GAEG,CACH,IAAA,qCAAA,SAAA,MAAA;IAAwD,UAAA,oCAAA,QAAW;IAGjE;;;OAGG,CACH,SAAA,mCACE,UAAoB,EACH,aAAoB;QAApB,IAAA,kBAAA,KAAA,GAAA;YAAA,gBAAA,IAAoB;QAAA;QAFvC,IAAA,QAIE,OAAA,IAAA,CAAA,IAAA,CAAO,IAAA,IAAA,CAkBR;QApBkB,MAAA,aAAa,GAAb,aAAa,CAAO;QAGrC,IAAI,UAAU,IAAI,IAAI,EAAE;YACtB,MAAM,IAAI,KAAK,CACb,qJAAqJ,CACtJ,CAAC;SACH;QACD,8CAA8C;QAC9C,UAAU,GAAG,UAAU,CAAC,MAAM,EAAE,CAAC;QACjC,2EAA2E;QAC3E,iDAAiD;QACjD,UAAU,GAAG,UAAU,CAAC,IAAI,CAAC,SAAC,CAAC,EAAE,CAAC;YAAK,OAAA,CAAC,GAAG,CAAC;QAAL,CAAK,CAAC,CAAC;QAC9C,2CAA2C;QAC3C,IAAM,kBAAkB,GAAG,UAAU,CAAC,WAAW,CAAC,CAAC,QAAQ,CAAC,CAAC;QAC7D,IAAI,aAAa,GAAuB,UAAU,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;QACrE,IAAI,aAAa,KAAK,CAAC,CAAC,EAAE;YACxB,aAAa,GAAG,SAAS,CAAC;SAC3B;QACD,KAAI,CAAC,WAAW,GAAG,UAAU,CAAC,KAAK,CAAC,kBAAkB,GAAG,CAAC,EAAE,aAAa,CAAC,CAAC;;IAC7E,CAAC;IAED,mCAAA,SAAA,CAAA,gBAAgB,GAAhB,SAAiB,WAAiC;QAChD,OAAO,sPAAI,sBAAmB,CAAC,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC;IACvE,CAAC;IACH,OAAA,kCAAC;AAAD,CAAC,AAlCD,CAAwD,WAAW,GAkClE;;AAED,IAAA,kCAAA,SAAA,MAAA;IAAqD,UAAA,iCAAA,QAAW;IAC9D,SAAA,gCACmB,QAAsB,EACtB,aAAoB;QADpB,IAAA,aAAA,KAAA,GAAA;YAAA,WAAA,GAAsB;QAAA;QACtB,IAAA,kBAAA,KAAA,GAAA;YAAA,gBAAA,IAAoB;QAAA;QAFvC,IAAA,QAIE,OAAA,IAAA,CAAA,IAAA,CAAO,IAAA,IAAA,CACR;QAJkB,MAAA,QAAQ,GAAR,QAAQ,CAAc;QACtB,MAAA,aAAa,GAAb,aAAa,CAAO;;IAGvC,CAAC;IACD,gCAAA,SAAA,CAAA,gBAAgB,GAAhB,SAAiB,WAAiC;QAChD,OAAO,iQAAI,iCAA8B,CACvC,IAAI,CAAC,QAAQ,EACb,IAAI,CAAC,aAAa,CACnB,CAAC;IACJ,CAAC;IACH,OAAA,+BAAC;AAAD,CAAC,AAbD,CAAqD,WAAW,GAa/D;;AAED;;GAEG,CACH,IAAA,qBAAA,SAAA,MAAA;IAAwC,UAAA,oBAAA,QAAW;IAAnD,SAAA;;IAgCA,CAAC;IA/BS,mBAAA,SAAA,CAAA,QAAQ,GAAhB,SAAiB,UAAgC;QAC/C,uEAAuE;QACvE,OAAQ,UAAU,CAAC,IAAe,EAAE;YAClC,KAAK,gQAAc,CAAC,OAAO,CAAC;YAC5B,oPAAK,iBAAc,CAAC,eAAe,CAAC;YACpC,oPAAK,iBAAc,CAAC,kBAAkB,CAAC;YACvC,oPAAK,iBAAc,CAAC,0BAA0B,CAAC;gBAAC;oBAC9C,OAAO,eAAe,CAAC;iBACxB;YACD,oPAAK,iBAAc,CAAC,KAAK,CAAC;YAC1B,oPAAK,iBAAc,CAAC,gBAAgB,CAAC;gBAAC;oBACpC,OAAO,sBAAsB,CAAC;iBAC/B;YACD,oPAAK,iBAAc,CAAC,SAAS,CAAC;gBAAC;oBAC7B,IAAI,UAAU,CAAC,MAAM,CAAC,wBAAwB,EAAE;wBAC9C,OAAO,IAAI,kCAAkC,CAC3C,UAAU,CAAC,MAAM,CAAC,wBAAwB,CAC3C,CAAC;qBACH;oBACD,OAAO,qBAAqB,CAAC;iBAC9B;SACF;iLACD,GAAG,CAAC,GAAI,CAAC,IAAI,CAAC,0CAAwC,UAAU,CAAC,IAAM,CAAC,CAAC;QACzE,OAAO,gBAAgB,CAAC;IAC1B,CAAC;IAED,mBAAA,SAAA,CAAA,gBAAgB,GAAhB,SACE,UAAgC;QAEhC,OAAO,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC,gBAAgB,CAAC,UAAU,CAAC,CAAC;IAChE,CAAC;IACH,OAAA,kBAAC;AAAD,CAAC,AAhCD,CAAwC,WAAW,GAgClD;;AAED,IAAM,gBAAgB,GAAG,IAAI,eAAe,EAAE,CAAC;AAC/C,IAAM,eAAe,GAAG,IAAI,cAAc,EAAE,CAAC;AAC7C,IAAM,sBAAsB,GAAG,IAAI,oBAAoB,EAAE,CAAC;AAC1D,IAAM,qBAAqB,GAAG,IAAI,oBAAoB,EAAE,CAAC;AACzD,IAAM,iCAAiC,GAAG,IAAI,+BAA+B,EAAE,CAAC;AAChF,IAAM,mBAAmB,GAAG,IAAI,kBAAkB,EAAE,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2666, "column": 0}, "map": {"version": 3, "file": "AggregationSelector.js", "sourceRoot": "", "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/node_modules/%40opentelemetry/sdk-node/node_modules/%40opentelemetry/sdk-metrics/src/export/AggregationSelector.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { InstrumentType } from '../InstrumentDescriptor';\nimport { Aggregation } from '../view/Aggregation';\nimport { AggregationTemporality } from './AggregationTemporality';\n\n/**\n * Aggregation selector based on metric instrument types.\n */\nexport type AggregationSelector = (\n  instrumentType: InstrumentType\n) => Aggregation;\n\n/**\n * Aggregation temporality selector based on metric instrument types.\n */\nexport type AggregationTemporalitySelector = (\n  instrumentType: InstrumentType\n) => AggregationTemporality;\n\nexport const DEFAULT_AGGREGATION_SELECTOR: AggregationSelector =\n  _instrumentType => Aggregation.Default();\nexport const DEFAULT_AGGREGATION_TEMPORALITY_SELECTOR: AggregationTemporalitySelector =\n  _instrumentType => AggregationTemporality.CUMULATIVE;\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;;AAGH,OAAO,EAAE,WAAW,EAAE,MAAM,qBAAqB,CAAC;AAClD,OAAO,EAAE,sBAAsB,EAAE,MAAM,0BAA0B,CAAC;;;AAgB3D,IAAM,4BAA4B,GACvC,SAAA,eAAe;IAAI,qPAAA,cAAW,CAAC,OAAO,EAAE;AAArB,CAAqB,CAAC;AACpC,IAAM,wCAAwC,GACnD,SAAA,eAAe;IAAI,kQAAA,yBAAsB,CAAC,UAAU;AAAjC,CAAiC,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2700, "column": 0}, "map": {"version": 3, "file": "MetricReader.js", "sourceRoot": "", "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/node_modules/%40opentelemetry/sdk-node/node_modules/%40opentelemetry/sdk-metrics/src/export/MetricReader.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport * as api from '@opentelemetry/api';\nimport { AggregationTemporality } from './AggregationTemporality';\nimport { MetricProducer } from './MetricProducer';\nimport { CollectionResult } from './MetricData';\nimport { FlatMap, callWithTimeout } from '../utils';\nimport { InstrumentType } from '../InstrumentDescriptor';\nimport {\n  CollectionOptions,\n  ForceFlushOptions,\n  ShutdownOptions,\n} from '../types';\nimport { Aggregation } from '../view/Aggregation';\nimport {\n  AggregationSelector,\n  AggregationTemporalitySelector,\n  DEFAULT_AGGREGATION_SELECTOR,\n  DEFAULT_AGGREGATION_TEMPORALITY_SELECTOR,\n} from './AggregationSelector';\n\nexport interface MetricReaderOptions {\n  /**\n   * Aggregation selector based on metric instrument types. If no views are\n   * configured for a metric instrument, a per-metric-reader aggregation is\n   * selected with this selector.\n   */\n  aggregationSelector?: AggregationSelector;\n  /**\n   * Aggregation temporality selector based on metric instrument types. If\n   * not configured, cumulative is used for all instruments.\n   */\n  aggregationTemporalitySelector?: AggregationTemporalitySelector;\n  /**\n   * **Note, this option is experimental**. Additional MetricProducers to use as a source of\n   * aggregated metric data in addition to the SDK's metric data. The resource returned by\n   * these MetricProducers is ignored; the SDK's resource will be used instead.\n   * @experimental\n   */\n  metricProducers?: MetricProducer[];\n}\n\n/**\n * A registered reader of metrics that, when linked to a {@link MetricProducer}, offers global\n * control over metrics.\n */\nexport abstract class MetricReader {\n  // Tracks the shutdown state.\n  // TODO: use BindOncePromise here once a new version of @opentelemetry/core is available.\n  private _shutdown = false;\n  // Additional MetricProducers which will be combined with the SDK's output\n  private _metricProducers: MetricProducer[];\n  // MetricProducer used by this instance which produces metrics from the SDK\n  private _sdkMetricProducer?: MetricProducer;\n  private readonly _aggregationTemporalitySelector: AggregationTemporalitySelector;\n  private readonly _aggregationSelector: AggregationSelector;\n\n  constructor(options?: MetricReaderOptions) {\n    this._aggregationSelector =\n      options?.aggregationSelector ?? DEFAULT_AGGREGATION_SELECTOR;\n    this._aggregationTemporalitySelector =\n      options?.aggregationTemporalitySelector ??\n      DEFAULT_AGGREGATION_TEMPORALITY_SELECTOR;\n    this._metricProducers = options?.metricProducers ?? [];\n  }\n\n  /**\n   * Set the {@link MetricProducer} used by this instance. **This should only be called by the\n   * SDK and should be considered internal.**\n   *\n   * To add additional {@link MetricProducer}s to a {@link MetricReader}, pass them to the\n   * constructor as {@link MetricReaderOptions.metricProducers}.\n   *\n   * @internal\n   * @param metricProducer\n   */\n  setMetricProducer(metricProducer: MetricProducer) {\n    if (this._sdkMetricProducer) {\n      throw new Error(\n        'MetricReader can not be bound to a MeterProvider again.'\n      );\n    }\n    this._sdkMetricProducer = metricProducer;\n    this.onInitialized();\n  }\n\n  /**\n   * Select the {@link Aggregation} for the given {@link InstrumentType} for this\n   * reader.\n   */\n  selectAggregation(instrumentType: InstrumentType): Aggregation {\n    return this._aggregationSelector(instrumentType);\n  }\n\n  /**\n   * Select the {@link AggregationTemporality} for the given\n   * {@link InstrumentType} for this reader.\n   */\n  selectAggregationTemporality(\n    instrumentType: InstrumentType\n  ): AggregationTemporality {\n    return this._aggregationTemporalitySelector(instrumentType);\n  }\n\n  /**\n   * Handle once the SDK has initialized this {@link MetricReader}\n   * Overriding this method is optional.\n   */\n  protected onInitialized(): void {\n    // Default implementation is empty.\n  }\n\n  /**\n   * Handle a shutdown signal by the SDK.\n   *\n   * <p> For push exporters, this should shut down any intervals and close any open connections.\n   * @protected\n   */\n  protected abstract onShutdown(): Promise<void>;\n\n  /**\n   * Handle a force flush signal by the SDK.\n   *\n   * <p> In all scenarios metrics should be collected via {@link collect()}.\n   * <p> For push exporters, this should collect and report metrics.\n   * @protected\n   */\n  protected abstract onForceFlush(): Promise<void>;\n\n  /**\n   * Collect all metrics from the associated {@link MetricProducer}\n   */\n  async collect(options?: CollectionOptions): Promise<CollectionResult> {\n    if (this._sdkMetricProducer === undefined) {\n      throw new Error('MetricReader is not bound to a MetricProducer');\n    }\n\n    // Subsequent invocations to collect are not allowed. SDKs SHOULD return some failure for these calls.\n    if (this._shutdown) {\n      throw new Error('MetricReader is shutdown');\n    }\n\n    const [sdkCollectionResults, ...additionalCollectionResults] =\n      await Promise.all([\n        this._sdkMetricProducer.collect({\n          timeoutMillis: options?.timeoutMillis,\n        }),\n        ...this._metricProducers.map(producer =>\n          producer.collect({\n            timeoutMillis: options?.timeoutMillis,\n          })\n        ),\n      ]);\n\n    // Merge the results, keeping the SDK's Resource\n    const errors = sdkCollectionResults.errors.concat(\n      FlatMap(additionalCollectionResults, result => result.errors)\n    );\n    const resource = sdkCollectionResults.resourceMetrics.resource;\n    const scopeMetrics =\n      sdkCollectionResults.resourceMetrics.scopeMetrics.concat(\n        FlatMap(\n          additionalCollectionResults,\n          result => result.resourceMetrics.scopeMetrics\n        )\n      );\n    return {\n      resourceMetrics: {\n        resource,\n        scopeMetrics,\n      },\n      errors,\n    };\n  }\n\n  /**\n   * Shuts down the metric reader, the promise will reject after the optional timeout or resolve after completion.\n   *\n   * <p> NOTE: this operation will continue even after the promise rejects due to a timeout.\n   * @param options options with timeout.\n   */\n  async shutdown(options?: ShutdownOptions): Promise<void> {\n    // Do not call shutdown again if it has already been called.\n    if (this._shutdown) {\n      api.diag.error('Cannot call shutdown twice.');\n      return;\n    }\n\n    // No timeout if timeoutMillis is undefined or null.\n    if (options?.timeoutMillis == null) {\n      await this.onShutdown();\n    } else {\n      await callWithTimeout(this.onShutdown(), options.timeoutMillis);\n    }\n\n    this._shutdown = true;\n  }\n\n  /**\n   * Flushes metrics read by this reader, the promise will reject after the optional timeout or resolve after completion.\n   *\n   * <p> NOTE: this operation will continue even after the promise rejects due to a timeout.\n   * @param options options with timeout.\n   */\n  async forceFlush(options?: ForceFlushOptions): Promise<void> {\n    if (this._shutdown) {\n      api.diag.warn('Cannot forceFlush on already shutdown MetricReader.');\n      return;\n    }\n\n    // No timeout if timeoutMillis is undefined or null.\n    if (options?.timeoutMillis == null) {\n      await this.onForceFlush();\n      return;\n    }\n\n    await callWithTimeout(this.onForceFlush(), options.timeoutMillis);\n  }\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;AAEH,OAAO,KAAK,GAAG,MAAM,oBAAoB,CAAC;AAI1C,OAAO,EAAE,OAAO,EAAE,eAAe,EAAE,MAAM,UAAU,CAAC;AAQpD,OAAO,EAGL,4BAA4B,EAC5B,wCAAwC,GACzC,MAAM,uBAAuB,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAuB/B;;;GAGG,CACH,IAAA,eAAA;IAWE,SAAA,aAAY,OAA6B;;QAVzC,6BAA6B;QAC7B,yFAAyF;QACjF,IAAA,CAAA,SAAS,GAAG,KAAK,CAAC;QASxB,IAAI,CAAC,oBAAoB,GACvB,CAAA,KAAA,OAAO,KAAA,QAAP,OAAO,KAAA,KAAA,IAAA,KAAA,IAAP,OAAO,CAAE,mBAAmB,MAAA,QAAA,OAAA,KAAA,IAAA,KAAI,uRAA4B,CAAC;QAC/D,IAAI,CAAC,+BAA+B,GAClC,CAAA,KAAA,OAAO,KAAA,QAAP,OAAO,KAAA,KAAA,IAAA,KAAA,IAAP,OAAO,CAAE,8BAA8B,MAAA,QAAA,OAAA,KAAA,IAAA,6PACvC,2CAAwC,CAAC;QAC3C,IAAI,CAAC,gBAAgB,GAAG,CAAA,KAAA,OAAO,KAAA,QAAP,OAAO,KAAA,KAAA,IAAA,KAAA,IAAP,OAAO,CAAE,eAAe,MAAA,QAAA,OAAA,KAAA,IAAA,KAAI,EAAE,CAAC;IACzD,CAAC;IAED;;;;;;;;;OASG,CACH,aAAA,SAAA,CAAA,iBAAiB,GAAjB,SAAkB,cAA8B;QAC9C,IAAI,IAAI,CAAC,kBAAkB,EAAE;YAC3B,MAAM,IAAI,KAAK,CACb,yDAAyD,CAC1D,CAAC;SACH;QACD,IAAI,CAAC,kBAAkB,GAAG,cAAc,CAAC;QACzC,IAAI,CAAC,aAAa,EAAE,CAAC;IACvB,CAAC;IAED;;;OAGG,CACH,aAAA,SAAA,CAAA,iBAAiB,GAAjB,SAAkB,cAA8B;QAC9C,OAAO,IAAI,CAAC,oBAAoB,CAAC,cAAc,CAAC,CAAC;IACnD,CAAC;IAED;;;OAGG,CACH,aAAA,SAAA,CAAA,4BAA4B,GAA5B,SACE,cAA8B;QAE9B,OAAO,IAAI,CAAC,+BAA+B,CAAC,cAAc,CAAC,CAAC;IAC9D,CAAC;IAED;;;OAGG,CACO,aAAA,SAAA,CAAA,aAAa,GAAvB;IACE,mCAAmC;IACrC,CAAC;IAmBD;;OAEG,CACG,aAAA,SAAA,CAAA,OAAO,GAAb,SAAc,OAA2B;;;;;;wBACvC,IAAI,IAAI,CAAC,kBAAkB,KAAK,SAAS,EAAE;4BACzC,MAAM,IAAI,KAAK,CAAC,+CAA+C,CAAC,CAAC;yBAClE;wBAED,sGAAsG;wBACtG,IAAI,IAAI,CAAC,SAAS,EAAE;4BAClB,MAAM,IAAI,KAAK,CAAC,0BAA0B,CAAC,CAAC;yBAC7C;wBAGC,OAAA;4BAAA,EAAA,OAAA;4BAAM,OAAO,CAAC,GAAG,CAAA,cAAA;gCACf,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC;oCAC9B,aAAa,EAAE,OAAO,KAAA,QAAP,OAAO,KAAA,KAAA,IAAA,KAAA,IAAP,OAAO,CAAE,aAAa;iCACtC,CAAC;sCACC,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,SAAA,QAAQ;gCACnC,OAAA,QAAQ,CAAC,OAAO,CAAC;oCACf,aAAa,EAAE,OAAO,KAAA,QAAP,OAAO,KAAA,KAAA,IAAA,KAAA,IAAP,OAAO,CAAE,aAAa;iCACtC,CAAC;4BAFF,CAEE,CACH,GAAA,OACD;yBAAA,CAAA;;wBAVE,KAAA,OAAA,KAAA,CAAA,KAAA,GAAA;4BACJ,GAAA,IAAA,EASE;yBAAA,CAAA,EAVG,oBAAoB,GAAA,EAAA,CAAA,EAAA,EAAK,2BAA2B,GAAA,GAAA,KAAA,CAAA,EAAA;wBAarD,MAAM,GAAG,oBAAoB,CAAC,MAAM,CAAC,MAAM,qOAC/C,UAAA,AAAO,EAAC,2BAA2B,EAAE,SAAA,MAAM;4BAAI,OAAA,MAAM,CAAC,MAAM;wBAAb,CAAa,CAAC,CAC9D,CAAC;wBACI,QAAQ,GAAG,oBAAoB,CAAC,eAAe,CAAC,QAAQ,CAAC;wBACzD,YAAY,GAChB,oBAAoB,CAAC,eAAe,CAAC,YAAY,CAAC,MAAM,KACtD,0OAAA,AAAO,EACL,2BAA2B,EAC3B,SAAA,MAAM;4BAAI,OAAA,MAAM,CAAC,eAAe,CAAC,YAAY;wBAAnC,CAAmC,CAC9C,CACF,CAAC;wBACJ,OAAA;4BAAA,EAAA,QAAA;4BAAO;gCACL,eAAe,EAAE;oCACf,QAAQ,EAAA,QAAA;oCACR,YAAY,EAAA,YAAA;iCACb;gCACD,MAAM,EAAA,MAAA;6BACP;yBAAA,CAAC;;;;KACH;IAED;;;;;OAKG,CACG,aAAA,SAAA,CAAA,QAAQ,GAAd,SAAe,OAAyB;;;;;wBACtC,4DAA4D;wBAC5D,IAAI,IAAI,CAAC,SAAS,EAAE;qMAClB,GAAG,CAAC,GAAI,CAAC,KAAK,CAAC,6BAA6B,CAAC,CAAC;4BAC9C,OAAA;gCAAA,EAAA,QAAA;6BAAA,CAAO;yBACR;6BAGG,CAAA,CAAA,OAAO,KAAA,QAAP,OAAO,KAAA,KAAA,IAAA,KAAA,IAAP,OAAO,CAAE,aAAa,KAAI,IAAI,CAAA,EAA9B,OAAA;4BAAA,EAAA,OAAA;4BAAA;yBAAA,CAA8B;wBAChC,OAAA;4BAAA,EAAA,OAAA;4BAAM,IAAI,CAAC,UAAU,EAAE;yBAAA,CAAA;;wBAAvB,GAAA,IAAA,EAAuB,CAAC;;;;;;wBAExB,OAAA;4BAAA,EAAA,OAAA;gQAAM,kBAAA,AAAe,EAAC,IAAI,CAAC,UAAU,EAAE,EAAE,OAAO,CAAC,aAAa,CAAC;yBAAA,CAAA;;wBAA/D,GAAA,IAAA,EAA+D,CAAC;;;wBAGlE,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;;;;;;;KACvB;IAED;;;;;OAKG,CACG,aAAA,SAAA,CAAA,UAAU,GAAhB,SAAiB,OAA2B;;;;;wBAC1C,IAAI,IAAI,CAAC,SAAS,EAAE;4BAClB,GAAG,CAAC,4KAAI,CAAC,IAAI,CAAC,qDAAqD,CAAC,CAAC;4BACrE,OAAA;gCAAA,EAAA,QAAA;6BAAA,CAAO;yBACR;6BAGG,CAAA,CAAA,OAAO,KAAA,QAAP,OAAO,KAAA,KAAA,IAAA,KAAA,IAAP,OAAO,CAAE,aAAa,KAAI,IAAI,CAAA,EAA9B,OAAA;4BAAA,EAAA,OAAA;4BAAA;yBAAA,CAA8B;wBAChC,OAAA;4BAAA,EAAA,OAAA;4BAAM,IAAI,CAAC,YAAY,EAAE;yBAAA,CAAA;;wBAAzB,GAAA,IAAA,EAAyB,CAAC;wBAC1B,OAAA;4BAAA,EAAA,QAAA;yBAAA,CAAO;;wBAGT,OAAA;4BAAA,EAAA,OAAA;6BAAM,qPAAA,AAAe,EAAC,IAAI,CAAC,YAAY,EAAE,EAAE,OAAO,CAAC,aAAa,CAAC;yBAAA,CAAA;;wBAAjE,GAAA,IAAA,EAAiE,CAAC;;;;;;;KACnE;IACH,OAAA,YAAC;AAAD,CAAC,AA5KD,IA4KC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3074, "column": 0}, "map": {"version": 3, "file": "PeriodicExportingMetricReader.js", "sourceRoot": "", "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/node_modules/%40opentelemetry/sdk-node/node_modules/%40opentelemetry/sdk-metrics/src/export/PeriodicExportingMetricReader.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport * as api from '@opentelemetry/api';\nimport {\n  internal,\n  ExportResultCode,\n  globalErrorHandler,\n  unrefTimer,\n} from '@opentelemetry/core';\nimport { MetricReader } from './MetricReader';\nimport { PushMetricExporter } from './MetricExporter';\nimport { callWithTimeout, TimeoutError } from '../utils';\nimport { diag } from '@opentelemetry/api';\nimport { MetricProducer } from './MetricProducer';\n\nexport type PeriodicExportingMetricReaderOptions = {\n  /**\n   * The backing exporter for the metric reader.\n   */\n  exporter: PushMetricExporter;\n  /**\n   * An internal milliseconds for the metric reader to initiate metric\n   * collection.\n   */\n  exportIntervalMillis?: number;\n  /**\n   * Milliseconds for the async observable callback to timeout.\n   */\n  exportTimeoutMillis?: number;\n  /**\n   * **Note, this option is experimental**. Additional MetricProducers to use as a source of\n   * aggregated metric data in addition to the SDK's metric data. The resource returned by\n   * these MetricProducers is ignored; the SDK's resource will be used instead.\n   * @experimental\n   */\n  metricProducers?: MetricProducer[];\n};\n\n/**\n * {@link MetricReader} which collects metrics based on a user-configurable time interval, and passes the metrics to\n * the configured {@link PushMetricExporter}\n */\nexport class PeriodicExportingMetricReader extends MetricReader {\n  private _interval?: ReturnType<typeof setInterval>;\n  private _exporter: PushMetricExporter;\n  private readonly _exportInterval: number;\n  private readonly _exportTimeout: number;\n\n  constructor(options: PeriodicExportingMetricReaderOptions) {\n    super({\n      aggregationSelector: options.exporter.selectAggregation?.bind(\n        options.exporter\n      ),\n      aggregationTemporalitySelector:\n        options.exporter.selectAggregationTemporality?.bind(options.exporter),\n      metricProducers: options.metricProducers,\n    });\n\n    if (\n      options.exportIntervalMillis !== undefined &&\n      options.exportIntervalMillis <= 0\n    ) {\n      throw Error('exportIntervalMillis must be greater than 0');\n    }\n\n    if (\n      options.exportTimeoutMillis !== undefined &&\n      options.exportTimeoutMillis <= 0\n    ) {\n      throw Error('exportTimeoutMillis must be greater than 0');\n    }\n\n    if (\n      options.exportTimeoutMillis !== undefined &&\n      options.exportIntervalMillis !== undefined &&\n      options.exportIntervalMillis < options.exportTimeoutMillis\n    ) {\n      throw Error(\n        'exportIntervalMillis must be greater than or equal to exportTimeoutMillis'\n      );\n    }\n\n    this._exportInterval = options.exportIntervalMillis ?? 60000;\n    this._exportTimeout = options.exportTimeoutMillis ?? 30000;\n    this._exporter = options.exporter;\n  }\n\n  private async _runOnce(): Promise<void> {\n    try {\n      await callWithTimeout(this._doRun(), this._exportTimeout);\n    } catch (err) {\n      if (err instanceof TimeoutError) {\n        api.diag.error(\n          'Export took longer than %s milliseconds and timed out.',\n          this._exportTimeout\n        );\n        return;\n      }\n\n      globalErrorHandler(err);\n    }\n  }\n\n  private async _doRun(): Promise<void> {\n    const { resourceMetrics, errors } = await this.collect({\n      timeoutMillis: this._exportTimeout,\n    });\n\n    if (errors.length > 0) {\n      api.diag.error(\n        'PeriodicExportingMetricReader: metrics collection errors',\n        ...errors\n      );\n    }\n\n    const doExport = async () => {\n      const result = await internal._export(this._exporter, resourceMetrics);\n      if (result.code !== ExportResultCode.SUCCESS) {\n        throw new Error(\n          `PeriodicExportingMetricReader: metrics export failed (error ${result.error})`\n        );\n      }\n    };\n\n    // Avoid scheduling a promise to make the behavior more predictable and easier to test\n    if (resourceMetrics.resource.asyncAttributesPending) {\n      resourceMetrics.resource\n        .waitForAsyncAttributes?.()\n        .then(doExport, err =>\n          diag.debug('Error while resolving async portion of resource: ', err)\n        );\n    } else {\n      await doExport();\n    }\n  }\n\n  protected override onInitialized(): void {\n    // start running the interval as soon as this reader is initialized and keep handle for shutdown.\n    this._interval = setInterval(() => {\n      // this._runOnce never rejects. Using void operator to suppress @typescript-eslint/no-floating-promises.\n      void this._runOnce();\n    }, this._exportInterval);\n    unrefTimer(this._interval);\n  }\n\n  protected async onForceFlush(): Promise<void> {\n    await this._runOnce();\n    await this._exporter.forceFlush();\n  }\n\n  protected async onShutdown(): Promise<void> {\n    if (this._interval) {\n      clearInterval(this._interval);\n    }\n\n    await this._exporter.shutdown();\n  }\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;AAEH,OAAO,KAAK,GAAG,MAAM,oBAAoB,CAAC;;;;AAC1C,OAAO,EACL,QAAQ,EACR,gBAAgB,EAChB,kBAAkB,EAClB,UAAU,GACX,MAAM,qBAAqB,CAAC;AAC7B,OAAO,EAAE,YAAY,EAAE,MAAM,gBAAgB,CAAC;AAE9C,OAAO,EAAE,eAAe,EAAE,YAAY,EAAE,MAAM,UAAU,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA2BzD;;;GAGG,CACH,IAAA,gCAAA,SAAA,MAAA;IAAmD,UAAA,+BAAA,QAAY;IAM7D,SAAA,8BAAY,OAA6C;;QAAzD,IAAA,QACE,OAAA,IAAA,CAAA,IAAA,EAAM;YACJ,mBAAmB,EAAE,CAAA,KAAA,OAAO,CAAC,QAAQ,CAAC,iBAAiB,MAAA,QAAA,OAAA,KAAA,IAAA,KAAA,IAAA,GAAE,IAAI,CAC3D,OAAO,CAAC,QAAQ,CACjB;YACD,8BAA8B,EAC5B,CAAA,KAAA,OAAO,CAAC,QAAQ,CAAC,4BAA4B,MAAA,QAAA,OAAA,KAAA,IAAA,KAAA,IAAA,GAAE,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC;YACvE,eAAe,EAAE,OAAO,CAAC,eAAe;SACzC,CAAC,IAAA,IAAA,CA6BH;QA3BC,IACE,OAAO,CAAC,oBAAoB,KAAK,SAAS,IAC1C,OAAO,CAAC,oBAAoB,IAAI,CAAC,EACjC;YACA,MAAM,KAAK,CAAC,6CAA6C,CAAC,CAAC;SAC5D;QAED,IACE,OAAO,CAAC,mBAAmB,KAAK,SAAS,IACzC,OAAO,CAAC,mBAAmB,IAAI,CAAC,EAChC;YACA,MAAM,KAAK,CAAC,4CAA4C,CAAC,CAAC;SAC3D;QAED,IACE,OAAO,CAAC,mBAAmB,KAAK,SAAS,IACzC,OAAO,CAAC,oBAAoB,KAAK,SAAS,IAC1C,OAAO,CAAC,oBAAoB,GAAG,OAAO,CAAC,mBAAmB,EAC1D;YACA,MAAM,KAAK,CACT,2EAA2E,CAC5E,CAAC;SACH;QAED,KAAI,CAAC,eAAe,GAAG,CAAA,KAAA,OAAO,CAAC,oBAAoB,MAAA,QAAA,OAAA,KAAA,IAAA,KAAI,KAAK,CAAC;QAC7D,KAAI,CAAC,cAAc,GAAG,CAAA,KAAA,OAAO,CAAC,mBAAmB,MAAA,QAAA,OAAA,KAAA,IAAA,KAAI,KAAK,CAAC;QAC3D,KAAI,CAAC,SAAS,GAAG,OAAO,CAAC,QAAQ,CAAC;;IACpC,CAAC;IAEa,8BAAA,SAAA,CAAA,QAAQ,GAAtB;;;;;;;;;;;;wBAEI,OAAA;4BAAA,EAAA,OAAA;gQAAM,kBAAA,AAAe,EAAC,IAAI,CAAC,MAAM,EAAE,EAAE,IAAI,CAAC,cAAc,CAAC;yBAAA,CAAA;;wBAAzD,GAAA,IAAA,EAAyD,CAAC;;;;;;;wBAE1D,IAAI,KAAG,4OAAY,eAAY,EAAE;qMAC/B,GAAG,CAAC,GAAI,CAAC,KAAK,CACZ,wDAAwD,EACxD,IAAI,CAAC,cAAc,CACpB,CAAC;4BACF,OAAA;gCAAA,EAAA,QAAA;6BAAA,CAAO;yBACR;wBAED,8QAAA,AAAkB,EAAC,KAAG,CAAC,CAAC;;;;;;;;;;;;KAE3B;IAEa,8BAAA,SAAA,CAAA,MAAM,GAApB;;;;;;;;;wBACsC,OAAA;4BAAA,EAAA,OAAA;4BAAM,IAAI,CAAC,OAAO,CAAC;gCACrD,aAAa,EAAE,IAAI,CAAC,cAAc;6BACnC,CAAC;yBAAA,CAAA;;wBAFI,KAA8B,GAAA,IAAA,EAElC,EAFM,eAAe,GAAA,GAAA,eAAA,EAAE,MAAM,GAAA,GAAA,MAAA;wBAI/B,IAAI,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE;4BACrB,CAAA,8KAAA,GAAG,CAAC,GAAI,CAAA,CAAC,KAAK,CAAA,KAAA,CAAA,IAAA,cAAA;gCACZ,0DAA0D;6BAAA,EAAA,OACvD,MAAM,GAAA,QACT;yBACH;wBAEK,QAAQ,GAAG;4BAAA,OAAA,UAAA,OAAA,KAAA,GAAA,KAAA,GAAA;;;;;4CACA,OAAA;gDAAA,EAAA,OAAA;sRAAM,WAAQ,CAAC,OAAO,CAAC,IAAI,CAAC,SAAS,EAAE,eAAe,CAAC;6CAAA,CAAA;;4CAAhE,MAAM,GAAG,GAAA,IAAA,EAAuD;4CACtE,IAAI,MAAM,CAAC,IAAI,kOAAK,mBAAgB,CAAC,OAAO,EAAE;gDAC5C,MAAM,IAAI,KAAK,CACb,iEAA+D,MAAM,CAAC,KAAK,GAAA,GAAG,CAC/E,CAAC;6CACH;;;;;;;yBACF,CAAC;6BAGE,eAAe,CAAC,QAAQ,CAAC,sBAAsB,EAA/C,OAAA;4BAAA,EAAA,OAAA;4BAAA;yBAAA,CAA+C;wBACjD,CAAA,KAAA,CAAA,KAAA,eAAe,CAAC,QAAQ,EACrB,sBAAsB,MAAA,QAAA,OAAA,KAAA,IAAA,KAAA,IAAA,GAAA,IAAA,CAAA,IACtB,IAAI,CAAC,QAAQ,EAAE,SAAA,GAAG;4BACjB,gLAAA,OAAI,CAAC,KAAK,CAAC,mDAAmD,EAAE,GAAG,CAAC;wBAApE,CAAoE,CACrE,CAAC;;;;;;wBAEJ,OAAA;4BAAA,EAAA,OAAA;4BAAM,QAAQ,EAAE;yBAAA,CAAA;;wBAAhB,GAAA,IAAA,EAAgB,CAAC;;;;;;;;;KAEpB;IAEkB,8BAAA,SAAA,CAAA,aAAa,GAAhC;QAAA,IAAA,QAAA,IAAA,CAOC;QANC,iGAAiG;QACjG,IAAI,CAAC,SAAS,GAAG,WAAW,CAAC;YAC3B,wGAAwG;YACxG,KAAK,KAAI,CAAC,QAAQ,EAAE,CAAC;QACvB,CAAC,EAAE,IAAI,CAAC,eAAe,CAAC,CAAC;iQACzB,aAAA,AAAU,EAAC,IAAI,CAAC,SAAS,CAAC,CAAC;IAC7B,CAAC;IAEe,8BAAA,SAAA,CAAA,YAAY,GAA5B;;;;;wBACE,OAAA;4BAAA,EAAA,OAAA;4BAAM,IAAI,CAAC,QAAQ,EAAE;yBAAA,CAAA;;wBAArB,GAAA,IAAA,EAAqB,CAAC;wBACtB,OAAA;4BAAA,EAAA,OAAA;4BAAM,IAAI,CAAC,SAAS,CAAC,UAAU,EAAE;yBAAA,CAAA;;wBAAjC,GAAA,IAAA,EAAiC,CAAC;;;;;;;KACnC;IAEe,8BAAA,SAAA,CAAA,UAAU,GAA1B;;;;;wBACE,IAAI,IAAI,CAAC,SAAS,EAAE;4BAClB,aAAa,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;yBAC/B;wBAED,OAAA;4BAAA,EAAA,OAAA;4BAAM,IAAI,CAAC,SAAS,CAAC,QAAQ,EAAE;yBAAA,CAAA;;wBAA/B,GAAA,IAAA,EAA+B,CAAC;;;;;;;KACjC;IACH,OAAA,6BAAC;AAAD,CAAC,AAnHD,kPAAmD,eAAY,GAmH9D", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3478, "column": 0}, "map": {"version": 3, "file": "InMemoryMetricExporter.js", "sourceRoot": "", "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/node_modules/%40opentelemetry/sdk-node/node_modules/%40opentelemetry/sdk-metrics/src/export/InMemoryMetricExporter.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { ExportResultCode } from '@opentelemetry/core';\nimport { ExportResult } from '@opentelemetry/core';\nimport { InstrumentType } from '../InstrumentDescriptor';\nimport { AggregationTemporality } from './AggregationTemporality';\nimport { ResourceMetrics } from './MetricData';\nimport { PushMetricExporter } from './MetricExporter';\n\n/**\n * In-memory Metrics Exporter is a Push Metric Exporter\n * which accumulates metrics data in the local memory and\n * allows to inspect it (useful for e.g. unit tests).\n */\nexport class InMemoryMetricExporter implements PushMetricExporter {\n  protected _shutdown = false;\n  protected _aggregationTemporality: AggregationTemporality;\n  private _metrics: ResourceMetrics[] = [];\n\n  constructor(aggregationTemporality: AggregationTemporality) {\n    this._aggregationTemporality = aggregationTemporality;\n  }\n\n  /**\n   * @inheritedDoc\n   */\n  export(\n    metrics: ResourceMetrics,\n    resultCallback: (result: ExportResult) => void\n  ): void {\n    // Avoid storing metrics when exporter is shutdown\n    if (this._shutdown) {\n      setTimeout(() => resultCallback({ code: ExportResultCode.FAILED }), 0);\n      return;\n    }\n\n    this._metrics.push(metrics);\n    setTimeout(() => resultCallback({ code: ExportResultCode.SUCCESS }), 0);\n  }\n\n  /**\n   * Returns all the collected resource metrics\n   * @returns ResourceMetrics[]\n   */\n  public getMetrics(): ResourceMetrics[] {\n    return this._metrics;\n  }\n\n  forceFlush(): Promise<void> {\n    return Promise.resolve();\n  }\n\n  reset() {\n    this._metrics = [];\n  }\n\n  selectAggregationTemporality(\n    _instrumentType: InstrumentType\n  ): AggregationTemporality {\n    return this._aggregationTemporality;\n  }\n\n  shutdown(): Promise<void> {\n    this._shutdown = true;\n    return Promise.resolve();\n  }\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;AAEH,OAAO,EAAE,gBAAgB,EAAE,MAAM,qBAAqB,CAAC;;AAOvD;;;;GAIG,CACH,IAAA,yBAAA;IAKE,SAAA,uBAAY,sBAA8C;QAJhD,IAAA,CAAA,SAAS,GAAG,KAAK,CAAC;QAEpB,IAAA,CAAA,QAAQ,GAAsB,EAAE,CAAC;QAGvC,IAAI,CAAC,uBAAuB,GAAG,sBAAsB,CAAC;IACxD,CAAC;IAED;;OAEG,CACH,uBAAA,SAAA,CAAA,MAAM,GAAN,SACE,OAAwB,EACxB,cAA8C;QAE9C,kDAAkD;QAClD,IAAI,IAAI,CAAC,SAAS,EAAE;YAClB,UAAU,CAAC;gBAAM,OAAA,cAAc,CAAC;oBAAE,IAAI,+NAAE,mBAAgB,CAAC,MAAM;gBAAA,CAAE,CAAC;YAAjD,CAAiD,EAAE,CAAC,CAAC,CAAC;YACvE,OAAO;SACR;QAED,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QAC5B,UAAU,CAAC;YAAM,OAAA,cAAc,CAAC;gBAAE,IAAI,+NAAE,mBAAgB,CAAC,OAAO;YAAA,CAAE,CAAC;QAAlD,CAAkD,EAAE,CAAC,CAAC,CAAC;IAC1E,CAAC;IAED;;;OAGG,CACI,uBAAA,SAAA,CAAA,UAAU,GAAjB;QACE,OAAO,IAAI,CAAC,QAAQ,CAAC;IACvB,CAAC;IAED,uBAAA,SAAA,CAAA,UAAU,GAAV;QACE,OAAO,OAAO,CAAC,OAAO,EAAE,CAAC;IAC3B,CAAC;IAED,uBAAA,SAAA,CAAA,KAAK,GAAL;QACE,IAAI,CAAC,QAAQ,GAAG,EAAE,CAAC;IACrB,CAAC;IAED,uBAAA,SAAA,CAAA,4BAA4B,GAA5B,SACE,eAA+B;QAE/B,OAAO,IAAI,CAAC,uBAAuB,CAAC;IACtC,CAAC;IAED,uBAAA,SAAA,CAAA,QAAQ,GAAR;QACE,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;QACtB,OAAO,OAAO,CAAC,OAAO,EAAE,CAAC;IAC3B,CAAC;IACH,OAAA,sBAAC;AAAD,CAAC,AApDD,IAoDC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3555, "column": 0}, "map": {"version": 3, "file": "ConsoleMetricExporter.js", "sourceRoot": "", "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/node_modules/%40opentelemetry/sdk-node/node_modules/%40opentelemetry/sdk-metrics/src/export/ConsoleMetricExporter.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nimport { ExportResult, ExportResultCode } from '@opentelemetry/core';\nimport { InstrumentType } from '../InstrumentDescriptor';\nimport { AggregationTemporality } from './AggregationTemporality';\nimport { ResourceMetrics } from './MetricData';\nimport { PushMetricExporter } from './MetricExporter';\nimport {\n  AggregationTemporalitySelector,\n  DEFAULT_AGGREGATION_TEMPORALITY_SELECTOR,\n} from './AggregationSelector';\n\ninterface ConsoleMetricExporterOptions {\n  temporalitySelector?: AggregationTemporalitySelector;\n}\n\n/* eslint-disable no-console */\nexport class ConsoleMetricExporter implements PushMetricExporter {\n  protected _shutdown = false;\n  protected _temporalitySelector: AggregationTemporalitySelector;\n\n  constructor(options?: ConsoleMetricExporterOptions) {\n    this._temporalitySelector =\n      options?.temporalitySelector ?? DEFAULT_AGGREGATION_TEMPORALITY_SELECTOR;\n  }\n\n  export(\n    metrics: ResourceMetrics,\n    resultCallback: (result: ExportResult) => void\n  ): void {\n    if (this._shutdown) {\n      // If the exporter is shutting down, by spec, we need to return FAILED as export result\n      setImmediate(resultCallback, { code: ExportResultCode.FAILED });\n      return;\n    }\n\n    return ConsoleMetricExporter._sendMetrics(metrics, resultCallback);\n  }\n\n  forceFlush(): Promise<void> {\n    return Promise.resolve();\n  }\n\n  selectAggregationTemporality(\n    _instrumentType: InstrumentType\n  ): AggregationTemporality {\n    return this._temporalitySelector(_instrumentType);\n  }\n\n  shutdown(): Promise<void> {\n    this._shutdown = true;\n    return Promise.resolve();\n  }\n\n  private static _sendMetrics(\n    metrics: ResourceMetrics,\n    done: (result: ExportResult) => void\n  ): void {\n    for (const scopeMetrics of metrics.scopeMetrics) {\n      for (const metric of scopeMetrics.metrics) {\n        console.dir(\n          {\n            descriptor: metric.descriptor,\n            dataPointType: metric.dataPointType,\n            dataPoints: metric.dataPoints,\n          },\n          { depth: null }\n        );\n      }\n    }\n\n    done({ code: ExportResultCode.SUCCESS });\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;;;;;;;;;;;;;;GAcG,CACH,OAAO,EAAgB,gBAAgB,EAAE,MAAM,qBAAqB,CAAC;AAKrE,OAAO,EAEL,wCAAwC,GACzC,MAAM,uBAAuB,CAAC;;;;;;;;;;;;;;;;;AAM/B,6BAAA,EAA+B,CAC/B,IAAA,wBAAA;IAIE,SAAA,sBAAY,OAAsC;;QAHxC,IAAA,CAAA,SAAS,GAAG,KAAK,CAAC;QAI1B,IAAI,CAAC,oBAAoB,GACvB,CAAA,KAAA,OAAO,KAAA,QAAP,OAAO,KAAA,KAAA,IAAA,KAAA,IAAP,OAAO,CAAE,mBAAmB,MAAA,QAAA,OAAA,KAAA,IAAA,6PAAI,2CAAwC,CAAC;IAC7E,CAAC;IAED,sBAAA,SAAA,CAAA,MAAM,GAAN,SACE,OAAwB,EACxB,cAA8C;QAE9C,IAAI,IAAI,CAAC,SAAS,EAAE;YAClB,uFAAuF;YACvF,YAAY,CAAC,cAAc,EAAE;gBAAE,IAAI,+NAAE,mBAAgB,CAAC,MAAM;YAAA,CAAE,CAAC,CAAC;YAChE,OAAO;SACR;QAED,OAAO,qBAAqB,CAAC,YAAY,CAAC,OAAO,EAAE,cAAc,CAAC,CAAC;IACrE,CAAC;IAED,sBAAA,SAAA,CAAA,UAAU,GAAV;QACE,OAAO,OAAO,CAAC,OAAO,EAAE,CAAC;IAC3B,CAAC;IAED,sBAAA,SAAA,CAAA,4BAA4B,GAA5B,SACE,eAA+B;QAE/B,OAAO,IAAI,CAAC,oBAAoB,CAAC,eAAe,CAAC,CAAC;IACpD,CAAC;IAED,sBAAA,SAAA,CAAA,QAAQ,GAAR;QACE,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;QACtB,OAAO,OAAO,CAAC,OAAO,EAAE,CAAC;IAC3B,CAAC;IAEc,sBAAA,YAAY,GAA3B,SACE,OAAwB,EACxB,IAAoC;;;YAEpC,IAA2B,IAAA,KAAA,SAAA,OAAO,CAAC,YAAY,CAAA,EAAA,KAAA,GAAA,IAAA,EAAA,EAAA,CAAA,GAAA,IAAA,EAAA,KAAA,GAAA,IAAA,GAAE;gBAA5C,IAAM,YAAY,GAAA,GAAA,KAAA;;oBACrB,IAAqB,IAAA,KAAA,CAAA,MAAA,KAAA,GAAA,SAAA,YAAY,CAAC,OAAO,CAAA,CAAA,EAAA,KAAA,GAAA,IAAA,EAAA,EAAA,CAAA,GAAA,IAAA,EAAA,KAAA,GAAA,IAAA,GAAE;wBAAtC,IAAM,MAAM,GAAA,GAAA,KAAA;wBACf,OAAO,CAAC,GAAG,CACT;4BACE,UAAU,EAAE,MAAM,CAAC,UAAU;4BAC7B,aAAa,EAAE,MAAM,CAAC,aAAa;4BACnC,UAAU,EAAE,MAAM,CAAC,UAAU;yBAC9B,EACD;4BAAE,KAAK,EAAE,IAAI;wBAAA,CAAE,CAChB,CAAC;qBACH;;;;;;;;;;;;aACF;;;;;;;;;;;;QAED,IAAI,CAAC;YAAE,IAAI,+NAAE,mBAAgB,CAAC,OAAO;QAAA,CAAE,CAAC,CAAC;IAC3C,CAAC;IACH,OAAA,qBAAC;AAAD,CAAC,AAxDD,IAwDC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3669, "column": 0}, "map": {"version": 3, "file": "ViewRegistry.js", "sourceRoot": "", "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/node_modules/%40opentelemetry/sdk-node/node_modules/%40opentelemetry/sdk-metrics/src/view/ViewRegistry.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { InstrumentationScope } from '@opentelemetry/core';\nimport { InstrumentDescriptor } from '../InstrumentDescriptor';\nimport { InstrumentSelector } from './InstrumentSelector';\nimport { MeterSelector } from './MeterSelector';\nimport { View } from './View';\n\nexport class ViewRegistry {\n  private _registeredViews: View[] = [];\n\n  addView(view: View) {\n    this._registeredViews.push(view);\n  }\n\n  findViews(\n    instrument: InstrumentDescriptor,\n    meter: InstrumentationScope\n  ): View[] {\n    const views = this._registeredViews.filter(registeredView => {\n      return (\n        this._matchInstrument(registeredView.instrumentSelector, instrument) &&\n        this._matchMeter(registeredView.meterSelector, meter)\n      );\n    });\n\n    return views;\n  }\n\n  private _matchInstrument(\n    selector: InstrumentSelector,\n    instrument: InstrumentDescriptor\n  ): boolean {\n    return (\n      (selector.getType() === undefined ||\n        instrument.type === selector.getType()) &&\n      selector.getNameFilter().match(instrument.name) &&\n      selector.getUnitFilter().match(instrument.unit)\n    );\n  }\n\n  private _matchMeter(\n    selector: MeterSelector,\n    meter: InstrumentationScope\n  ): boolean {\n    return (\n      selector.getNameFilter().match(meter.name) &&\n      (meter.version === undefined ||\n        selector.getVersionFilter().match(meter.version)) &&\n      (meter.schemaUrl === undefined ||\n        selector.getSchemaUrlFilter().match(meter.schemaUrl))\n    );\n  }\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;AAQH,IAAA,eAAA;IAAA,SAAA;QACU,IAAA,CAAA,gBAAgB,GAAW,EAAE,CAAC;IA4CxC,CAAC;IA1CC,aAAA,SAAA,CAAA,OAAO,GAAP,SAAQ,IAAU;QAChB,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IACnC,CAAC;IAED,aAAA,SAAA,CAAA,SAAS,GAAT,SACE,UAAgC,EAChC,KAA2B;QAF7B,IAAA,QAAA,IAAA,CAYC;QARC,IAAM,KAAK,GAAG,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,SAAA,cAAc;YACvD,OAAO,AACL,KAAI,CAAC,gBAAgB,CAAC,cAAc,CAAC,kBAAkB,EAAE,UAAU,CAAC,IACpE,KAAI,CAAC,WAAW,CAAC,cAAc,CAAC,aAAa,EAAE,KAAK,CAAC,CACtD,CAAC;QACJ,CAAC,CAAC,CAAC;QAEH,OAAO,KAAK,CAAC;IACf,CAAC;IAEO,aAAA,SAAA,CAAA,gBAAgB,GAAxB,SACE,QAA4B,EAC5B,UAAgC;QAEhC,OAAO,AACL,CAAC,QAAQ,CAAC,OAAO,EAAE,KAAK,SAAS,IAC/B,UAAU,CAAC,IAAI,KAAK,QAAQ,CAAC,OAAO,EAAE,CAAC,IACzC,QAAQ,CAAC,aAAa,EAAE,CAAC,KAAK,CAAC,UAAU,CAAC,IAAI,CAAC,IAC/C,QAAQ,CAAC,aAAa,EAAE,CAAC,KAAK,CAAC,UAAU,CAAC,IAAI,CAAC,CAChD,CAAC;IACJ,CAAC;IAEO,aAAA,SAAA,CAAA,WAAW,GAAnB,SACE,QAAuB,EACvB,KAA2B;QAE3B,OAAO,AACL,QAAQ,CAAC,aAAa,EAAE,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,IAC1C,CAAC,KAAK,CAAC,OAAO,KAAK,SAAS,IAC1B,QAAQ,CAAC,gBAAgB,EAAE,CAAC,KAAK,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,IACnD,CAAC,KAAK,CAAC,SAAS,KAAK,SAAS,IAC5B,QAAQ,CAAC,kBAAkB,EAAE,CAAC,KAAK,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,CACxD,CAAC;IACJ,CAAC;IACH,OAAA,YAAC;AAAD,CAAC,AA7CD,IA6CC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3716, "column": 0}, "map": {"version": 3, "file": "Instruments.js", "sourceRoot": "", "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/node_modules/%40opentelemetry/sdk-node/node_modules/%40opentelemetry/sdk-metrics/src/Instruments.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  context as contextApi,\n  diag,\n  Context,\n  MetricAttributes,\n  ValueType,\n  UpDownCounter,\n  Counter,\n  Histogram,\n  Observable,\n  ObservableCallback,\n  ObservableCounter,\n  ObservableGauge,\n  ObservableUpDownCounter,\n} from '@opentelemetry/api';\nimport { millisToHrTime } from '@opentelemetry/core';\nimport { InstrumentDescriptor } from './InstrumentDescriptor';\nimport { ObservableRegistry } from './state/ObservableRegistry';\nimport {\n  AsyncWritableMetricStorage,\n  WritableMetricStorage,\n} from './state/WritableMetricStorage';\nimport { Gauge } from './types';\n\nexport class SyncInstrument {\n  constructor(\n    private _writableMetricStorage: WritableMetricStorage,\n    protected _descriptor: InstrumentDescriptor\n  ) {}\n\n  protected _record(\n    value: number,\n    attributes: MetricAttributes = {},\n    context: Context = contextApi.active()\n  ) {\n    if (typeof value !== 'number') {\n      diag.warn(\n        `non-number value provided to metric ${this._descriptor.name}: ${value}`\n      );\n      return;\n    }\n    if (\n      this._descriptor.valueType === ValueType.INT &&\n      !Number.isInteger(value)\n    ) {\n      diag.warn(\n        `INT value type cannot accept a floating-point value for ${this._descriptor.name}, ignoring the fractional digits.`\n      );\n      value = Math.trunc(value);\n      // ignore non-finite values.\n      if (!Number.isInteger(value)) {\n        return;\n      }\n    }\n    this._writableMetricStorage.record(\n      value,\n      attributes,\n      context,\n      millisToHrTime(Date.now())\n    );\n  }\n}\n\n/**\n * The class implements {@link UpDownCounter} interface.\n */\nexport class UpDownCounterInstrument\n  extends SyncInstrument\n  implements UpDownCounter\n{\n  /**\n   * Increment value of counter by the input. Inputs may be negative.\n   */\n  add(value: number, attributes?: MetricAttributes, ctx?: Context): void {\n    this._record(value, attributes, ctx);\n  }\n}\n\n/**\n * The class implements {@link Counter} interface.\n */\nexport class CounterInstrument extends SyncInstrument implements Counter {\n  /**\n   * Increment value of counter by the input. Inputs may not be negative.\n   */\n  add(value: number, attributes?: MetricAttributes, ctx?: Context): void {\n    if (value < 0) {\n      diag.warn(\n        `negative value provided to counter ${this._descriptor.name}: ${value}`\n      );\n      return;\n    }\n\n    this._record(value, attributes, ctx);\n  }\n}\n\n/**\n * The class implements {@link Gauge} interface.\n */\nexport class GaugeInstrument extends SyncInstrument implements Gauge {\n  /**\n   * Records a measurement.\n   */\n  record(value: number, attributes?: MetricAttributes, ctx?: Context): void {\n    this._record(value, attributes, ctx);\n  }\n}\n\n/**\n * The class implements {@link Histogram} interface.\n */\nexport class HistogramInstrument extends SyncInstrument implements Histogram {\n  /**\n   * Records a measurement. Value of the measurement must not be negative.\n   */\n  record(value: number, attributes?: MetricAttributes, ctx?: Context): void {\n    if (value < 0) {\n      diag.warn(\n        `negative value provided to histogram ${this._descriptor.name}: ${value}`\n      );\n      return;\n    }\n    this._record(value, attributes, ctx);\n  }\n}\n\nexport class ObservableInstrument implements Observable {\n  /** @internal */\n  _metricStorages: AsyncWritableMetricStorage[];\n  /** @internal */\n  _descriptor: InstrumentDescriptor;\n\n  constructor(\n    descriptor: InstrumentDescriptor,\n    metricStorages: AsyncWritableMetricStorage[],\n    private _observableRegistry: ObservableRegistry\n  ) {\n    this._descriptor = descriptor;\n    this._metricStorages = metricStorages;\n  }\n\n  /**\n   * @see {Observable.addCallback}\n   */\n  addCallback(callback: ObservableCallback) {\n    this._observableRegistry.addCallback(callback, this);\n  }\n\n  /**\n   * @see {Observable.removeCallback}\n   */\n  removeCallback(callback: ObservableCallback) {\n    this._observableRegistry.removeCallback(callback, this);\n  }\n}\n\nexport class ObservableCounterInstrument\n  extends ObservableInstrument\n  implements ObservableCounter {}\nexport class ObservableGaugeInstrument\n  extends ObservableInstrument\n  implements ObservableGauge {}\nexport class ObservableUpDownCounterInstrument\n  extends ObservableInstrument\n  implements ObservableUpDownCounter {}\n\nexport function isObservableInstrument(\n  it: unknown\n): it is ObservableInstrument {\n  return it instanceof ObservableInstrument;\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;;;;;;;;;;;;AAEH,OAAO,EACL,OAAO,IAAI,UAAU,EACrB,IAAI,EAGJ,SAAS,GASV,MAAM,oBAAoB,CAAC;AAC5B,OAAO,EAAE,cAAc,EAAE,MAAM,qBAAqB,CAAC;;;;;;;;;;;;;;;;;;;;;;;AASrD,IAAA,iBAAA;IACE,SAAA,eACU,sBAA6C,EAC3C,WAAiC;QADnC,IAAA,CAAA,sBAAsB,GAAtB,sBAAsB,CAAuB;QAC3C,IAAA,CAAA,WAAW,GAAX,WAAW,CAAsB;IAC1C,CAAC;IAEM,eAAA,SAAA,CAAA,OAAO,GAAjB,SACE,KAAa,EACb,UAAiC,EACjC,OAAsC;QADtC,IAAA,eAAA,KAAA,GAAA;YAAA,aAAA,CAAA,CAAiC;QAAA;QACjC,IAAA,YAAA,KAAA,GAAA;YAAA,sLAAmB,UAAU,CAAC,MAAM,EAAE;QAAA;QAEtC,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE;oLAC7B,QAAI,CAAC,IAAI,CACP,yCAAuC,IAAI,CAAC,WAAW,CAAC,IAAI,GAAA,OAAK,KAAO,CACzE,CAAC;YACF,OAAO;SACR;QACD,IACE,IAAI,CAAC,WAAW,CAAC,SAAS,KAAK,2LAAS,CAAC,GAAG,IAC5C,CAAC,MAAM,CAAC,SAAS,CAAC,KAAK,CAAC,EACxB;qLACA,OAAI,CAAC,IAAI,CACP,6DAA2D,IAAI,CAAC,WAAW,CAAC,IAAI,GAAA,mCAAmC,CACpH,CAAC;YACF,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;YAC1B,4BAA4B;YAC5B,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,KAAK,CAAC,EAAE;gBAC5B,OAAO;aACR;SACF;QACD,IAAI,CAAC,sBAAsB,CAAC,MAAM,CAChC,KAAK,EACL,UAAU,EACV,OAAO,qOACP,iBAAA,AAAc,EAAC,IAAI,CAAC,GAAG,EAAE,CAAC,CAC3B,CAAC;IACJ,CAAC;IACH,OAAA,cAAC;AAAD,CAAC,AArCD,IAqCC;;AAED;;GAEG,CACH,IAAA,0BAAA,SAAA,MAAA;IACU,UAAA,yBAAA,QAAc;IADxB,SAAA;;IAUA,CAAC;IANC;;OAEG,CACH,wBAAA,SAAA,CAAA,GAAG,GAAH,SAAI,KAAa,EAAE,UAA6B,EAAE,GAAa;QAC7D,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE,UAAU,EAAE,GAAG,CAAC,CAAC;IACvC,CAAC;IACH,OAAA,uBAAC;AAAD,CAAC,AAVD,CACU,cAAc,GASvB;;AAED;;GAEG,CACH,IAAA,oBAAA,SAAA,MAAA;IAAuC,UAAA,mBAAA,QAAc;IAArD,SAAA;;IAcA,CAAC;IAbC;;OAEG,CACH,kBAAA,SAAA,CAAA,GAAG,GAAH,SAAI,KAAa,EAAE,UAA6B,EAAE,GAAa;QAC7D,IAAI,KAAK,GAAG,CAAC,EAAE;qLACb,OAAI,CAAC,IAAI,CACP,wCAAsC,IAAI,CAAC,WAAW,CAAC,IAAI,GAAA,OAAK,KAAO,CACxE,CAAC;YACF,OAAO;SACR;QAED,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE,UAAU,EAAE,GAAG,CAAC,CAAC;IACvC,CAAC;IACH,OAAA,iBAAC;AAAD,CAAC,AAdD,CAAuC,cAAc,GAcpD;;AAED;;GAEG,CACH,IAAA,kBAAA,SAAA,MAAA;IAAqC,UAAA,iBAAA,QAAc;IAAnD,SAAA;;IAOA,CAAC;IANC;;OAEG,CACH,gBAAA,SAAA,CAAA,MAAM,GAAN,SAAO,KAAa,EAAE,UAA6B,EAAE,GAAa;QAChE,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE,UAAU,EAAE,GAAG,CAAC,CAAC;IACvC,CAAC;IACH,OAAA,eAAC;AAAD,CAAC,AAPD,CAAqC,cAAc,GAOlD;;AAED;;GAEG,CACH,IAAA,sBAAA,SAAA,MAAA;IAAyC,UAAA,qBAAA,QAAc;IAAvD,SAAA;;IAaA,CAAC;IAZC;;OAEG,CACH,oBAAA,SAAA,CAAA,MAAM,GAAN,SAAO,KAAa,EAAE,UAA6B,EAAE,GAAa;QAChE,IAAI,KAAK,GAAG,CAAC,EAAE;qLACb,OAAI,CAAC,IAAI,CACP,0CAAwC,IAAI,CAAC,WAAW,CAAC,IAAI,GAAA,OAAK,KAAO,CAC1E,CAAC;YACF,OAAO;SACR;QACD,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE,UAAU,EAAE,GAAG,CAAC,CAAC;IACvC,CAAC;IACH,OAAA,mBAAC;AAAD,CAAC,AAbD,CAAyC,cAAc,GAatD;;AAED,IAAA,uBAAA;IAME,SAAA,qBACE,UAAgC,EAChC,cAA4C,EACpC,mBAAuC;QAAvC,IAAA,CAAA,mBAAmB,GAAnB,mBAAmB,CAAoB;QAE/C,IAAI,CAAC,WAAW,GAAG,UAAU,CAAC;QAC9B,IAAI,CAAC,eAAe,GAAG,cAAc,CAAC;IACxC,CAAC;IAED;;OAEG,CACH,qBAAA,SAAA,CAAA,WAAW,GAAX,SAAY,QAA4B;QACtC,IAAI,CAAC,mBAAmB,CAAC,WAAW,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC;IACvD,CAAC;IAED;;OAEG,CACH,qBAAA,SAAA,CAAA,cAAc,GAAd,SAAe,QAA4B;QACzC,IAAI,CAAC,mBAAmB,CAAC,cAAc,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC;IAC1D,CAAC;IACH,OAAA,oBAAC;AAAD,CAAC,AA5BD,IA4BC;;AAED,IAAA,8BAAA,SAAA,MAAA;IACU,UAAA,6BAAA,QAAoB;IAD9B,SAAA;;IAEgC,CAAC;IAAD,OAAA,2BAAC;AAAD,CAAC,AAFjC,CACU,oBAAoB,GACG;;AACjC,IAAA,4BAAA,SAAA,MAAA;IACU,UAAA,2BAAA,QAAoB;IAD9B,SAAA;;IAE8B,CAAC;IAAD,OAAA,yBAAC;AAAD,CAAC,AAF/B,CACU,oBAAoB,GACC;;AAC/B,IAAA,oCAAA,SAAA,MAAA;IACU,UAAA,mCAAA,QAAoB;IAD9B,SAAA;;IAEsC,CAAC;IAAD,OAAA,iCAAC;AAAD,CAAC,AAFvC,CACU,oBAAoB,GACS;;AAEjC,SAAU,sBAAsB,CACpC,EAAW;IAEX,OAAO,EAAE,YAAY,oBAAoB,CAAC;AAC5C,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3917, "column": 0}, "map": {"version": 3, "file": "Meter.js", "sourceRoot": "", "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/node_modules/%40opentelemetry/sdk-node/node_modules/%40opentelemetry/sdk-metrics/src/Meter.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  Meter as IMeter,\n  MetricOptions,\n  Histogram,\n  Counter,\n  UpDownCounter,\n  ObservableGauge,\n  ObservableCounter,\n  ObservableUpDownCounter,\n  BatchObservableCallback,\n  Observable,\n} from '@opentelemetry/api';\nimport {\n  createInstrumentDescriptor,\n  InstrumentType,\n} from './InstrumentDescriptor';\nimport {\n  CounterInstrument,\n  GaugeInstrument,\n  HistogramInstrument,\n  ObservableCounterInstrument,\n  ObservableGaugeInstrument,\n  ObservableUpDownCounterInstrument,\n  UpDownCounterInstrument,\n} from './Instruments';\nimport { MeterSharedState } from './state/MeterSharedState';\nimport { Gauge } from './types';\n\n/**\n * This class implements the {@link IMeter} interface.\n */\nexport class Meter implements IMeter {\n  constructor(private _meterSharedState: MeterSharedState) {}\n\n  /**\n   * Create a {@link Gauge} instrument.\n   */\n  createGauge(name: string, options?: MetricOptions): Gauge {\n    const descriptor = createInstrumentDescriptor(\n      name,\n      InstrumentType.GAUGE,\n      options\n    );\n    const storage = this._meterSharedState.registerMetricStorage(descriptor);\n    return new GaugeInstrument(storage, descriptor);\n  }\n\n  /**\n   * Create a {@link Histogram} instrument.\n   */\n  createHistogram(name: string, options?: MetricOptions): Histogram {\n    const descriptor = createInstrumentDescriptor(\n      name,\n      InstrumentType.HISTOGRAM,\n      options\n    );\n    const storage = this._meterSharedState.registerMetricStorage(descriptor);\n    return new HistogramInstrument(storage, descriptor);\n  }\n\n  /**\n   * Create a {@link Counter} instrument.\n   */\n  createCounter(name: string, options?: MetricOptions): Counter {\n    const descriptor = createInstrumentDescriptor(\n      name,\n      InstrumentType.COUNTER,\n      options\n    );\n    const storage = this._meterSharedState.registerMetricStorage(descriptor);\n    return new CounterInstrument(storage, descriptor);\n  }\n\n  /**\n   * Create a {@link UpDownCounter} instrument.\n   */\n  createUpDownCounter(name: string, options?: MetricOptions): UpDownCounter {\n    const descriptor = createInstrumentDescriptor(\n      name,\n      InstrumentType.UP_DOWN_COUNTER,\n      options\n    );\n    const storage = this._meterSharedState.registerMetricStorage(descriptor);\n    return new UpDownCounterInstrument(storage, descriptor);\n  }\n\n  /**\n   * Create a {@link ObservableGauge} instrument.\n   */\n  createObservableGauge(\n    name: string,\n    options?: MetricOptions\n  ): ObservableGauge {\n    const descriptor = createInstrumentDescriptor(\n      name,\n      InstrumentType.OBSERVABLE_GAUGE,\n      options\n    );\n    const storages =\n      this._meterSharedState.registerAsyncMetricStorage(descriptor);\n    return new ObservableGaugeInstrument(\n      descriptor,\n      storages,\n      this._meterSharedState.observableRegistry\n    );\n  }\n\n  /**\n   * Create a {@link ObservableCounter} instrument.\n   */\n  createObservableCounter(\n    name: string,\n    options?: MetricOptions\n  ): ObservableCounter {\n    const descriptor = createInstrumentDescriptor(\n      name,\n      InstrumentType.OBSERVABLE_COUNTER,\n      options\n    );\n    const storages =\n      this._meterSharedState.registerAsyncMetricStorage(descriptor);\n    return new ObservableCounterInstrument(\n      descriptor,\n      storages,\n      this._meterSharedState.observableRegistry\n    );\n  }\n\n  /**\n   * Create a {@link ObservableUpDownCounter} instrument.\n   */\n  createObservableUpDownCounter(\n    name: string,\n    options?: MetricOptions\n  ): ObservableUpDownCounter {\n    const descriptor = createInstrumentDescriptor(\n      name,\n      InstrumentType.OBSERVABLE_UP_DOWN_COUNTER,\n      options\n    );\n    const storages =\n      this._meterSharedState.registerAsyncMetricStorage(descriptor);\n    return new ObservableUpDownCounterInstrument(\n      descriptor,\n      storages,\n      this._meterSharedState.observableRegistry\n    );\n  }\n\n  /**\n   * @see {@link Meter.addBatchObservableCallback}\n   */\n  addBatchObservableCallback(\n    callback: BatchObservableCallback,\n    observables: Observable[]\n  ) {\n    this._meterSharedState.observableRegistry.addBatchCallback(\n      callback,\n      observables\n    );\n  }\n\n  /**\n   * @see {@link Meter.removeBatchObservableCallback}\n   */\n  removeBatchObservableCallback(\n    callback: BatchObservableCallback,\n    observables: Observable[]\n  ) {\n    this._meterSharedState.observableRegistry.removeBatchCallback(\n      callback,\n      observables\n    );\n  }\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;AAcH,OAAO,EACL,0BAA0B,EAC1B,cAAc,GACf,MAAM,wBAAwB,CAAC;AAChC,OAAO,EACL,iBAAiB,EACjB,eAAe,EACf,mBAAmB,EACnB,2BAA2B,EAC3B,yBAAyB,EACzB,iCAAiC,EACjC,uBAAuB,GACxB,MAAM,eAAe,CAAC;;;AAIvB;;GAEG,CACH,IAAA,QAAA;IACE,SAAA,MAAoB,iBAAmC;QAAnC,IAAA,CAAA,iBAAiB,GAAjB,iBAAiB,CAAkB;IAAG,CAAC;IAE3D;;OAEG,CACH,MAAA,SAAA,CAAA,WAAW,GAAX,SAAY,IAAY,EAAE,OAAuB;QAC/C,IAAM,UAAU,sPAAG,6BAAA,AAA0B,EAC3C,IAAI,EACJ,gQAAc,CAAC,KAAK,EACpB,OAAO,CACR,CAAC;QACF,IAAM,OAAO,GAAG,IAAI,CAAC,iBAAiB,CAAC,qBAAqB,CAAC,UAAU,CAAC,CAAC;QACzE,OAAO,0OAAI,kBAAe,CAAC,OAAO,EAAE,UAAU,CAAC,CAAC;IAClD,CAAC;IAED;;OAEG,CACH,MAAA,SAAA,CAAA,eAAe,GAAf,SAAgB,IAAY,EAAE,OAAuB;QACnD,IAAM,UAAU,sPAAG,6BAAA,AAA0B,EAC3C,IAAI,iPACJ,iBAAc,CAAC,SAAS,EACxB,OAAO,CACR,CAAC;QACF,IAAM,OAAO,GAAG,IAAI,CAAC,iBAAiB,CAAC,qBAAqB,CAAC,UAAU,CAAC,CAAC;QACzE,OAAO,0OAAI,sBAAmB,CAAC,OAAO,EAAE,UAAU,CAAC,CAAC;IACtD,CAAC;IAED;;OAEG,CACH,MAAA,SAAA,CAAA,aAAa,GAAb,SAAc,IAAY,EAAE,OAAuB;QACjD,IAAM,UAAU,IAAG,+QAAA,AAA0B,EAC3C,IAAI,iPACJ,iBAAc,CAAC,OAAO,EACtB,OAAO,CACR,CAAC;QACF,IAAM,OAAO,GAAG,IAAI,CAAC,iBAAiB,CAAC,qBAAqB,CAAC,UAAU,CAAC,CAAC;QACzE,OAAO,0OAAI,oBAAiB,CAAC,OAAO,EAAE,UAAU,CAAC,CAAC;IACpD,CAAC;IAED;;OAEG,CACH,MAAA,SAAA,CAAA,mBAAmB,GAAnB,SAAoB,IAAY,EAAE,OAAuB;QACvD,IAAM,UAAU,sPAAG,6BAAA,AAA0B,EAC3C,IAAI,iPACJ,iBAAc,CAAC,eAAe,EAC9B,OAAO,CACR,CAAC;QACF,IAAM,OAAO,GAAG,IAAI,CAAC,iBAAiB,CAAC,qBAAqB,CAAC,UAAU,CAAC,CAAC;QACzE,OAAO,0OAAI,0BAAuB,CAAC,OAAO,EAAE,UAAU,CAAC,CAAC;IAC1D,CAAC;IAED;;OAEG,CACH,MAAA,SAAA,CAAA,qBAAqB,GAArB,SACE,IAAY,EACZ,OAAuB;QAEvB,IAAM,UAAU,sPAAG,6BAAA,AAA0B,EAC3C,IAAI,iPACJ,iBAAc,CAAC,gBAAgB,EAC/B,OAAO,CACR,CAAC;QACF,IAAM,QAAQ,GACZ,IAAI,CAAC,iBAAiB,CAAC,0BAA0B,CAAC,UAAU,CAAC,CAAC;QAChE,OAAO,0OAAI,4BAAyB,CAClC,UAAU,EACV,QAAQ,EACR,IAAI,CAAC,iBAAiB,CAAC,kBAAkB,CAC1C,CAAC;IACJ,CAAC;IAED;;OAEG,CACH,MAAA,SAAA,CAAA,uBAAuB,GAAvB,SACE,IAAY,EACZ,OAAuB;QAEvB,IAAM,UAAU,sPAAG,6BAAA,AAA0B,EAC3C,IAAI,iPACJ,iBAAc,CAAC,kBAAkB,EACjC,OAAO,CACR,CAAC;QACF,IAAM,QAAQ,GACZ,IAAI,CAAC,iBAAiB,CAAC,0BAA0B,CAAC,UAAU,CAAC,CAAC;QAChE,OAAO,0OAAI,8BAA2B,CACpC,UAAU,EACV,QAAQ,EACR,IAAI,CAAC,iBAAiB,CAAC,kBAAkB,CAC1C,CAAC;IACJ,CAAC;IAED;;OAEG,CACH,MAAA,SAAA,CAAA,6BAA6B,GAA7B,SACE,IAAY,EACZ,OAAuB;QAEvB,IAAM,UAAU,sPAAG,6BAAA,AAA0B,EAC3C,IAAI,iPACJ,iBAAc,CAAC,0BAA0B,EACzC,OAAO,CACR,CAAC;QACF,IAAM,QAAQ,GACZ,IAAI,CAAC,iBAAiB,CAAC,0BAA0B,CAAC,UAAU,CAAC,CAAC;QAChE,OAAO,IAAI,0QAAiC,CAC1C,UAAU,EACV,QAAQ,EACR,IAAI,CAAC,iBAAiB,CAAC,kBAAkB,CAC1C,CAAC;IACJ,CAAC;IAED;;OAEG,CACH,MAAA,SAAA,CAAA,0BAA0B,GAA1B,SACE,QAAiC,EACjC,WAAyB;QAEzB,IAAI,CAAC,iBAAiB,CAAC,kBAAkB,CAAC,gBAAgB,CACxD,QAAQ,EACR,WAAW,CACZ,CAAC;IACJ,CAAC;IAED;;OAEG,CACH,MAAA,SAAA,CAAA,6BAA6B,GAA7B,SACE,QAAiC,EACjC,WAAyB;QAEzB,IAAI,CAAC,iBAAiB,CAAC,kBAAkB,CAAC,mBAAmB,CAC3D,QAAQ,EACR,WAAW,CACZ,CAAC;IACJ,CAAC;IACH,OAAA,KAAC;AAAD,CAAC,AA/ID,IA+IC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4013, "column": 0}, "map": {"version": 3, "file": "MetricStorage.js", "sourceRoot": "", "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/node_modules/%40opentelemetry/sdk-node/node_modules/%40opentelemetry/sdk-metrics/src/state/MetricStorage.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { HrTime } from '@opentelemetry/api';\nimport { MetricData } from '../export/MetricData';\nimport { Maybe } from '../utils';\nimport { MetricCollectorHandle } from './MetricCollector';\nimport {\n  createInstrumentDescriptor,\n  InstrumentDescriptor,\n} from '../InstrumentDescriptor';\n\n/**\n * Internal interface.\n *\n * Represents a storage from which we can collect metrics.\n */\nexport abstract class MetricStorage {\n  constructor(protected _instrumentDescriptor: InstrumentDescriptor) {}\n\n  /**\n   * Collects the metrics from this storage.\n   *\n   * Note: This is a stateful operation and may reset any interval-related\n   * state for the MetricCollector.\n   */\n  abstract collect(\n    collector: MetricCollectorHandle,\n    collectionTime: HrTime\n  ): Maybe<MetricData>;\n\n  getInstrumentDescriptor(): Readonly<InstrumentDescriptor> {\n    return this._instrumentDescriptor;\n  }\n\n  updateDescription(description: string): void {\n    this._instrumentDescriptor = createInstrumentDescriptor(\n      this._instrumentDescriptor.name,\n      this._instrumentDescriptor.type,\n      {\n        description: description,\n        valueType: this._instrumentDescriptor.valueType,\n        unit: this._instrumentDescriptor.unit,\n        advice: this._instrumentDescriptor.advice,\n      }\n    );\n  }\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;AAMH,OAAO,EACL,0BAA0B,GAE3B,MAAM,yBAAyB,CAAC;;AAEjC;;;;GAIG,CACH,IAAA,gBAAA;IACE,SAAA,cAAsB,qBAA2C;QAA3C,IAAA,CAAA,qBAAqB,GAArB,qBAAqB,CAAsB;IAAG,CAAC;IAarE,cAAA,SAAA,CAAA,uBAAuB,GAAvB;QACE,OAAO,IAAI,CAAC,qBAAqB,CAAC;IACpC,CAAC;IAED,cAAA,SAAA,CAAA,iBAAiB,GAAjB,SAAkB,WAAmB;QACnC,IAAI,CAAC,qBAAqB,sPAAG,6BAAA,AAA0B,EACrD,IAAI,CAAC,qBAAqB,CAAC,IAAI,EAC/B,IAAI,CAAC,qBAAqB,CAAC,IAAI,EAC/B;YACE,WAAW,EAAE,WAAW;YACxB,SAAS,EAAE,IAAI,CAAC,qBAAqB,CAAC,SAAS;YAC/C,IAAI,EAAE,IAAI,CAAC,qBAAqB,CAAC,IAAI;YACrC,MAAM,EAAE,IAAI,CAAC,qBAAqB,CAAC,MAAM;SAC1C,CACF,CAAC;IACJ,CAAC;IACH,OAAA,aAAC;AAAD,CAAC,AA9BD,IA8BC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4061, "column": 0}, "map": {"version": 3, "file": "HashMap.js", "sourceRoot": "", "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/node_modules/%40opentelemetry/sdk-node/node_modules/%40opentelemetry/sdk-metrics/src/state/HashMap.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { MetricAttributes } from '@opentelemetry/api';\nimport { hashAttributes } from '../utils';\n\nexport interface Hash<ValueType, HashCodeType> {\n  (value: ValueType): HashCodeType;\n}\n\nexport class HashMap<KeyType, ValueType, HashCodeType> {\n  private _valueMap = new Map<HashCodeType, ValueType>();\n  private _keyMap = new Map<HashCodeType, KeyType>();\n\n  constructor(private _hash: Hash<KeyType, HashCodeType>) {}\n\n  get(key: KeyType, hashCode?: HashCodeType) {\n    hashCode ??= this._hash(key);\n    return this._valueMap.get(hashCode);\n  }\n\n  getOrDefault(key: KeyType, defaultFactory: () => ValueType) {\n    const hash = this._hash(key);\n    if (this._valueMap.has(hash)) {\n      return this._valueMap.get(hash);\n    }\n    const val = defaultFactory();\n    if (!this._keyMap.has(hash)) {\n      this._keyMap.set(hash, key);\n    }\n    this._valueMap.set(hash, val);\n    return val;\n  }\n\n  set(key: KeyType, value: ValueType, hashCode?: HashCodeType) {\n    hashCode ??= this._hash(key);\n    if (!this._keyMap.has(hashCode)) {\n      this._keyMap.set(hashCode, key);\n    }\n    this._valueMap.set(hashCode, value);\n  }\n\n  has(key: KeyType, hashCode?: HashCodeType) {\n    hashCode ??= this._hash(key);\n    return this._valueMap.has(hashCode);\n  }\n\n  *keys(): IterableIterator<[KeyType, HashCodeType]> {\n    const keyIterator = this._keyMap.entries();\n    let next = keyIterator.next();\n    while (next.done !== true) {\n      yield [next.value[1], next.value[0]];\n      next = keyIterator.next();\n    }\n  }\n\n  *entries(): IterableIterator<[KeyType, ValueType, HashCodeType]> {\n    const valueIterator = this._valueMap.entries();\n    let next = valueIterator.next();\n    while (next.done !== true) {\n      // next.value[0] here can not be undefined\n      // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n      yield [this._keyMap.get(next.value[0])!, next.value[1], next.value[0]];\n      next = valueIterator.next();\n    }\n  }\n\n  get size() {\n    return this._valueMap.size;\n  }\n}\n\nexport class AttributeHashMap<ValueType> extends HashMap<\n  MetricAttributes,\n  ValueType,\n  string\n> {\n  constructor() {\n    super(hashAttributes);\n  }\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;;AAGH,OAAO,EAAE,cAAc,EAAE,MAAM,UAAU,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAM1C,IAAA,UAAA;IAIE,SAAA,QAAoB,KAAkC;QAAlC,IAAA,CAAA,KAAK,GAAL,KAAK,CAA6B;QAH9C,IAAA,CAAA,SAAS,GAAG,IAAI,GAAG,EAA2B,CAAC;QAC/C,IAAA,CAAA,OAAO,GAAG,IAAI,GAAG,EAAyB,CAAC;IAEM,CAAC;IAE1D,QAAA,SAAA,CAAA,GAAG,GAAH,SAAI,GAAY,EAAE,QAAuB;QACvC,QAAQ,KAAA,QAAR,QAAQ,KAAA,KAAA,IAAR,QAAQ,GAAR,QAAQ,GAAK,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,EAAC;QAC7B,OAAO,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;IACtC,CAAC;IAED,QAAA,SAAA,CAAA,YAAY,GAAZ,SAAa,GAAY,EAAE,cAA+B;QACxD,IAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;QAC7B,IAAI,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE;YAC5B,OAAO,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;SACjC;QACD,IAAM,GAAG,GAAG,cAAc,EAAE,CAAC;QAC7B,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE;YAC3B,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC;SAC7B;QACD,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC;QAC9B,OAAO,GAAG,CAAC;IACb,CAAC;IAED,QAAA,SAAA,CAAA,GAAG,GAAH,SAAI,GAAY,EAAE,KAAgB,EAAE,QAAuB;QACzD,QAAQ,KAAA,QAAR,QAAQ,KAAA,KAAA,IAAR,QAAQ,GAAR,QAAQ,GAAK,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,EAAC;QAC7B,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE;YAC/B,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,QAAQ,EAAE,GAAG,CAAC,CAAC;SACjC;QACD,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC;IACtC,CAAC;IAED,QAAA,SAAA,CAAA,GAAG,GAAH,SAAI,GAAY,EAAE,QAAuB;QACvC,QAAQ,KAAA,QAAR,QAAQ,KAAA,KAAA,IAAR,QAAQ,GAAR,QAAQ,GAAK,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,EAAC;QAC7B,OAAO,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;IACtC,CAAC;IAEA,QAAA,SAAA,CAAA,IAAI,GAAL;;;;;oBACQ,WAAW,GAAG,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC;oBACvC,IAAI,GAAG,WAAW,CAAC,IAAI,EAAE,CAAC;;;yBACvB,CAAA,IAAI,CAAC,IAAI,KAAK,IAAI,CAAA,EAAA,OAAA;wBAAA,EAAA,OAAA;wBAAA;qBAAA;oBACvB,OAAA;wBAAA,EAAA,OAAA;wBAAM;4BAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;4BAAE,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;yBAAC;qBAAA,CAAA;;oBAApC,GAAA,IAAA,EAAoC,CAAC;oBACrC,IAAI,GAAG,WAAW,CAAC,IAAI,EAAE,CAAC;;;;;;;;;;;KAE7B;IAEA,QAAA,SAAA,CAAA,OAAO,GAAR;;;;;oBACQ,aAAa,GAAG,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE,CAAC;oBAC3C,IAAI,GAAG,aAAa,CAAC,IAAI,EAAE,CAAC;;;yBACzB,CAAA,IAAI,CAAC,IAAI,KAAK,IAAI,CAAA,EAAA,OAAA;wBAAA,EAAA,OAAA;wBAAA;qBAAA;oBACvB,0CAA0C;oBAC1C,oEAAoE;oBACpE,OAAA;wBAAA,EAAA,OAAA;wBAAM;4BAAC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAE;4BAAE,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;4BAAE,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;yBAAC;qBAAA,CAAA;;oBAFtE,0CAA0C;oBAC1C,oEAAoE;oBACpE,GAAA,IAAA,EAAsE,CAAC;oBACvE,IAAI,GAAG,aAAa,CAAC,IAAI,EAAE,CAAC;;;;;;;;;;;KAE/B;IAED,OAAA,cAAA,CAAI,QAAA,SAAA,EAAA,MAAI,EAAA;aAAR;YACE,OAAO,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC;QAC7B,CAAC;;;OAAA;IACH,OAAA,OAAC;AAAD,CAAC,AA5DD,IA4DC;;AAED,IAAA,mBAAA,SAAA,MAAA;IAAiD,UAAA,kBAAA,QAIhD;IACC,SAAA;eACE,OAAA,IAAA,CAAA,IAAA,kOAAM,iBAAc,CAAC,IAAA,IAAA;IACvB,CAAC;IACH,OAAA,gBAAC;AAAD,CAAC,AARD,CAAiD,OAAO,GAQvD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4328, "column": 0}, "map": {"version": 3, "file": "DeltaMetricProcessor.js", "sourceRoot": "", "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/node_modules/%40opentelemetry/sdk-node/node_modules/%40opentelemetry/sdk-metrics/src/state/DeltaMetricProcessor.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { Context, HrTime, MetricAttributes } from '@opentelemetry/api';\nimport { Maybe } from '../utils';\nimport { Accumulation, Aggregator } from '../aggregator/types';\nimport { AttributeHashMap } from './HashMap';\n\n/**\n * Internal interface.\n *\n * Allows synchronous collection of metrics. This processor should allow\n * allocation of new aggregation cells for metrics and convert cumulative\n * recording to delta data points.\n */\nexport class DeltaMetricProcessor<T extends Maybe<Accumulation>> {\n  private _activeCollectionStorage = new AttributeHashMap<T>();\n  // TODO: find a reasonable mean to clean the memo;\n  // https://github.com/open-telemetry/opentelemetry-specification/pull/2208\n  private _cumulativeMemoStorage = new AttributeHashMap<T>();\n\n  constructor(private _aggregator: Aggregator<T>) {}\n\n  record(\n    value: number,\n    attributes: MetricAttributes,\n    _context: Context,\n    collectionTime: HrTime\n  ) {\n    const accumulation = this._activeCollectionStorage.getOrDefault(\n      attributes,\n      () => this._aggregator.createAccumulation(collectionTime)\n    );\n    accumulation?.record(value);\n  }\n\n  batchCumulate(\n    measurements: AttributeHashMap<number>,\n    collectionTime: HrTime\n  ) {\n    Array.from(measurements.entries()).forEach(\n      ([attributes, value, hashCode]) => {\n        const accumulation =\n          this._aggregator.createAccumulation(collectionTime);\n        accumulation?.record(value);\n        let delta = accumulation;\n        // Diff with recorded cumulative memo.\n        if (this._cumulativeMemoStorage.has(attributes, hashCode)) {\n          // has() returned true, previous is present.\n          // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n          const previous = this._cumulativeMemoStorage.get(\n            attributes,\n            hashCode\n          )!;\n          delta = this._aggregator.diff(previous, accumulation);\n        }\n        // Merge with uncollected active delta.\n        if (this._activeCollectionStorage.has(attributes, hashCode)) {\n          // has() returned true, previous is present.\n          // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n          const active = this._activeCollectionStorage.get(\n            attributes,\n            hashCode\n          )!;\n          delta = this._aggregator.merge(active, delta);\n        }\n\n        // Save the current record and the delta record.\n        this._cumulativeMemoStorage.set(attributes, accumulation, hashCode);\n        this._activeCollectionStorage.set(attributes, delta, hashCode);\n      }\n    );\n  }\n\n  /**\n   * Returns a collection of delta metrics. Start time is the when first\n   * time event collected.\n   */\n  collect() {\n    const unreportedDelta = this._activeCollectionStorage;\n    this._activeCollectionStorage = new AttributeHashMap();\n    return unreportedDelta;\n  }\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;AAKH,OAAO,EAAE,gBAAgB,EAAE,MAAM,WAAW,CAAC;;;;;;;;;;;;;;;;;;;;;AAE7C;;;;;;GAMG,CACH,IAAA,uBAAA;IAME,SAAA,qBAAoB,WAA0B;QAA1B,IAAA,CAAA,WAAW,GAAX,WAAW,CAAe;QALtC,IAAA,CAAA,wBAAwB,GAAG,+OAAI,mBAAgB,EAAK,CAAC;QAC7D,kDAAkD;QAClD,0EAA0E;QAClE,IAAA,CAAA,sBAAsB,GAAG,+OAAI,mBAAgB,EAAK,CAAC;IAEV,CAAC;IAElD,qBAAA,SAAA,CAAA,MAAM,GAAN,SACE,KAAa,EACb,UAA4B,EAC5B,QAAiB,EACjB,cAAsB;QAJxB,IAAA,QAAA,IAAA,CAWC;QALC,IAAM,YAAY,GAAG,IAAI,CAAC,wBAAwB,CAAC,YAAY,CAC7D,UAAU,EACV;YAAM,OAAA,KAAI,CAAC,WAAW,CAAC,kBAAkB,CAAC,cAAc,CAAC;QAAnD,CAAmD,CAC1D,CAAC;QACF,YAAY,KAAA,QAAZ,YAAY,KAAA,KAAA,IAAA,KAAA,IAAZ,YAAY,CAAE,MAAM,CAAC,KAAK,CAAC,CAAC;IAC9B,CAAC;IAED,qBAAA,SAAA,CAAA,aAAa,GAAb,SACE,YAAsC,EACtC,cAAsB;QAFxB,IAAA,QAAA,IAAA,CAoCC;QAhCC,KAAK,CAAC,IAAI,CAAC,YAAY,CAAC,OAAO,EAAE,CAAC,CAAC,OAAO,CACxC,SAAC,EAA6B;gBAA7B,KAAA,OAAA,IAAA,EAA6B,EAA5B,UAAU,GAAA,EAAA,CAAA,EAAA,EAAE,KAAK,GAAA,EAAA,CAAA,EAAA,EAAE,QAAQ,GAAA,EAAA,CAAA,EAAA;YAC3B,IAAM,YAAY,GAChB,KAAI,CAAC,WAAW,CAAC,kBAAkB,CAAC,cAAc,CAAC,CAAC;YACtD,YAAY,KAAA,QAAZ,YAAY,KAAA,KAAA,IAAA,KAAA,IAAZ,YAAY,CAAE,MAAM,CAAC,KAAK,CAAC,CAAC;YAC5B,IAAI,KAAK,GAAG,YAAY,CAAC;YACzB,sCAAsC;YACtC,IAAI,KAAI,CAAC,sBAAsB,CAAC,GAAG,CAAC,UAAU,EAAE,QAAQ,CAAC,EAAE;gBACzD,4CAA4C;gBAC5C,oEAAoE;gBACpE,IAAM,QAAQ,GAAG,KAAI,CAAC,sBAAsB,CAAC,GAAG,CAC9C,UAAU,EACV,QAAQ,CACR,CAAC;gBACH,KAAK,GAAG,KAAI,CAAC,WAAW,CAAC,IAAI,CAAC,QAAQ,EAAE,YAAY,CAAC,CAAC;aACvD;YACD,uCAAuC;YACvC,IAAI,KAAI,CAAC,wBAAwB,CAAC,GAAG,CAAC,UAAU,EAAE,QAAQ,CAAC,EAAE;gBAC3D,4CAA4C;gBAC5C,oEAAoE;gBACpE,IAAM,MAAM,GAAG,KAAI,CAAC,wBAAwB,CAAC,GAAG,CAC9C,UAAU,EACV,QAAQ,CACR,CAAC;gBACH,KAAK,GAAG,KAAI,CAAC,WAAW,CAAC,KAAK,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;aAC/C;YAED,gDAAgD;YAChD,KAAI,CAAC,sBAAsB,CAAC,GAAG,CAAC,UAAU,EAAE,YAAY,EAAE,QAAQ,CAAC,CAAC;YACpE,KAAI,CAAC,wBAAwB,CAAC,GAAG,CAAC,UAAU,EAAE,KAAK,EAAE,QAAQ,CAAC,CAAC;QACjE,CAAC,CACF,CAAC;IACJ,CAAC;IAED;;;OAGG,CACH,qBAAA,SAAA,CAAA,OAAO,GAAP;QACE,IAAM,eAAe,GAAG,IAAI,CAAC,wBAAwB,CAAC;QACtD,IAAI,CAAC,wBAAwB,GAAG,+OAAI,mBAAgB,EAAE,CAAC;QACvD,OAAO,eAAe,CAAC;IACzB,CAAC;IACH,OAAA,oBAAC;AAAD,CAAC,AApED,IAoEC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4431, "column": 0}, "map": {"version": 3, "file": "TemporalMetricProcessor.js", "sourceRoot": "", "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/node_modules/%40opentelemetry/sdk-node/node_modules/%40opentelemetry/sdk-metrics/src/state/TemporalMetricProcessor.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { HrTime } from '@opentelemetry/api';\nimport {\n  Accumulation,\n  AccumulationRecord,\n  Aggregator,\n} from '../aggregator/types';\nimport { MetricData } from '../export/MetricData';\nimport { InstrumentDescriptor } from '../InstrumentDescriptor';\nimport { AggregationTemporality } from '../export/AggregationTemporality';\nimport { Maybe } from '../utils';\nimport { MetricCollectorHandle } from './MetricCollector';\nimport { AttributeHashMap } from './HashMap';\n\n/**\n * Remembers what was presented to a specific exporter.\n */\ninterface LastReportedHistory<T extends Maybe<Accumulation>> {\n  /**\n   * The last accumulation of metric data.\n   */\n  accumulations: AttributeHashMap<T>;\n  /**\n   * The timestamp the data was reported.\n   */\n  collectionTime: HrTime;\n  /**\n   * The AggregationTemporality used to aggregate reports.\n   */\n  aggregationTemporality: AggregationTemporality;\n}\n\n/**\n * Internal interface.\n *\n * Provides unique reporting for each collector. Allows synchronous collection\n * of metrics and reports given temporality values.\n */\nexport class TemporalMetricProcessor<T extends Maybe<Accumulation>> {\n  private _unreportedAccumulations = new Map<\n    MetricCollectorHandle,\n    AttributeHashMap<T>[]\n  >();\n  private _reportHistory = new Map<\n    MetricCollectorHandle,\n    LastReportedHistory<T>\n  >();\n\n  constructor(\n    private _aggregator: Aggregator<T>,\n    collectorHandles: MetricCollectorHandle[]\n  ) {\n    collectorHandles.forEach(handle => {\n      this._unreportedAccumulations.set(handle, []);\n    });\n  }\n\n  /**\n   * Builds the {@link MetricData} streams to report against a specific MetricCollector.\n   * @param collector The information of the MetricCollector.\n   * @param collectors The registered collectors.\n   * @param instrumentDescriptor The instrumentation descriptor that these metrics generated with.\n   * @param currentAccumulations The current accumulation of metric data from instruments.\n   * @param collectionTime The current collection timestamp.\n   * @returns The {@link MetricData} points or `null`.\n   */\n  buildMetrics(\n    collector: MetricCollectorHandle,\n    instrumentDescriptor: InstrumentDescriptor,\n    currentAccumulations: AttributeHashMap<T>,\n    collectionTime: HrTime\n  ): Maybe<MetricData> {\n    this._stashAccumulations(currentAccumulations);\n    const unreportedAccumulations =\n      this._getMergedUnreportedAccumulations(collector);\n\n    let result = unreportedAccumulations;\n    let aggregationTemporality: AggregationTemporality;\n    // Check our last report time.\n    if (this._reportHistory.has(collector)) {\n      // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n      const last = this._reportHistory.get(collector)!;\n      const lastCollectionTime = last.collectionTime;\n      aggregationTemporality = last.aggregationTemporality;\n\n      // Use aggregation temporality + instrument to determine if we do a merge or a diff of\n      // previous. We have the following four scenarios:\n      // 1. Cumulative Aggregation (temporality) + Delta recording (sync instrument).\n      //    Here we merge with our last record to get a cumulative aggregation.\n      // 2. Cumulative Aggregation + Cumulative recording (async instrument).\n      //    Cumulative records are converted to delta recording with DeltaMetricProcessor.\n      //    Here we merge with our last record to get a cumulative aggregation.\n      // 3. Delta Aggregation + Delta recording\n      //    Calibrate the startTime of metric streams to be the reader's lastCollectionTime.\n      // 4. Delta Aggregation + Cumulative recording.\n      //    Cumulative records are converted to delta recording with DeltaMetricProcessor.\n      //    Calibrate the startTime of metric streams to be the reader's lastCollectionTime.\n      if (aggregationTemporality === AggregationTemporality.CUMULATIVE) {\n        // We need to make sure the current delta recording gets merged into the previous cumulative\n        // for the next cumulative recording.\n        result = TemporalMetricProcessor.merge(\n          last.accumulations,\n          unreportedAccumulations,\n          this._aggregator\n        );\n      } else {\n        result = TemporalMetricProcessor.calibrateStartTime(\n          last.accumulations,\n          unreportedAccumulations,\n          lastCollectionTime\n        );\n      }\n    } else {\n      // Call into user code to select aggregation temporality for the instrument.\n      aggregationTemporality = collector.selectAggregationTemporality(\n        instrumentDescriptor.type\n      );\n    }\n\n    // Update last reported (cumulative) accumulation.\n    this._reportHistory.set(collector, {\n      accumulations: result,\n      collectionTime,\n      aggregationTemporality,\n    });\n\n    const accumulationRecords = AttributesMapToAccumulationRecords(result);\n\n    // do not convert to metric data if there is nothing to convert.\n    if (accumulationRecords.length === 0) {\n      return undefined;\n    }\n\n    return this._aggregator.toMetricData(\n      instrumentDescriptor,\n      aggregationTemporality,\n      accumulationRecords,\n      /* endTime */ collectionTime\n    );\n  }\n\n  private _stashAccumulations(currentAccumulation: AttributeHashMap<T>) {\n    const registeredCollectors = this._unreportedAccumulations.keys();\n    for (const collector of registeredCollectors) {\n      let stash = this._unreportedAccumulations.get(collector);\n      if (stash === undefined) {\n        stash = [];\n        this._unreportedAccumulations.set(collector, stash);\n      }\n      stash.push(currentAccumulation);\n    }\n  }\n\n  private _getMergedUnreportedAccumulations(collector: MetricCollectorHandle) {\n    let result = new AttributeHashMap<T>();\n    const unreportedList = this._unreportedAccumulations.get(collector);\n    this._unreportedAccumulations.set(collector, []);\n    if (unreportedList === undefined) {\n      return result;\n    }\n    for (const it of unreportedList) {\n      result = TemporalMetricProcessor.merge(result, it, this._aggregator);\n    }\n    return result;\n  }\n\n  static merge<T extends Maybe<Accumulation>>(\n    last: AttributeHashMap<T>,\n    current: AttributeHashMap<T>,\n    aggregator: Aggregator<T>\n  ) {\n    const result = last;\n    const iterator = current.entries();\n    let next = iterator.next();\n    while (next.done !== true) {\n      const [key, record, hash] = next.value;\n      if (last.has(key, hash)) {\n        const lastAccumulation = last.get(key, hash);\n        // last.has() returned true, lastAccumulation is present.\n        // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n        const accumulation = aggregator.merge(lastAccumulation!, record);\n        result.set(key, accumulation, hash);\n      } else {\n        result.set(key, record, hash);\n      }\n\n      next = iterator.next();\n    }\n    return result;\n  }\n\n  /**\n   * Calibrate the reported metric streams' startTime to lastCollectionTime. Leaves\n   * the new stream to be the initial observation time unchanged.\n   */\n  static calibrateStartTime<T extends Maybe<Accumulation>>(\n    last: AttributeHashMap<T>,\n    current: AttributeHashMap<T>,\n    lastCollectionTime: HrTime\n  ) {\n    for (const [key, hash] of last.keys()) {\n      const currentAccumulation = current.get(key, hash);\n      currentAccumulation?.setStartTime(lastCollectionTime);\n    }\n    return current;\n  }\n}\n\n// TypeScript complains about converting 3 elements tuple to AccumulationRecord<T>.\nfunction AttributesMapToAccumulationRecords<T>(\n  map: AttributeHashMap<T>\n): AccumulationRecord<T>[] {\n  return Array.from(map.entries()) as unknown as AccumulationRecord<T>[];\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;AAUH,OAAO,EAAE,sBAAsB,EAAE,MAAM,kCAAkC,CAAC;AAG1E,OAAO,EAAE,gBAAgB,EAAE,MAAM,WAAW,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAoB7C;;;;;GAKG,CACH,IAAA,0BAAA;IAUE,SAAA,wBACU,WAA0B,EAClC,gBAAyC;QAF3C,IAAA,QAAA,IAAA,CAOC;QANS,IAAA,CAAA,WAAW,GAAX,WAAW,CAAe;QAV5B,IAAA,CAAA,wBAAwB,GAAG,IAAI,GAAG,EAGvC,CAAC;QACI,IAAA,CAAA,cAAc,GAAG,IAAI,GAAG,EAG7B,CAAC;QAMF,gBAAgB,CAAC,OAAO,CAAC,SAAA,MAAM;YAC7B,KAAI,CAAC,wBAAwB,CAAC,GAAG,CAAC,MAAM,EAAE,EAAE,CAAC,CAAC;QAChD,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;;;;;;;OAQG,CACH,wBAAA,SAAA,CAAA,YAAY,GAAZ,SACE,SAAgC,EAChC,oBAA0C,EAC1C,oBAAyC,EACzC,cAAsB;QAEtB,IAAI,CAAC,mBAAmB,CAAC,oBAAoB,CAAC,CAAC;QAC/C,IAAM,uBAAuB,GAC3B,IAAI,CAAC,iCAAiC,CAAC,SAAS,CAAC,CAAC;QAEpD,IAAI,MAAM,GAAG,uBAAuB,CAAC;QACrC,IAAI,sBAA8C,CAAC;QACnD,8BAA8B;QAC9B,IAAI,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,SAAS,CAAC,EAAE;YACtC,oEAAoE;YACpE,IAAM,IAAI,GAAG,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,SAAS,CAAE,CAAC;YACjD,IAAM,kBAAkB,GAAG,IAAI,CAAC,cAAc,CAAC;YAC/C,sBAAsB,GAAG,IAAI,CAAC,sBAAsB,CAAC;YAErD,sFAAsF;YACtF,kDAAkD;YAClD,+EAA+E;YAC/E,yEAAyE;YACzE,uEAAuE;YACvE,oFAAoF;YACpF,yEAAyE;YACzE,yCAAyC;YACzC,sFAAsF;YACtF,+CAA+C;YAC/C,oFAAoF;YACpF,sFAAsF;YACtF,IAAI,sBAAsB,gQAAK,yBAAsB,CAAC,UAAU,EAAE;gBAChE,4FAA4F;gBAC5F,qCAAqC;gBACrC,MAAM,GAAG,uBAAuB,CAAC,KAAK,CACpC,IAAI,CAAC,aAAa,EAClB,uBAAuB,EACvB,IAAI,CAAC,WAAW,CACjB,CAAC;aACH,MAAM;gBACL,MAAM,GAAG,uBAAuB,CAAC,kBAAkB,CACjD,IAAI,CAAC,aAAa,EAClB,uBAAuB,EACvB,kBAAkB,CACnB,CAAC;aACH;SACF,MAAM;YACL,4EAA4E;YAC5E,sBAAsB,GAAG,SAAS,CAAC,4BAA4B,CAC7D,oBAAoB,CAAC,IAAI,CAC1B,CAAC;SACH;QAED,kDAAkD;QAClD,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,SAAS,EAAE;YACjC,aAAa,EAAE,MAAM;YACrB,cAAc,EAAA,cAAA;YACd,sBAAsB,EAAA,sBAAA;SACvB,CAAC,CAAC;QAEH,IAAM,mBAAmB,GAAG,kCAAkC,CAAC,MAAM,CAAC,CAAC;QAEvE,gEAAgE;QAChE,IAAI,mBAAmB,CAAC,MAAM,KAAK,CAAC,EAAE;YACpC,OAAO,SAAS,CAAC;SAClB;QAED,OAAO,IAAI,CAAC,WAAW,CAAC,YAAY,CAClC,oBAAoB,EACpB,sBAAsB,EACtB,mBAAmB,EACnB,WAAA,EAAa,CAAC,cAAc,CAC7B,CAAC;IACJ,CAAC;IAEO,wBAAA,SAAA,CAAA,mBAAmB,GAA3B,SAA4B,mBAAwC;;QAClE,IAAM,oBAAoB,GAAG,IAAI,CAAC,wBAAwB,CAAC,IAAI,EAAE,CAAC;;YAClE,IAAwB,IAAA,yBAAA,SAAA,oBAAoB,CAAA,EAAA,2BAAA,uBAAA,IAAA,EAAA,EAAA,CAAA,yBAAA,IAAA,EAAA,2BAAA,uBAAA,IAAA,GAAE;gBAAzC,IAAM,SAAS,GAAA,yBAAA,KAAA;gBAClB,IAAI,KAAK,GAAG,IAAI,CAAC,wBAAwB,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;gBACzD,IAAI,KAAK,KAAK,SAAS,EAAE;oBACvB,KAAK,GAAG,EAAE,CAAC;oBACX,IAAI,CAAC,wBAAwB,CAAC,GAAG,CAAC,SAAS,EAAE,KAAK,CAAC,CAAC;iBACrD;gBACD,KAAK,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC;aACjC;;;;;;;;;;;;IACH,CAAC;IAEO,wBAAA,SAAA,CAAA,iCAAiC,GAAzC,SAA0C,SAAgC;;QACxE,IAAI,MAAM,GAAG,+OAAI,mBAAgB,EAAK,CAAC;QACvC,IAAM,cAAc,GAAG,IAAI,CAAC,wBAAwB,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;QACpE,IAAI,CAAC,wBAAwB,CAAC,GAAG,CAAC,SAAS,EAAE,EAAE,CAAC,CAAC;QACjD,IAAI,cAAc,KAAK,SAAS,EAAE;YAChC,OAAO,MAAM,CAAC;SACf;;YACD,IAAiB,IAAA,mBAAA,SAAA,cAAc,CAAA,EAAA,qBAAA,iBAAA,IAAA,EAAA,EAAA,CAAA,mBAAA,IAAA,EAAA,qBAAA,iBAAA,IAAA,GAAE;gBAA5B,IAAM,IAAE,GAAA,mBAAA,KAAA;gBACX,MAAM,GAAG,uBAAuB,CAAC,KAAK,CAAC,MAAM,EAAE,IAAE,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC;aACtE;;;;;;;;;;;;QACD,OAAO,MAAM,CAAC;IAChB,CAAC;IAEM,wBAAA,KAAK,GAAZ,SACE,IAAyB,EACzB,OAA4B,EAC5B,UAAyB;QAEzB,IAAM,MAAM,GAAG,IAAI,CAAC;QACpB,IAAM,QAAQ,GAAG,OAAO,CAAC,OAAO,EAAE,CAAC;QACnC,IAAI,IAAI,GAAG,QAAQ,CAAC,IAAI,EAAE,CAAC;QAC3B,MAAO,IAAI,CAAC,IAAI,KAAK,IAAI,CAAE;YACnB,IAAA,KAAA,OAAsB,IAAI,CAAC,KAAK,EAAA,EAAA,EAA/B,GAAG,GAAA,EAAA,CAAA,EAAA,EAAE,MAAM,GAAA,EAAA,CAAA,EAAA,EAAE,IAAI,GAAA,EAAA,CAAA,EAAc,CAAC;YACvC,IAAI,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,IAAI,CAAC,EAAE;gBACvB,IAAM,gBAAgB,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC;gBAC7C,yDAAyD;gBACzD,oEAAoE;gBACpE,IAAM,YAAY,GAAG,UAAU,CAAC,KAAK,CAAC,gBAAiB,EAAE,MAAM,CAAC,CAAC;gBACjE,MAAM,CAAC,GAAG,CAAC,GAAG,EAAE,YAAY,EAAE,IAAI,CAAC,CAAC;aACrC,MAAM;gBACL,MAAM,CAAC,GAAG,CAAC,GAAG,EAAE,MAAM,EAAE,IAAI,CAAC,CAAC;aAC/B;YAED,IAAI,GAAG,QAAQ,CAAC,IAAI,EAAE,CAAC;SACxB;QACD,OAAO,MAAM,CAAC;IAChB,CAAC;IAED;;;OAGG,CACI,wBAAA,kBAAkB,GAAzB,SACE,IAAyB,EACzB,OAA4B,EAC5B,kBAA0B;;;YAE1B,IAA0B,IAAA,KAAA,SAAA,IAAI,CAAC,IAAI,EAAE,CAAA,EAAA,KAAA,GAAA,IAAA,EAAA,EAAA,CAAA,GAAA,IAAA,EAAA,KAAA,GAAA,IAAA,GAAE;gBAA5B,IAAA,KAAA,OAAA,GAAA,KAAA,EAAA,EAAW,EAAV,GAAG,GAAA,EAAA,CAAA,EAAA,EAAE,IAAI,GAAA,EAAA,CAAA,EAAA;gBACnB,IAAM,mBAAmB,GAAG,OAAO,CAAC,GAAG,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC;gBACnD,mBAAmB,KAAA,QAAnB,mBAAmB,KAAA,KAAA,IAAA,KAAA,IAAnB,mBAAmB,CAAE,YAAY,CAAC,kBAAkB,CAAC,CAAC;aACvD;;;;;;;;;;;;QACD,OAAO,OAAO,CAAC;IACjB,CAAC;IACH,OAAA,uBAAC;AAAD,CAAC,AAxKD,IAwKC;;AAED,mFAAmF;AACnF,SAAS,kCAAkC,CACzC,GAAwB;IAExB,OAAO,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,OAAO,EAAE,CAAuC,CAAC;AACzE,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4662, "column": 0}, "map": {"version": 3, "file": "AsyncMetricStorage.js", "sourceRoot": "", "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/node_modules/%40opentelemetry/sdk-node/node_modules/%40opentelemetry/sdk-metrics/src/state/AsyncMetricStorage.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { HrTime } from '@opentelemetry/api';\nimport { Accumulation, Aggregator } from '../aggregator/types';\nimport { InstrumentDescriptor } from '../InstrumentDescriptor';\nimport { AttributesProcessor } from '../view/AttributesProcessor';\nimport { MetricStorage } from './MetricStorage';\nimport { MetricData } from '../export/MetricData';\nimport { DeltaMetricProcessor } from './DeltaMetricProcessor';\nimport { TemporalMetricProcessor } from './TemporalMetricProcessor';\nimport { Maybe } from '../utils';\nimport { MetricCollectorHandle } from './MetricCollector';\nimport { AttributeHashMap } from './HashMap';\nimport { AsyncWritableMetricStorage } from './WritableMetricStorage';\n\n/**\n * Internal interface.\n *\n * Stores and aggregates {@link MetricData} for asynchronous instruments.\n */\nexport class AsyncMetricStorage<T extends Maybe<Accumulation>>\n  extends MetricStorage\n  implements AsyncWritableMetricStorage\n{\n  private _deltaMetricStorage: DeltaMetricProcessor<T>;\n  private _temporalMetricStorage: TemporalMetricProcessor<T>;\n\n  constructor(\n    _instrumentDescriptor: InstrumentDescriptor,\n    aggregator: Aggregator<T>,\n    private _attributesProcessor: AttributesProcessor,\n    collectorHandles: MetricCollectorHandle[]\n  ) {\n    super(_instrumentDescriptor);\n    this._deltaMetricStorage = new DeltaMetricProcessor(aggregator);\n    this._temporalMetricStorage = new TemporalMetricProcessor(\n      aggregator,\n      collectorHandles\n    );\n  }\n\n  record(measurements: AttributeHashMap<number>, observationTime: HrTime) {\n    const processed = new AttributeHashMap<number>();\n    Array.from(measurements.entries()).forEach(([attributes, value]) => {\n      processed.set(this._attributesProcessor.process(attributes), value);\n    });\n    this._deltaMetricStorage.batchCumulate(processed, observationTime);\n  }\n\n  /**\n   * Collects the metrics from this storage. The ObservableCallback is invoked\n   * during the collection.\n   *\n   * Note: This is a stateful operation and may reset any interval-related\n   * state for the MetricCollector.\n   */\n  collect(\n    collector: MetricCollectorHandle,\n    collectionTime: HrTime\n  ): Maybe<MetricData> {\n    const accumulations = this._deltaMetricStorage.collect();\n\n    return this._temporalMetricStorage.buildMetrics(\n      collector,\n      this._instrumentDescriptor,\n      accumulations,\n      collectionTime\n    );\n  }\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;AAMH,OAAO,EAAE,aAAa,EAAE,MAAM,iBAAiB,CAAC;AAEhD,OAAO,EAAE,oBAAoB,EAAE,MAAM,wBAAwB,CAAC;AAC9D,OAAO,EAAE,uBAAuB,EAAE,MAAM,2BAA2B,CAAC;AAGpE,OAAO,EAAE,gBAAgB,EAAE,MAAM,WAAW,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAG7C;;;;GAIG,CACH,IAAA,qBAAA,SAAA,MAAA;IACU,UAAA,oBAAA,QAAa;IAMrB,SAAA,mBACE,qBAA2C,EAC3C,UAAyB,EACjB,oBAAyC,EACjD,gBAAyC;QAJ3C,IAAA,QAME,OAAA,IAAA,CAAA,IAAA,EAAM,qBAAqB,CAAC,IAAA,IAAA,CAM7B;QATS,MAAA,oBAAoB,GAApB,oBAAoB,CAAqB;QAIjD,KAAI,CAAC,mBAAmB,GAAG,IAAI,+QAAoB,CAAC,UAAU,CAAC,CAAC;QAChE,KAAI,CAAC,sBAAsB,GAAG,+PAAI,0BAAuB,CACvD,UAAU,EACV,gBAAgB,CACjB,CAAC;;IACJ,CAAC;IAED,mBAAA,SAAA,CAAA,MAAM,GAAN,SAAO,YAAsC,EAAE,eAAuB;QAAtE,IAAA,QAAA,IAAA,CAMC;QALC,IAAM,SAAS,GAAG,+OAAI,mBAAgB,EAAU,CAAC;QACjD,KAAK,CAAC,IAAI,CAAC,YAAY,CAAC,OAAO,EAAE,CAAC,CAAC,OAAO,CAAC,SAAC,EAAmB;gBAAnB,KAAA,OAAA,IAAA,EAAmB,EAAlB,UAAU,GAAA,EAAA,CAAA,EAAA,EAAE,KAAK,GAAA,EAAA,CAAA,EAAA;YAC5D,SAAS,CAAC,GAAG,CAAC,KAAI,CAAC,oBAAoB,CAAC,OAAO,CAAC,UAAU,CAAC,EAAE,KAAK,CAAC,CAAC;QACtE,CAAC,CAAC,CAAC;QACH,IAAI,CAAC,mBAAmB,CAAC,aAAa,CAAC,SAAS,EAAE,eAAe,CAAC,CAAC;IACrE,CAAC;IAED;;;;;;OAMG,CACH,mBAAA,SAAA,CAAA,OAAO,GAAP,SACE,SAAgC,EAChC,cAAsB;QAEtB,IAAM,aAAa,GAAG,IAAI,CAAC,mBAAmB,CAAC,OAAO,EAAE,CAAC;QAEzD,OAAO,IAAI,CAAC,sBAAsB,CAAC,YAAY,CAC7C,SAAS,EACT,IAAI,CAAC,qBAAqB,EAC1B,aAAa,EACb,cAAc,CACf,CAAC;IACJ,CAAC;IACH,OAAA,kBAAC;AAAD,CAAC,AAjDD,kPACU,gBAAa,GAgDtB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4768, "column": 0}, "map": {"version": 3, "file": "RegistrationConflicts.js", "sourceRoot": "", "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/node_modules/%40opentelemetry/sdk-node/node_modules/%40opentelemetry/sdk-metrics/src/view/RegistrationConflicts.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { InstrumentSelectorCriteria } from './InstrumentSelector';\nimport { InstrumentDescriptor } from '../InstrumentDescriptor';\n\nexport function getIncompatibilityDetails(\n  existing: InstrumentDescriptor,\n  otherDescriptor: InstrumentDescriptor\n) {\n  let incompatibility = '';\n  if (existing.unit !== otherDescriptor.unit) {\n    incompatibility += `\\t- Unit '${existing.unit}' does not match '${otherDescriptor.unit}'\\n`;\n  }\n  if (existing.type !== otherDescriptor.type) {\n    incompatibility += `\\t- Type '${existing.type}' does not match '${otherDescriptor.type}'\\n`;\n  }\n  if (existing.valueType !== otherDescriptor.valueType) {\n    incompatibility += `\\t- Value Type '${existing.valueType}' does not match '${otherDescriptor.valueType}'\\n`;\n  }\n  if (existing.description !== otherDescriptor.description) {\n    incompatibility += `\\t- Description '${existing.description}' does not match '${otherDescriptor.description}'\\n`;\n  }\n\n  return incompatibility;\n}\n\nexport function getValueTypeConflictResolutionRecipe(\n  existing: InstrumentDescriptor,\n  otherDescriptor: InstrumentDescriptor\n) {\n  return `\\t- use valueType '${existing.valueType}' on instrument creation or use an instrument name other than '${otherDescriptor.name}'`;\n}\n\nexport function getUnitConflictResolutionRecipe(\n  existing: InstrumentDescriptor,\n  otherDescriptor: InstrumentDescriptor\n) {\n  return `\\t- use unit '${existing.unit}' on instrument creation or use an instrument name other than '${otherDescriptor.name}'`;\n}\n\nexport function getTypeConflictResolutionRecipe(\n  existing: InstrumentDescriptor,\n  otherDescriptor: InstrumentDescriptor\n) {\n  const selector: InstrumentSelectorCriteria = {\n    name: otherDescriptor.name,\n    type: otherDescriptor.type,\n    unit: otherDescriptor.unit,\n  };\n\n  const selectorString = JSON.stringify(selector);\n\n  return `\\t- create a new view with a name other than '${existing.name}' and InstrumentSelector '${selectorString}'`;\n}\n\nexport function getDescriptionResolutionRecipe(\n  existing: InstrumentDescriptor,\n  otherDescriptor: InstrumentDescriptor\n): string {\n  const selector: InstrumentSelectorCriteria = {\n    name: otherDescriptor.name,\n    type: otherDescriptor.type,\n    unit: otherDescriptor.unit,\n  };\n\n  const selectorString = JSON.stringify(selector);\n\n  return `\\t- create a new view with a name other than '${existing.name}' and InstrumentSelector '${selectorString}'\n    \\t- OR - create a new view with the name ${existing.name} and description '${existing.description}' and InstrumentSelector ${selectorString}\n    \\t- OR - create a new view with the name ${otherDescriptor.name} and description '${existing.description}' and InstrumentSelector ${selectorString}`;\n}\n\nexport function getConflictResolutionRecipe(\n  existing: InstrumentDescriptor,\n  otherDescriptor: InstrumentDescriptor\n): string {\n  // Conflicts that cannot be solved via views.\n  if (existing.valueType !== otherDescriptor.valueType) {\n    return getValueTypeConflictResolutionRecipe(existing, otherDescriptor);\n  }\n\n  if (existing.unit !== otherDescriptor.unit) {\n    return getUnitConflictResolutionRecipe(existing, otherDescriptor);\n  }\n\n  // Conflicts that can be solved via views.\n  if (existing.type !== otherDescriptor.type) {\n    // this will automatically solve possible description conflicts.\n    return getTypeConflictResolutionRecipe(existing, otherDescriptor);\n  }\n\n  if (existing.description !== otherDescriptor.description) {\n    return getDescriptionResolutionRecipe(existing, otherDescriptor);\n  }\n\n  return '';\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;;;;;;AAKG,SAAU,yBAAyB,CACvC,QAA8B,EAC9B,eAAqC;IAErC,IAAI,eAAe,GAAG,EAAE,CAAC;IACzB,IAAI,QAAQ,CAAC,IAAI,KAAK,eAAe,CAAC,IAAI,EAAE;QAC1C,eAAe,IAAI,eAAa,QAAQ,CAAC,IAAI,GAAA,uBAAqB,eAAe,CAAC,IAAI,GAAA,KAAK,CAAC;KAC7F;IACD,IAAI,QAAQ,CAAC,IAAI,KAAK,eAAe,CAAC,IAAI,EAAE;QAC1C,eAAe,IAAI,eAAa,QAAQ,CAAC,IAAI,GAAA,uBAAqB,eAAe,CAAC,IAAI,GAAA,KAAK,CAAC;KAC7F;IACD,IAAI,QAAQ,CAAC,SAAS,KAAK,eAAe,CAAC,SAAS,EAAE;QACpD,eAAe,IAAI,qBAAmB,QAAQ,CAAC,SAAS,GAAA,uBAAqB,eAAe,CAAC,SAAS,GAAA,KAAK,CAAC;KAC7G;IACD,IAAI,QAAQ,CAAC,WAAW,KAAK,eAAe,CAAC,WAAW,EAAE;QACxD,eAAe,IAAI,sBAAoB,QAAQ,CAAC,WAAW,GAAA,uBAAqB,eAAe,CAAC,WAAW,GAAA,KAAK,CAAC;KAClH;IAED,OAAO,eAAe,CAAC;AACzB,CAAC;AAEK,SAAU,oCAAoC,CAClD,QAA8B,EAC9B,eAAqC;IAErC,OAAO,wBAAsB,QAAQ,CAAC,SAAS,GAAA,oEAAkE,eAAe,CAAC,IAAI,GAAA,GAAG,CAAC;AAC3I,CAAC;AAEK,SAAU,+BAA+B,CAC7C,QAA8B,EAC9B,eAAqC;IAErC,OAAO,mBAAiB,QAAQ,CAAC,IAAI,GAAA,oEAAkE,eAAe,CAAC,IAAI,GAAA,GAAG,CAAC;AACjI,CAAC;AAEK,SAAU,+BAA+B,CAC7C,QAA8B,EAC9B,eAAqC;IAErC,IAAM,QAAQ,GAA+B;QAC3C,IAAI,EAAE,eAAe,CAAC,IAAI;QAC1B,IAAI,EAAE,eAAe,CAAC,IAAI;QAC1B,IAAI,EAAE,eAAe,CAAC,IAAI;KAC3B,CAAC;IAEF,IAAM,cAAc,GAAG,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC;IAEhD,OAAO,mDAAiD,QAAQ,CAAC,IAAI,GAAA,+BAA6B,cAAc,GAAA,GAAG,CAAC;AACtH,CAAC;AAEK,SAAU,8BAA8B,CAC5C,QAA8B,EAC9B,eAAqC;IAErC,IAAM,QAAQ,GAA+B;QAC3C,IAAI,EAAE,eAAe,CAAC,IAAI;QAC1B,IAAI,EAAE,eAAe,CAAC,IAAI;QAC1B,IAAI,EAAE,eAAe,CAAC,IAAI;KAC3B,CAAC;IAEF,IAAM,cAAc,GAAG,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC;IAEhD,OAAO,mDAAiD,QAAQ,CAAC,IAAI,GAAA,+BAA6B,cAAc,GAAA,qDACnE,QAAQ,CAAC,IAAI,GAAA,uBAAqB,QAAQ,CAAC,WAAW,GAAA,8BAA4B,cAAc,GAAA,oDAChG,eAAe,CAAC,IAAI,GAAA,uBAAqB,QAAQ,CAAC,WAAW,GAAA,8BAA4B,cAAgB,CAAC;AACzJ,CAAC;AAEK,SAAU,2BAA2B,CACzC,QAA8B,EAC9B,eAAqC;IAErC,6CAA6C;IAC7C,IAAI,QAAQ,CAAC,SAAS,KAAK,eAAe,CAAC,SAAS,EAAE;QACpD,OAAO,oCAAoC,CAAC,QAAQ,EAAE,eAAe,CAAC,CAAC;KACxE;IAED,IAAI,QAAQ,CAAC,IAAI,KAAK,eAAe,CAAC,IAAI,EAAE;QAC1C,OAAO,+BAA+B,CAAC,QAAQ,EAAE,eAAe,CAAC,CAAC;KACnE;IAED,0CAA0C;IAC1C,IAAI,QAAQ,CAAC,IAAI,KAAK,eAAe,CAAC,IAAI,EAAE;QAC1C,gEAAgE;QAChE,OAAO,+BAA+B,CAAC,QAAQ,EAAE,eAAe,CAAC,CAAC;KACnE;IAED,IAAI,QAAQ,CAAC,WAAW,KAAK,eAAe,CAAC,WAAW,EAAE;QACxD,OAAO,8BAA8B,CAAC,QAAQ,EAAE,eAAe,CAAC,CAAC;KAClE;IAED,OAAO,EAAE,CAAC;AACZ,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4854, "column": 0}, "map": {"version": 3, "file": "MetricStorageRegistry.js", "sourceRoot": "", "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/node_modules/%40opentelemetry/sdk-node/node_modules/%40opentelemetry/sdk-metrics/src/state/MetricStorageRegistry.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { MetricStorage } from './MetricStorage';\nimport {\n  InstrumentDescriptor,\n  isDescriptorCompatibleWith,\n} from '../InstrumentDescriptor';\nimport * as api from '@opentelemetry/api';\nimport {\n  getConflictResolutionRecipe,\n  getIncompatibilityDetails,\n} from '../view/RegistrationConflicts';\nimport { MetricCollectorHandle } from './MetricCollector';\n\ntype StorageMap = Map<string, MetricStorage[]>;\n\n/**\n * Internal class for storing {@link MetricStorage}\n */\nexport class MetricStorageRegistry {\n  private readonly _sharedRegistry: StorageMap = new Map();\n  private readonly _perCollectorRegistry = new Map<\n    MetricCollectorHandle,\n    StorageMap\n  >();\n\n  static create() {\n    return new MetricStorageRegistry();\n  }\n\n  getStorages(collector: MetricCollectorHandle): MetricStorage[] {\n    let storages: MetricStorage[] = [];\n    for (const metricStorages of this._sharedRegistry.values()) {\n      storages = storages.concat(metricStorages);\n    }\n\n    const perCollectorStorages = this._perCollectorRegistry.get(collector);\n    if (perCollectorStorages != null) {\n      for (const metricStorages of perCollectorStorages.values()) {\n        storages = storages.concat(metricStorages);\n      }\n    }\n\n    return storages;\n  }\n\n  register(storage: MetricStorage) {\n    this._registerStorage(storage, this._sharedRegistry);\n  }\n\n  registerForCollector(\n    collector: MetricCollectorHandle,\n    storage: MetricStorage\n  ) {\n    let storageMap = this._perCollectorRegistry.get(collector);\n    if (storageMap == null) {\n      storageMap = new Map();\n      this._perCollectorRegistry.set(collector, storageMap);\n    }\n    this._registerStorage(storage, storageMap);\n  }\n\n  findOrUpdateCompatibleStorage<T extends MetricStorage>(\n    expectedDescriptor: InstrumentDescriptor\n  ): T | null {\n    const storages = this._sharedRegistry.get(expectedDescriptor.name);\n    if (storages === undefined) {\n      return null;\n    }\n\n    // If the descriptor is compatible, the type of their metric storage\n    // (either SyncMetricStorage or AsyncMetricStorage) must be compatible.\n    return this._findOrUpdateCompatibleStorage<T>(expectedDescriptor, storages);\n  }\n\n  findOrUpdateCompatibleCollectorStorage<T extends MetricStorage>(\n    collector: MetricCollectorHandle,\n    expectedDescriptor: InstrumentDescriptor\n  ): T | null {\n    const storageMap = this._perCollectorRegistry.get(collector);\n    if (storageMap === undefined) {\n      return null;\n    }\n\n    const storages = storageMap.get(expectedDescriptor.name);\n    if (storages === undefined) {\n      return null;\n    }\n\n    // If the descriptor is compatible, the type of their metric storage\n    // (either SyncMetricStorage or AsyncMetricStorage) must be compatible.\n    return this._findOrUpdateCompatibleStorage<T>(expectedDescriptor, storages);\n  }\n\n  private _registerStorage(storage: MetricStorage, storageMap: StorageMap) {\n    const descriptor = storage.getInstrumentDescriptor();\n    const storages = storageMap.get(descriptor.name);\n\n    if (storages === undefined) {\n      storageMap.set(descriptor.name, [storage]);\n      return;\n    }\n\n    storages.push(storage);\n  }\n\n  private _findOrUpdateCompatibleStorage<T extends MetricStorage>(\n    expectedDescriptor: InstrumentDescriptor,\n    existingStorages: MetricStorage[]\n  ): T | null {\n    let compatibleStorage = null;\n\n    for (const existingStorage of existingStorages) {\n      const existingDescriptor = existingStorage.getInstrumentDescriptor();\n\n      if (isDescriptorCompatibleWith(existingDescriptor, expectedDescriptor)) {\n        // Use the longer description if it does not match.\n        if (existingDescriptor.description !== expectedDescriptor.description) {\n          if (\n            expectedDescriptor.description.length >\n            existingDescriptor.description.length\n          ) {\n            existingStorage.updateDescription(expectedDescriptor.description);\n          }\n\n          api.diag.warn(\n            'A view or instrument with the name ',\n            expectedDescriptor.name,\n            ' has already been registered, but has a different description and is incompatible with another registered view.\\n',\n            'Details:\\n',\n            getIncompatibilityDetails(existingDescriptor, expectedDescriptor),\n            'The longer description will be used.\\nTo resolve the conflict:',\n            getConflictResolutionRecipe(existingDescriptor, expectedDescriptor)\n          );\n        }\n        // Storage is fully compatible. There will never be more than one pre-existing fully compatible storage.\n        compatibleStorage = existingStorage as T;\n      } else {\n        // The implementation SHOULD warn about duplicate instrument registration\n        // conflicts after applying View configuration.\n        api.diag.warn(\n          'A view or instrument with the name ',\n          expectedDescriptor.name,\n          ' has already been registered and is incompatible with another registered view.\\n',\n          'Details:\\n',\n          getIncompatibilityDetails(existingDescriptor, expectedDescriptor),\n          'To resolve the conflict:\\n',\n          getConflictResolutionRecipe(existingDescriptor, expectedDescriptor)\n        );\n      }\n    }\n\n    return compatibleStorage;\n  }\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;AAGH,OAAO,EAEL,0BAA0B,GAC3B,MAAM,yBAAyB,CAAC;AACjC,OAAO,KAAK,GAAG,MAAM,oBAAoB,CAAC;AAC1C,OAAO,EACL,2BAA2B,EAC3B,yBAAyB,GAC1B,MAAM,+BAA+B,CAAC;;;;;;;;;;;;;;;;;;AAKvC;;GAEG,CACH,IAAA,wBAAA;IAAA,SAAA;QACmB,IAAA,CAAA,eAAe,GAAe,IAAI,GAAG,EAAE,CAAC;QACxC,IAAA,CAAA,qBAAqB,GAAG,IAAI,GAAG,EAG7C,CAAC;IAkIN,CAAC;IAhIQ,sBAAA,MAAM,GAAb;QACE,OAAO,IAAI,qBAAqB,EAAE,CAAC;IACrC,CAAC;IAED,sBAAA,SAAA,CAAA,WAAW,GAAX,SAAY,SAAgC;;QAC1C,IAAI,QAAQ,GAAoB,EAAE,CAAC;;YACnC,IAA6B,IAAA,KAAA,SAAA,IAAI,CAAC,eAAe,CAAC,MAAM,EAAE,CAAA,EAAA,KAAA,GAAA,IAAA,EAAA,EAAA,CAAA,GAAA,IAAA,EAAA,KAAA,GAAA,IAAA,GAAE;gBAAvD,IAAM,cAAc,GAAA,GAAA,KAAA;gBACvB,QAAQ,GAAG,QAAQ,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC;aAC5C;;;;;;;;;;;;QAED,IAAM,oBAAoB,GAAG,IAAI,CAAC,qBAAqB,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;QACvE,IAAI,oBAAoB,IAAI,IAAI,EAAE;;gBAChC,IAA6B,IAAA,KAAA,SAAA,oBAAoB,CAAC,MAAM,EAAE,CAAA,EAAA,KAAA,GAAA,IAAA,EAAA,EAAA,CAAA,GAAA,IAAA,EAAA,KAAA,GAAA,IAAA,GAAE;oBAAvD,IAAM,cAAc,GAAA,GAAA,KAAA;oBACvB,QAAQ,GAAG,QAAQ,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC;iBAC5C;;;;;;;;;;;;SACF;QAED,OAAO,QAAQ,CAAC;IAClB,CAAC;IAED,sBAAA,SAAA,CAAA,QAAQ,GAAR,SAAS,OAAsB;QAC7B,IAAI,CAAC,gBAAgB,CAAC,OAAO,EAAE,IAAI,CAAC,eAAe,CAAC,CAAC;IACvD,CAAC;IAED,sBAAA,SAAA,CAAA,oBAAoB,GAApB,SACE,SAAgC,EAChC,OAAsB;QAEtB,IAAI,UAAU,GAAG,IAAI,CAAC,qBAAqB,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;QAC3D,IAAI,UAAU,IAAI,IAAI,EAAE;YACtB,UAAU,GAAG,IAAI,GAAG,EAAE,CAAC;YACvB,IAAI,CAAC,qBAAqB,CAAC,GAAG,CAAC,SAAS,EAAE,UAAU,CAAC,CAAC;SACvD;QACD,IAAI,CAAC,gBAAgB,CAAC,OAAO,EAAE,UAAU,CAAC,CAAC;IAC7C,CAAC;IAED,sBAAA,SAAA,CAAA,6BAA6B,GAA7B,SACE,kBAAwC;QAExC,IAAM,QAAQ,GAAG,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAC;QACnE,IAAI,QAAQ,KAAK,SAAS,EAAE;YAC1B,OAAO,IAAI,CAAC;SACb;QAED,oEAAoE;QACpE,uEAAuE;QACvE,OAAO,IAAI,CAAC,8BAA8B,CAAI,kBAAkB,EAAE,QAAQ,CAAC,CAAC;IAC9E,CAAC;IAED,sBAAA,SAAA,CAAA,sCAAsC,GAAtC,SACE,SAAgC,EAChC,kBAAwC;QAExC,IAAM,UAAU,GAAG,IAAI,CAAC,qBAAqB,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;QAC7D,IAAI,UAAU,KAAK,SAAS,EAAE;YAC5B,OAAO,IAAI,CAAC;SACb;QAED,IAAM,QAAQ,GAAG,UAAU,CAAC,GAAG,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAC;QACzD,IAAI,QAAQ,KAAK,SAAS,EAAE;YAC1B,OAAO,IAAI,CAAC;SACb;QAED,oEAAoE;QACpE,uEAAuE;QACvE,OAAO,IAAI,CAAC,8BAA8B,CAAI,kBAAkB,EAAE,QAAQ,CAAC,CAAC;IAC9E,CAAC;IAEO,sBAAA,SAAA,CAAA,gBAAgB,GAAxB,SAAyB,OAAsB,EAAE,UAAsB;QACrE,IAAM,UAAU,GAAG,OAAO,CAAC,uBAAuB,EAAE,CAAC;QACrD,IAAM,QAAQ,GAAG,UAAU,CAAC,GAAG,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;QAEjD,IAAI,QAAQ,KAAK,SAAS,EAAE;YAC1B,UAAU,CAAC,GAAG,CAAC,UAAU,CAAC,IAAI,EAAE;gBAAC,OAAO;aAAC,CAAC,CAAC;YAC3C,OAAO;SACR;QAED,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;IACzB,CAAC;IAEO,sBAAA,SAAA,CAAA,8BAA8B,GAAtC,SACE,kBAAwC,EACxC,gBAAiC;;QAEjC,IAAI,iBAAiB,GAAG,IAAI,CAAC;;YAE7B,IAA8B,IAAA,qBAAA,SAAA,gBAAgB,CAAA,EAAA,uBAAA,mBAAA,IAAA,EAAA,EAAA,CAAA,qBAAA,IAAA,EAAA,uBAAA,mBAAA,IAAA,GAAE;gBAA3C,IAAM,eAAe,GAAA,qBAAA,KAAA;gBACxB,IAAM,kBAAkB,GAAG,eAAe,CAAC,uBAAuB,EAAE,CAAC;gBAErE,uPAAI,6BAAA,AAA0B,EAAC,kBAAkB,EAAE,kBAAkB,CAAC,EAAE;oBACtE,mDAAmD;oBACnD,IAAI,kBAAkB,CAAC,WAAW,KAAK,kBAAkB,CAAC,WAAW,EAAE;wBACrE,IACE,kBAAkB,CAAC,WAAW,CAAC,MAAM,GACrC,kBAAkB,CAAC,WAAW,CAAC,MAAM,EACrC;4BACA,eAAe,CAAC,iBAAiB,CAAC,kBAAkB,CAAC,WAAW,CAAC,CAAC;yBACnE;iMAED,GAAG,CAAC,GAAI,CAAC,IAAI,CACX,qCAAqC,EACrC,kBAAkB,CAAC,IAAI,EACvB,mHAAmH,EACnH,YAAY,8PACZ,4BAAA,AAAyB,EAAC,kBAAkB,EAAE,kBAAkB,CAAC,EACjE,gEAAgE,GAChE,yRAAA,AAA2B,EAAC,kBAAkB,EAAE,kBAAkB,CAAC,CACpE,CAAC;qBACH;oBACD,wGAAwG;oBACxG,iBAAiB,GAAG,eAAoB,CAAC;iBAC1C,MAAM;oBACL,yEAAyE;oBACzE,+CAA+C;6LAC/C,GAAG,CAAC,GAAI,CAAC,IAAI,CACX,qCAAqC,EACrC,kBAAkB,CAAC,IAAI,EACvB,kFAAkF,EAClF,YAAY,GACZ,uRAAA,AAAyB,EAAC,kBAAkB,EAAE,kBAAkB,CAAC,EACjE,4BAA4B,8PAC5B,8BAAA,AAA2B,EAAC,kBAAkB,EAAE,kBAAkB,CAAC,CACpE,CAAC;iBACH;aACF;;;;;;;;;;;;QAED,OAAO,iBAAiB,CAAC;IAC3B,CAAC;IACH,OAAA,qBAAC;AAAD,CAAC,AAvID,IAuIC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5031, "column": 0}, "map": {"version": 3, "file": "MultiWritableMetricStorage.js", "sourceRoot": "", "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/node_modules/%40opentelemetry/sdk-node/node_modules/%40opentelemetry/sdk-metrics/src/state/MultiWritableMetricStorage.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { Context, HrTime, MetricAttributes } from '@opentelemetry/api';\nimport { WritableMetricStorage } from './WritableMetricStorage';\n\n/**\n * Internal interface.\n */\nexport class MultiMetricStorage implements WritableMetricStorage {\n  constructor(private readonly _backingStorages: WritableMetricStorage[]) {}\n\n  record(\n    value: number,\n    attributes: MetricAttributes,\n    context: Context,\n    recordTime: HrTime\n  ) {\n    this._backingStorages.forEach(it => {\n      it.record(value, attributes, context, recordTime);\n    });\n  }\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG,CAKH;;GAEG;;;AACH,IAAA,qBAAA;IACE,SAAA,mBAA6B,gBAAyC;QAAzC,IAAA,CAAA,gBAAgB,GAAhB,gBAAgB,CAAyB;IAAG,CAAC;IAE1E,mBAAA,SAAA,CAAA,MAAM,GAAN,SACE,KAAa,EACb,UAA4B,EAC5B,OAAgB,EAChB,UAAkB;QAElB,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,SAAA,EAAE;YAC9B,EAAE,CAAC,MAAM,CAAC,KAAK,EAAE,UAAU,EAAE,OAAO,EAAE,UAAU,CAAC,CAAC;QACpD,CAAC,CAAC,CAAC;IACL,CAAC;IACH,OAAA,kBAAC;AAAD,CAAC,AAbD,IAaC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5069, "column": 0}, "map": {"version": 3, "file": "ObservableResult.js", "sourceRoot": "", "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/node_modules/%40opentelemetry/sdk-node/node_modules/%40opentelemetry/sdk-metrics/src/ObservableResult.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  diag,\n  ObservableResult,\n  MetricAttributes,\n  ValueType,\n  BatchObservableResult,\n  Observable,\n} from '@opentelemetry/api';\nimport { AttributeHashMap } from './state/HashMap';\nimport { isObservableInstrument, ObservableInstrument } from './Instruments';\n\n/**\n * The class implements {@link ObservableResult} interface.\n */\nexport class ObservableResultImpl implements ObservableResult {\n  /**\n   * @internal\n   */\n  _buffer = new AttributeHashMap<number>();\n\n  constructor(\n    private _instrumentName: string,\n    private _valueType: ValueType\n  ) {}\n\n  /**\n   * Observe a measurement of the value associated with the given attributes.\n   */\n  observe(value: number, attributes: MetricAttributes = {}): void {\n    if (typeof value !== 'number') {\n      diag.warn(\n        `non-number value provided to metric ${this._instrumentName}: ${value}`\n      );\n      return;\n    }\n    if (this._valueType === ValueType.INT && !Number.isInteger(value)) {\n      diag.warn(\n        `INT value type cannot accept a floating-point value for ${this._instrumentName}, ignoring the fractional digits.`\n      );\n      value = Math.trunc(value);\n      // ignore non-finite values.\n      if (!Number.isInteger(value)) {\n        return;\n      }\n    }\n    this._buffer.set(attributes, value);\n  }\n}\n\n/**\n * The class implements {@link BatchObservableCallback} interface.\n */\nexport class BatchObservableResultImpl implements BatchObservableResult {\n  /**\n   * @internal\n   */\n  _buffer: Map<ObservableInstrument, AttributeHashMap<number>> = new Map();\n\n  /**\n   * Observe a measurement of the value associated with the given attributes.\n   */\n  observe(\n    metric: Observable,\n    value: number,\n    attributes: MetricAttributes = {}\n  ): void {\n    if (!isObservableInstrument(metric)) {\n      return;\n    }\n    let map = this._buffer.get(metric);\n    if (map == null) {\n      map = new AttributeHashMap();\n      this._buffer.set(metric, map);\n    }\n    if (typeof value !== 'number') {\n      diag.warn(\n        `non-number value provided to metric ${metric._descriptor.name}: ${value}`\n      );\n      return;\n    }\n    if (\n      metric._descriptor.valueType === ValueType.INT &&\n      !Number.isInteger(value)\n    ) {\n      diag.warn(\n        `INT value type cannot accept a floating-point value for ${metric._descriptor.name}, ignoring the fractional digits.`\n      );\n      value = Math.trunc(value);\n      // ignore non-finite values.\n      if (!Number.isInteger(value)) {\n        return;\n      }\n    }\n    map.set(attributes, value);\n  }\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;;AAEH,OAAO,EACL,IAAI,EAGJ,SAAS,GAGV,MAAM,oBAAoB,CAAC;;AAC5B,OAAO,EAAE,gBAAgB,EAAE,MAAM,iBAAiB,CAAC;AACnD,OAAO,EAAE,sBAAsB,EAAwB,MAAM,eAAe,CAAC;;;;AAE7E;;GAEG,CACH,IAAA,uBAAA;IAME,SAAA,qBACU,eAAuB,EACvB,UAAqB;QADrB,IAAA,CAAA,eAAe,GAAf,eAAe,CAAQ;QACvB,IAAA,CAAA,UAAU,GAAV,UAAU,CAAW;QAP/B;;WAEG,CACH,IAAA,CAAA,OAAO,GAAG,+OAAI,mBAAgB,EAAU,CAAC;IAKtC,CAAC;IAEJ;;OAEG,CACH,qBAAA,SAAA,CAAA,OAAO,GAAP,SAAQ,KAAa,EAAE,UAAiC;QAAjC,IAAA,eAAA,KAAA,GAAA;YAAA,aAAA,CAAA,CAAiC;QAAA;QACtD,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE;qLAC7B,OAAI,CAAC,IAAI,CACP,yCAAuC,IAAI,CAAC,eAAe,GAAA,OAAK,KAAO,CACxE,CAAC;YACF,OAAO;SACR;QACD,IAAI,IAAI,CAAC,UAAU,oLAAK,YAAS,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,KAAK,CAAC,EAAE;qLACjE,OAAI,CAAC,IAAI,CACP,6DAA2D,IAAI,CAAC,eAAe,GAAA,mCAAmC,CACnH,CAAC;YACF,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;YAC1B,4BAA4B;YAC5B,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,KAAK,CAAC,EAAE;gBAC5B,OAAO;aACR;SACF;QACD,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,UAAU,EAAE,KAAK,CAAC,CAAC;IACtC,CAAC;IACH,OAAA,oBAAC;AAAD,CAAC,AAjCD,IAiCC;;AAED;;GAEG,CACH,IAAA,4BAAA;IAAA,SAAA;QACE;;WAEG,CACH,IAAA,CAAA,OAAO,GAAwD,IAAI,GAAG,EAAE,CAAC;IAuC3E,CAAC;IArCC;;OAEG,CACH,0BAAA,SAAA,CAAA,OAAO,GAAP,SACE,MAAkB,EAClB,KAAa,EACb,UAAiC;QAAjC,IAAA,eAAA,KAAA,GAAA;YAAA,aAAA,CAAA,CAAiC;QAAA;QAEjC,IAAI,2OAAC,yBAAA,AAAsB,EAAC,MAAM,CAAC,EAAE;YACnC,OAAO;SACR;QACD,IAAI,GAAG,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;QACnC,IAAI,GAAG,IAAI,IAAI,EAAE;YACf,GAAG,GAAG,IAAI,8PAAgB,EAAE,CAAC;YAC7B,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC;SAC/B;QACD,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE;qLAC7B,OAAI,CAAC,IAAI,CACP,yCAAuC,MAAM,CAAC,WAAW,CAAC,IAAI,GAAA,OAAK,KAAO,CAC3E,CAAC;YACF,OAAO;SACR;QACD,IACE,MAAM,CAAC,WAAW,CAAC,SAAS,oLAAK,YAAS,CAAC,GAAG,IAC9C,CAAC,MAAM,CAAC,SAAS,CAAC,KAAK,CAAC,EACxB;qLACA,OAAI,CAAC,IAAI,CACP,6DAA2D,MAAM,CAAC,WAAW,CAAC,IAAI,GAAA,mCAAmC,CACtH,CAAC;YACF,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;YAC1B,4BAA4B;YAC5B,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,KAAK,CAAC,EAAE;gBAC5B,OAAO;aACR;SACF;QACD,GAAG,CAAC,GAAG,CAAC,UAAU,EAAE,KAAK,CAAC,CAAC;IAC7B,CAAC;IACH,OAAA,yBAAC;AAAD,CAAC,AA3CD,IA2CC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5173, "column": 0}, "map": {"version": 3, "file": "ObservableRegistry.js", "sourceRoot": "", "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/node_modules/%40opentelemetry/sdk-node/node_modules/%40opentelemetry/sdk-metrics/src/state/ObservableRegistry.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  diag,\n  HrTime,\n  BatchObservableCallback,\n  Observable,\n  ObservableCallback,\n} from '@opentelemetry/api';\nimport { isObservableInstrument, ObservableInstrument } from '../Instruments';\nimport {\n  BatchObservableResultImpl,\n  ObservableResultImpl,\n} from '../ObservableResult';\nimport {\n  callWithTimeout,\n  PromiseAllSettled,\n  isPromiseAllSettledRejectionResult,\n  setEquals,\n} from '../utils';\n\n/**\n * Records for single instrument observable callback.\n */\ninterface ObservableCallbackRecord {\n  callback: ObservableCallback;\n  instrument: ObservableInstrument;\n}\n\n/**\n * Records for multiple instruments observable callback.\n */\ninterface BatchObservableCallbackRecord {\n  callback: BatchObservableCallback;\n  instruments: Set<ObservableInstrument>;\n}\n\n/**\n * An internal interface for managing ObservableCallbacks.\n *\n * Every registered callback associated with a set of instruments are be evaluated\n * exactly once during collection prior to reading data for that instrument.\n */\nexport class ObservableRegistry {\n  private _callbacks: ObservableCallbackRecord[] = [];\n  private _batchCallbacks: BatchObservableCallbackRecord[] = [];\n\n  addCallback(callback: ObservableCallback, instrument: ObservableInstrument) {\n    const idx = this._findCallback(callback, instrument);\n    if (idx >= 0) {\n      return;\n    }\n    this._callbacks.push({ callback, instrument });\n  }\n\n  removeCallback(\n    callback: ObservableCallback,\n    instrument: ObservableInstrument\n  ) {\n    const idx = this._findCallback(callback, instrument);\n    if (idx < 0) {\n      return;\n    }\n    this._callbacks.splice(idx, 1);\n  }\n\n  addBatchCallback(\n    callback: BatchObservableCallback,\n    instruments: Observable[]\n  ) {\n    // Create a set of unique instruments.\n    const observableInstruments = new Set(\n      instruments.filter(isObservableInstrument)\n    );\n    if (observableInstruments.size === 0) {\n      diag.error(\n        'BatchObservableCallback is not associated with valid instruments',\n        instruments\n      );\n      return;\n    }\n    const idx = this._findBatchCallback(callback, observableInstruments);\n    if (idx >= 0) {\n      return;\n    }\n    this._batchCallbacks.push({ callback, instruments: observableInstruments });\n  }\n\n  removeBatchCallback(\n    callback: BatchObservableCallback,\n    instruments: Observable[]\n  ) {\n    // Create a set of unique instruments.\n    const observableInstruments = new Set(\n      instruments.filter(isObservableInstrument)\n    );\n    const idx = this._findBatchCallback(callback, observableInstruments);\n    if (idx < 0) {\n      return;\n    }\n    this._batchCallbacks.splice(idx, 1);\n  }\n\n  /**\n   * @returns a promise of rejected reasons for invoking callbacks.\n   */\n  async observe(\n    collectionTime: HrTime,\n    timeoutMillis?: number\n  ): Promise<unknown[]> {\n    const callbackFutures = this._observeCallbacks(\n      collectionTime,\n      timeoutMillis\n    );\n    const batchCallbackFutures = this._observeBatchCallbacks(\n      collectionTime,\n      timeoutMillis\n    );\n\n    const results = await PromiseAllSettled([\n      ...callbackFutures,\n      ...batchCallbackFutures,\n    ]);\n\n    const rejections = results\n      .filter(isPromiseAllSettledRejectionResult)\n      .map(it => it.reason);\n    return rejections;\n  }\n\n  private _observeCallbacks(observationTime: HrTime, timeoutMillis?: number) {\n    return this._callbacks.map(async ({ callback, instrument }) => {\n      const observableResult = new ObservableResultImpl(\n        instrument._descriptor.name,\n        instrument._descriptor.valueType\n      );\n      let callPromise: Promise<void> = Promise.resolve(\n        callback(observableResult)\n      );\n      if (timeoutMillis != null) {\n        callPromise = callWithTimeout(callPromise, timeoutMillis);\n      }\n      await callPromise;\n      instrument._metricStorages.forEach(metricStorage => {\n        metricStorage.record(observableResult._buffer, observationTime);\n      });\n    });\n  }\n\n  private _observeBatchCallbacks(\n    observationTime: HrTime,\n    timeoutMillis?: number\n  ) {\n    return this._batchCallbacks.map(async ({ callback, instruments }) => {\n      const observableResult = new BatchObservableResultImpl();\n      let callPromise: Promise<void> = Promise.resolve(\n        callback(observableResult)\n      );\n      if (timeoutMillis != null) {\n        callPromise = callWithTimeout(callPromise, timeoutMillis);\n      }\n      await callPromise;\n      instruments.forEach(instrument => {\n        const buffer = observableResult._buffer.get(instrument);\n        if (buffer == null) {\n          return;\n        }\n        instrument._metricStorages.forEach(metricStorage => {\n          metricStorage.record(buffer, observationTime);\n        });\n      });\n    });\n  }\n\n  private _findCallback(\n    callback: ObservableCallback,\n    instrument: ObservableInstrument\n  ) {\n    return this._callbacks.findIndex(record => {\n      return record.callback === callback && record.instrument === instrument;\n    });\n  }\n\n  private _findBatchCallback(\n    callback: BatchObservableCallback,\n    instruments: Set<ObservableInstrument>\n  ) {\n    return this._batchCallbacks.findIndex(record => {\n      return (\n        record.callback === callback &&\n        setEquals(record.instruments, instruments)\n      );\n    });\n  }\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;AAEH,OAAO,EACL,IAAI,GAKL,MAAM,oBAAoB,CAAC;AAC5B,OAAO,EAAE,sBAAsB,EAAwB,MAAM,gBAAgB,CAAC;AAC9E,OAAO,EACL,yBAAyB,EACzB,oBAAoB,GACrB,MAAM,qBAAqB,CAAC;AAC7B,OAAO,EACL,eAAe,EACf,iBAAiB,EACjB,kCAAkC,EAClC,SAAS,GACV,MAAM,UAAU,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAkBlB;;;;;GAKG,CACH,IAAA,qBAAA;IAAA,SAAA;QACU,IAAA,CAAA,UAAU,GAA+B,EAAE,CAAC;QAC5C,IAAA,CAAA,eAAe,GAAoC,EAAE,CAAC;IAqJhE,CAAC;IAnJC,mBAAA,SAAA,CAAA,WAAW,GAAX,SAAY,QAA4B,EAAE,UAAgC;QACxE,IAAM,GAAG,GAAG,IAAI,CAAC,aAAa,CAAC,QAAQ,EAAE,UAAU,CAAC,CAAC;QACrD,IAAI,GAAG,IAAI,CAAC,EAAE;YACZ,OAAO;SACR;QACD,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC;YAAE,QAAQ,EAAA,QAAA;YAAE,UAAU,EAAA,UAAA;QAAA,CAAE,CAAC,CAAC;IACjD,CAAC;IAED,mBAAA,SAAA,CAAA,cAAc,GAAd,SACE,QAA4B,EAC5B,UAAgC;QAEhC,IAAM,GAAG,GAAG,IAAI,CAAC,aAAa,CAAC,QAAQ,EAAE,UAAU,CAAC,CAAC;QACrD,IAAI,GAAG,GAAG,CAAC,EAAE;YACX,OAAO;SACR;QACD,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;IACjC,CAAC;IAED,mBAAA,SAAA,CAAA,gBAAgB,GAAhB,SACE,QAAiC,EACjC,WAAyB;QAEzB,sCAAsC;QACtC,IAAM,qBAAqB,GAAG,IAAI,GAAG,CACnC,WAAW,CAAC,MAAM,sOAAC,0BAAsB,CAAC,CAC3C,CAAC;QACF,IAAI,qBAAqB,CAAC,IAAI,KAAK,CAAC,EAAE;qLACpC,OAAI,CAAC,KAAK,CACR,kEAAkE,EAClE,WAAW,CACZ,CAAC;YACF,OAAO;SACR;QACD,IAAM,GAAG,GAAG,IAAI,CAAC,kBAAkB,CAAC,QAAQ,EAAE,qBAAqB,CAAC,CAAC;QACrE,IAAI,GAAG,IAAI,CAAC,EAAE;YACZ,OAAO;SACR;QACD,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC;YAAE,QAAQ,EAAA,QAAA;YAAE,WAAW,EAAE,qBAAqB;QAAA,CAAE,CAAC,CAAC;IAC9E,CAAC;IAED,mBAAA,SAAA,CAAA,mBAAmB,GAAnB,SACE,QAAiC,EACjC,WAAyB;QAEzB,sCAAsC;QACtC,IAAM,qBAAqB,GAAG,IAAI,GAAG,CACnC,WAAW,CAAC,MAAM,uOAAC,yBAAsB,CAAC,CAC3C,CAAC;QACF,IAAM,GAAG,GAAG,IAAI,CAAC,kBAAkB,CAAC,QAAQ,EAAE,qBAAqB,CAAC,CAAC;QACrE,IAAI,GAAG,GAAG,CAAC,EAAE;YACX,OAAO;SACR;QACD,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;IACtC,CAAC;IAED;;OAEG,CACG,mBAAA,SAAA,CAAA,OAAO,GAAb,SACE,cAAsB,EACtB,aAAsB;;;;;;wBAEhB,eAAe,GAAG,IAAI,CAAC,iBAAiB,CAC5C,cAAc,EACd,aAAa,CACd,CAAC;wBACI,oBAAoB,GAAG,IAAI,CAAC,sBAAsB,CACtD,cAAc,EACd,aAAa,CACd,CAAC;wBAEc,OAAA;4BAAA,EAAA,OAAA;gQAAM,oBAAA,AAAiB,EAAA,cAAA,cAAA,EAAA,EAAA,OAClC,eAAe,GAAA,QAAA,OACf,oBAAoB,GAAA,OACvB;yBAAA,CAAA;;wBAHI,OAAO,GAAG,GAAA,IAAA,EAGd;wBAEI,UAAU,GAAG,OAAO,CACvB,MAAM,iOAAC,qCAAkC,CAAC,CAC1C,GAAG,CAAC,SAAA,EAAE;4BAAI,OAAA,EAAE,CAAC,MAAM;wBAAT,CAAS,CAAC,CAAC;wBACxB,OAAA;4BAAA,EAAA,QAAA;4BAAO,UAAU;yBAAA,CAAC;;;;KACnB;IAEO,mBAAA,SAAA,CAAA,iBAAiB,GAAzB,SAA0B,eAAuB,EAAE,aAAsB;QAAzE,IAAA,QAAA,IAAA,CAiBC;QAhBC,OAAO,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,SAAO,EAAwB;gBAAtB,QAAQ,GAAA,GAAA,QAAA,EAAE,UAAU,GAAA,GAAA,UAAA;;;;;;4BAChD,gBAAgB,GAAG,+OAAI,uBAAoB,CAC/C,UAAU,CAAC,WAAW,CAAC,IAAI,EAC3B,UAAU,CAAC,WAAW,CAAC,SAAS,CACjC,CAAC;4BACE,WAAW,GAAkB,OAAO,CAAC,OAAO,CAC9C,QAAQ,CAAC,gBAAgB,CAAC,CAC3B,CAAC;4BACF,IAAI,aAAa,IAAI,IAAI,EAAE;gCACzB,WAAW,uOAAG,kBAAA,AAAe,EAAC,WAAW,EAAE,aAAa,CAAC,CAAC;6BAC3D;4BACD,OAAA;gCAAA,EAAA,OAAA;gCAAM,WAAW;6BAAA,CAAA;;4BAAjB,GAAA,IAAA,EAAiB,CAAC;4BAClB,UAAU,CAAC,eAAe,CAAC,OAAO,CAAC,SAAA,aAAa;gCAC9C,aAAa,CAAC,MAAM,CAAC,gBAAgB,CAAC,OAAO,EAAE,eAAe,CAAC,CAAC;4BAClE,CAAC,CAAC,CAAC;;;;;;;SACJ,CAAC,CAAC;IACL,CAAC;IAEO,mBAAA,SAAA,CAAA,sBAAsB,GAA9B,SACE,eAAuB,EACvB,aAAsB;QAFxB,IAAA,QAAA,IAAA,CAuBC;QAnBC,OAAO,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,SAAO,EAAyB;gBAAvB,QAAQ,GAAA,GAAA,QAAA,EAAE,WAAW,GAAA,GAAA,WAAA;;;;;;4BACtD,gBAAgB,GAAG,+OAAI,4BAAyB,EAAE,CAAC;4BACrD,WAAW,GAAkB,OAAO,CAAC,OAAO,CAC9C,QAAQ,CAAC,gBAAgB,CAAC,CAC3B,CAAC;4BACF,IAAI,aAAa,IAAI,IAAI,EAAE;gCACzB,WAAW,uOAAG,kBAAA,AAAe,EAAC,WAAW,EAAE,aAAa,CAAC,CAAC;6BAC3D;4BACD,OAAA;gCAAA,EAAA,OAAA;gCAAM,WAAW;6BAAA,CAAA;;4BAAjB,GAAA,IAAA,EAAiB,CAAC;4BAClB,WAAW,CAAC,OAAO,CAAC,SAAA,UAAU;gCAC5B,IAAM,MAAM,GAAG,gBAAgB,CAAC,OAAO,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;gCACxD,IAAI,MAAM,IAAI,IAAI,EAAE;oCAClB,OAAO;iCACR;gCACD,UAAU,CAAC,eAAe,CAAC,OAAO,CAAC,SAAA,aAAa;oCAC9C,aAAa,CAAC,MAAM,CAAC,MAAM,EAAE,eAAe,CAAC,CAAC;gCAChD,CAAC,CAAC,CAAC;4BACL,CAAC,CAAC,CAAC;;;;;;;SACJ,CAAC,CAAC;IACL,CAAC;IAEO,mBAAA,SAAA,CAAA,aAAa,GAArB,SACE,QAA4B,EAC5B,UAAgC;QAEhC,OAAO,IAAI,CAAC,UAAU,CAAC,SAAS,CAAC,SAAA,MAAM;YACrC,OAAO,MAAM,CAAC,QAAQ,KAAK,QAAQ,IAAI,MAAM,CAAC,UAAU,KAAK,UAAU,CAAC;QAC1E,CAAC,CAAC,CAAC;IACL,CAAC;IAEO,mBAAA,SAAA,CAAA,kBAAkB,GAA1B,SACE,QAAiC,EACjC,WAAsC;QAEtC,OAAO,IAAI,CAAC,eAAe,CAAC,SAAS,CAAC,SAAA,MAAM;YAC1C,OAAO,AACL,MAAM,CAAC,QAAQ,KAAK,QAAQ,wOAC5B,YAAA,AAAS,EAAC,MAAM,CAAC,WAAW,EAAE,WAAW,CAAC,CAC3C,CAAC;QACJ,CAAC,CAAC,CAAC;IACL,CAAC;IACH,OAAA,kBAAC;AAAD,CAAC,AAvJD,IAuJC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5516, "column": 0}, "map": {"version": 3, "file": "SyncMetricStorage.js", "sourceRoot": "", "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/node_modules/%40opentelemetry/sdk-node/node_modules/%40opentelemetry/sdk-metrics/src/state/SyncMetricStorage.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { Context, HrTime, MetricAttributes } from '@opentelemetry/api';\nimport { WritableMetricStorage } from './WritableMetricStorage';\nimport { Accumulation, Aggregator } from '../aggregator/types';\nimport { InstrumentDescriptor } from '../InstrumentDescriptor';\nimport { AttributesProcessor } from '../view/AttributesProcessor';\nimport { MetricStorage } from './MetricStorage';\nimport { MetricData } from '../export/MetricData';\nimport { DeltaMetricProcessor } from './DeltaMetricProcessor';\nimport { TemporalMetricProcessor } from './TemporalMetricProcessor';\nimport { Maybe } from '../utils';\nimport { MetricCollectorHandle } from './MetricCollector';\n\n/**\n * Internal interface.\n *\n * Stores and aggregates {@link MetricData} for synchronous instruments.\n */\nexport class SyncMetricStorage<T extends Maybe<Accumulation>>\n  extends MetricStorage\n  implements WritableMetricStorage\n{\n  private _deltaMetricStorage: DeltaMetricProcessor<T>;\n  private _temporalMetricStorage: TemporalMetricProcessor<T>;\n\n  constructor(\n    instrumentDescriptor: InstrumentDescriptor,\n    aggregator: Aggregator<T>,\n    private _attributesProcessor: AttributesProcessor,\n    collectorHandles: MetricCollectorHandle[]\n  ) {\n    super(instrumentDescriptor);\n    this._deltaMetricStorage = new DeltaMetricProcessor(aggregator);\n    this._temporalMetricStorage = new TemporalMetricProcessor(\n      aggregator,\n      collectorHandles\n    );\n  }\n\n  record(\n    value: number,\n    attributes: MetricAttributes,\n    context: Context,\n    recordTime: HrTime\n  ) {\n    attributes = this._attributesProcessor.process(attributes, context);\n    this._deltaMetricStorage.record(value, attributes, context, recordTime);\n  }\n\n  /**\n   * Collects the metrics from this storage.\n   *\n   * Note: This is a stateful operation and may reset any interval-related\n   * state for the MetricCollector.\n   */\n  collect(\n    collector: MetricCollectorHandle,\n    collectionTime: HrTime\n  ): Maybe<MetricData> {\n    const accumulations = this._deltaMetricStorage.collect();\n\n    return this._temporalMetricStorage.buildMetrics(\n      collector,\n      this._instrumentDescriptor,\n      accumulations,\n      collectionTime\n    );\n  }\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;AAOH,OAAO,EAAE,aAAa,EAAE,MAAM,iBAAiB,CAAC;AAEhD,OAAO,EAAE,oBAAoB,EAAE,MAAM,wBAAwB,CAAC;AAC9D,OAAO,EAAE,uBAAuB,EAAE,MAAM,2BAA2B,CAAC;;;;;;;;;;;;;;;;;;;;;;;;AAIpE;;;;GAIG,CACH,IAAA,oBAAA,SAAA,MAAA;IACU,UAAA,mBAAA,QAAa;IAMrB,SAAA,kBACE,oBAA0C,EAC1C,UAAyB,EACjB,oBAAyC,EACjD,gBAAyC;QAJ3C,IAAA,QAME,OAAA,IAAA,CAAA,IAAA,EAAM,oBAAoB,CAAC,IAAA,IAAA,CAM5B;QATS,MAAA,oBAAoB,GAApB,oBAAoB,CAAqB;QAIjD,KAAI,CAAC,mBAAmB,GAAG,4PAAI,uBAAoB,CAAC,UAAU,CAAC,CAAC;QAChE,KAAI,CAAC,sBAAsB,GAAG,+PAAI,0BAAuB,CACvD,UAAU,EACV,gBAAgB,CACjB,CAAC;;IACJ,CAAC;IAED,kBAAA,SAAA,CAAA,MAAM,GAAN,SACE,KAAa,EACb,UAA4B,EAC5B,OAAgB,EAChB,UAAkB;QAElB,UAAU,GAAG,IAAI,CAAC,oBAAoB,CAAC,OAAO,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC;QACpE,IAAI,CAAC,mBAAmB,CAAC,MAAM,CAAC,KAAK,EAAE,UAAU,EAAE,OAAO,EAAE,UAAU,CAAC,CAAC;IAC1E,CAAC;IAED;;;;;OAKG,CACH,kBAAA,SAAA,CAAA,OAAO,GAAP,SACE,SAAgC,EAChC,cAAsB;QAEtB,IAAM,aAAa,GAAG,IAAI,CAAC,mBAAmB,CAAC,OAAO,EAAE,CAAC;QAEzD,OAAO,IAAI,CAAC,sBAAsB,CAAC,YAAY,CAC7C,SAAS,EACT,IAAI,CAAC,qBAAqB,EAC1B,aAAa,EACb,cAAc,CACf,CAAC;IACJ,CAAC;IACH,OAAA,iBAAC;AAAD,CAAC,AAlDD,kPACU,gBAAa,GAiDtB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5595, "column": 0}, "map": {"version": 3, "file": "AttributesProcessor.js", "sourceRoot": "", "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/node_modules/%40opentelemetry/sdk-node/node_modules/%40opentelemetry/sdk-metrics/src/view/AttributesProcessor.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { Context, MetricAttributes } from '@opentelemetry/api';\n\n/**\n * The {@link AttributesProcessor} is responsible for customizing which\n * attribute(s) are to be reported as metrics dimension(s) and adding\n * additional dimension(s) from the {@link Context}.\n */\nexport abstract class AttributesProcessor {\n  /**\n   * Process the metric instrument attributes.\n   *\n   * @param incoming The metric instrument attributes.\n   * @param context The active context when the instrument is synchronous.\n   * `undefined` otherwise.\n   */\n  abstract process(\n    incoming: MetricAttributes,\n    context?: Context\n  ): MetricAttributes;\n\n  static Noop() {\n    return NOOP;\n  }\n}\n\nexport class NoopAttributesProcessor extends AttributesProcessor {\n  process(incoming: MetricAttributes, _context?: Context) {\n    return incoming;\n  }\n}\n\n/**\n * {@link AttributesProcessor} that filters by allowed attribute names and drops any names that are not in the\n * allow list.\n */\nexport class FilteringAttributesProcessor extends AttributesProcessor {\n  constructor(private _allowedAttributeNames: string[]) {\n    super();\n  }\n\n  process(incoming: MetricAttributes, _context: Context): MetricAttributes {\n    const filteredAttributes: MetricAttributes = {};\n    Object.keys(incoming)\n      .filter(attributeName =>\n        this._allowedAttributeNames.includes(attributeName)\n      )\n      .forEach(\n        attributeName =>\n          (filteredAttributes[attributeName] = incoming[attributeName])\n      );\n    return filteredAttributes;\n  }\n}\n\nconst NOOP = new NoopAttributesProcessor();\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;;;;;;;;;;;;;;;;;;;;;;;AAIH;;;;GAIG,CACH,IAAA,sBAAA;IAAA,SAAA,uBAgBA,CAAC;IAHQ,oBAAA,IAAI,GAAX;QACE,OAAO,IAAI,CAAC;IACd,CAAC;IACH,OAAA,mBAAC;AAAD,CAAC,AAhBD,IAgBC;;AAED,IAAA,0BAAA,SAAA,MAAA;IAA6C,UAAA,yBAAA,QAAmB;IAAhE,SAAA;;IAIA,CAAC;IAHC,wBAAA,SAAA,CAAA,OAAO,GAAP,SAAQ,QAA0B,EAAE,QAAkB;QACpD,OAAO,QAAQ,CAAC;IAClB,CAAC;IACH,OAAA,uBAAC;AAAD,CAAC,AAJD,CAA6C,mBAAmB,GAI/D;;AAED;;;GAGG,CACH,IAAA,+BAAA,SAAA,MAAA;IAAkD,UAAA,8BAAA,QAAmB;IACnE,SAAA,6BAAoB,sBAAgC;QAApD,IAAA,QACE,OAAA,IAAA,CAAA,IAAA,CAAO,IAAA,IAAA,CACR;QAFmB,MAAA,sBAAsB,GAAtB,sBAAsB,CAAU;;IAEpD,CAAC;IAED,6BAAA,SAAA,CAAA,OAAO,GAAP,SAAQ,QAA0B,EAAE,QAAiB;QAArD,IAAA,QAAA,IAAA,CAWC;QAVC,IAAM,kBAAkB,GAAqB,CAAA,CAAE,CAAC;QAChD,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,CAClB,MAAM,CAAC,SAAA,aAAa;YACnB,OAAA,KAAI,CAAC,sBAAsB,CAAC,QAAQ,CAAC,aAAa,CAAC;QAAnD,CAAmD,CACpD,CACA,OAAO,CACN,SAAA,aAAa;YACX,OAAA,AAAC,kBAAkB,CAAC,aAAa,CAAC,GAAG,QAAQ,CAAC,aAAa,CAAC,CAAC;QAA7D,CAA6D,CAChE,CAAC;QACJ,OAAO,kBAAkB,CAAC;IAC5B,CAAC;IACH,OAAA,4BAAC;AAAD,CAAC,AAjBD,CAAkD,mBAAmB,GAiBpE;;AAED,IAAM,IAAI,GAAG,IAAI,uBAAuB,EAAE,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5687, "column": 0}, "map": {"version": 3, "file": "MeterSharedState.js", "sourceRoot": "", "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/node_modules/%40opentelemetry/sdk-node/node_modules/%40opentelemetry/sdk-metrics/src/state/MeterSharedState.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { HrTime } from '@opentelemetry/api';\nimport { InstrumentationScope } from '@opentelemetry/core';\nimport { MetricCollectOptions } from '../export/MetricProducer';\nimport { ScopeMetrics } from '../export/MetricData';\nimport {\n  createInstrumentDescriptorWithView,\n  InstrumentDescriptor,\n} from '../InstrumentDescriptor';\nimport { Meter } from '../Meter';\nimport { isNotNullish, Maybe } from '../utils';\nimport { AsyncMetricStorage } from './AsyncMetricStorage';\nimport { MeterProviderSharedState } from './MeterProviderSharedState';\nimport { MetricCollectorHandle } from './MetricCollector';\nimport { MetricStorageRegistry } from './MetricStorageRegistry';\nimport { MultiMetricStorage } from './MultiWritableMetricStorage';\nimport { ObservableRegistry } from './ObservableRegistry';\nimport { SyncMetricStorage } from './SyncMetricStorage';\nimport { Accumulation, Aggregator } from '../aggregator/types';\nimport { AttributesProcessor } from '../view/AttributesProcessor';\nimport { MetricStorage } from './MetricStorage';\n\n/**\n * An internal record for shared meter provider states.\n */\nexport class MeterSharedState {\n  metricStorageRegistry = new MetricStorageRegistry();\n  observableRegistry = new ObservableRegistry();\n  meter: Meter;\n\n  constructor(\n    private _meterProviderSharedState: MeterProviderSharedState,\n    private _instrumentationScope: InstrumentationScope\n  ) {\n    this.meter = new Meter(this);\n  }\n\n  registerMetricStorage(descriptor: InstrumentDescriptor) {\n    const storages = this._registerMetricStorage(descriptor, SyncMetricStorage);\n\n    if (storages.length === 1) {\n      return storages[0];\n    }\n    return new MultiMetricStorage(storages);\n  }\n\n  registerAsyncMetricStorage(descriptor: InstrumentDescriptor) {\n    const storages = this._registerMetricStorage(\n      descriptor,\n      AsyncMetricStorage\n    );\n\n    return storages;\n  }\n\n  /**\n   * @param collector opaque handle of {@link MetricCollector} which initiated the collection.\n   * @param collectionTime the HrTime at which the collection was initiated.\n   * @param options options for collection.\n   * @returns the list of metric data collected.\n   */\n  async collect(\n    collector: MetricCollectorHandle,\n    collectionTime: HrTime,\n    options?: MetricCollectOptions\n  ): Promise<ScopeMetricsResult | null> {\n    /**\n     * 1. Call all observable callbacks first.\n     * 2. Collect metric result for the collector.\n     */\n    const errors = await this.observableRegistry.observe(\n      collectionTime,\n      options?.timeoutMillis\n    );\n    const storages = this.metricStorageRegistry.getStorages(collector);\n\n    // prevent more allocations if there are no storages.\n    if (storages.length === 0) {\n      return null;\n    }\n\n    const metricDataList = storages\n      .map(metricStorage => {\n        return metricStorage.collect(collector, collectionTime);\n      })\n      .filter(isNotNullish);\n\n    // skip this scope if no data was collected (storage created, but no data observed)\n    if (metricDataList.length === 0) {\n      return { errors };\n    }\n\n    return {\n      scopeMetrics: {\n        scope: this._instrumentationScope,\n        metrics: metricDataList,\n      },\n      errors,\n    };\n  }\n\n  private _registerMetricStorage<\n    MetricStorageType extends MetricStorageConstructor,\n    R extends InstanceType<MetricStorageType>,\n  >(\n    descriptor: InstrumentDescriptor,\n    MetricStorageType: MetricStorageType\n  ): R[] {\n    const views = this._meterProviderSharedState.viewRegistry.findViews(\n      descriptor,\n      this._instrumentationScope\n    );\n    let storages = views.map(view => {\n      const viewDescriptor = createInstrumentDescriptorWithView(\n        view,\n        descriptor\n      );\n      const compatibleStorage =\n        this.metricStorageRegistry.findOrUpdateCompatibleStorage<R>(\n          viewDescriptor\n        );\n      if (compatibleStorage != null) {\n        return compatibleStorage;\n      }\n      const aggregator = view.aggregation.createAggregator(viewDescriptor);\n      const viewStorage = new MetricStorageType(\n        viewDescriptor,\n        aggregator,\n        view.attributesProcessor,\n        this._meterProviderSharedState.metricCollectors\n      ) as R;\n      this.metricStorageRegistry.register(viewStorage);\n      return viewStorage;\n    });\n\n    // Fallback to the per-collector aggregations if no view is configured for the instrument.\n    if (storages.length === 0) {\n      const perCollectorAggregations =\n        this._meterProviderSharedState.selectAggregations(descriptor.type);\n      const collectorStorages = perCollectorAggregations.map(\n        ([collector, aggregation]) => {\n          const compatibleStorage =\n            this.metricStorageRegistry.findOrUpdateCompatibleCollectorStorage<R>(\n              collector,\n              descriptor\n            );\n          if (compatibleStorage != null) {\n            return compatibleStorage;\n          }\n          const aggregator = aggregation.createAggregator(descriptor);\n          const storage = new MetricStorageType(\n            descriptor,\n            aggregator,\n            AttributesProcessor.Noop(),\n            [collector]\n          ) as R;\n          this.metricStorageRegistry.registerForCollector(collector, storage);\n          return storage;\n        }\n      );\n      storages = storages.concat(collectorStorages);\n    }\n\n    return storages;\n  }\n}\n\ninterface ScopeMetricsResult {\n  scopeMetrics?: ScopeMetrics;\n  errors: unknown[];\n}\n\ninterface MetricStorageConstructor {\n  new (\n    instrumentDescriptor: InstrumentDescriptor,\n    aggregator: Aggregator<Maybe<Accumulation>>,\n    attributesProcessor: AttributesProcessor,\n    collectors: MetricCollectorHandle[]\n  ): MetricStorage;\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;AAMH,OAAO,EACL,kCAAkC,GAEnC,MAAM,yBAAyB,CAAC;AACjC,OAAO,EAAE,KAAK,EAAE,MAAM,UAAU,CAAC;AACjC,OAAO,EAAE,YAAY,EAAS,MAAM,UAAU,CAAC;AAC/C,OAAO,EAAE,kBAAkB,EAAE,MAAM,sBAAsB,CAAC;AAG1D,OAAO,EAAE,qBAAqB,EAAE,MAAM,yBAAyB,CAAC;AAChE,OAAO,EAAE,kBAAkB,EAAE,MAAM,8BAA8B,CAAC;AAClE,OAAO,EAAE,kBAAkB,EAAE,MAAM,sBAAsB,CAAC;AAC1D,OAAO,EAAE,iBAAiB,EAAE,MAAM,qBAAqB,CAAC;AAExD,OAAO,EAAE,mBAAmB,EAAE,MAAM,6BAA6B,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAGlE;;GAEG,CACH,IAAA,mBAAA;IAKE,SAAA,iBACU,yBAAmD,EACnD,qBAA2C;QAD3C,IAAA,CAAA,yBAAyB,GAAzB,yBAAyB,CAA0B;QACnD,IAAA,CAAA,qBAAqB,GAArB,qBAAqB,CAAsB;QANrD,IAAA,CAAA,qBAAqB,GAAG,6PAAI,wBAAqB,EAAE,CAAC;QACpD,IAAA,CAAA,kBAAkB,GAAG,0PAAI,qBAAkB,EAAE,CAAC;QAO5C,IAAI,CAAC,KAAK,GAAG,oOAAI,QAAK,CAAC,IAAI,CAAC,CAAC;IAC/B,CAAC;IAED,iBAAA,SAAA,CAAA,qBAAqB,GAArB,SAAsB,UAAgC;QACpD,IAAM,QAAQ,GAAG,IAAI,CAAC,sBAAsB,CAAC,UAAU,uPAAE,oBAAiB,CAAC,CAAC;QAE5E,IAAI,QAAQ,CAAC,MAAM,KAAK,CAAC,EAAE;YACzB,OAAO,QAAQ,CAAC,CAAC,CAAC,CAAC;SACpB;QACD,OAAO,kQAAI,qBAAkB,CAAC,QAAQ,CAAC,CAAC;IAC1C,CAAC;IAED,iBAAA,SAAA,CAAA,0BAA0B,GAA1B,SAA2B,UAAgC;QACzD,IAAM,QAAQ,GAAG,IAAI,CAAC,sBAAsB,CAC1C,UAAU,wPACV,qBAAkB,CACnB,CAAC;QAEF,OAAO,QAAQ,CAAC;IAClB,CAAC;IAED;;;;;OAKG,CACG,iBAAA,SAAA,CAAA,OAAO,GAAb,SACE,SAAgC,EAChC,cAAsB,EACtB,OAA8B;;;;;;wBAMf,OAAA;4BAAA,EAAA,OAAA;4BAAM,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAClD,cAAc,EACd,OAAO,KAAA,QAAP,OAAO,KAAA,KAAA,IAAA,KAAA,IAAP,OAAO,CAAE,aAAa,CACvB;yBAAA,CAAA;;wBAHK,MAAM,GAAG,GAAA,IAAA,EAGd;wBACK,QAAQ,GAAG,IAAI,CAAC,qBAAqB,CAAC,WAAW,CAAC,SAAS,CAAC,CAAC;wBAEnE,qDAAqD;wBACrD,IAAI,QAAQ,CAAC,MAAM,KAAK,CAAC,EAAE;4BACzB,OAAA;gCAAA,EAAA,QAAA;gCAAO,IAAI;6BAAA,CAAC;yBACb;wBAEK,cAAc,GAAG,QAAQ,CAC5B,GAAG,CAAC,SAAA,aAAa;4BAChB,OAAO,aAAa,CAAC,OAAO,CAAC,SAAS,EAAE,cAAc,CAAC,CAAC;wBAC1D,CAAC,CAAC,CACD,MAAM,iOAAC,eAAY,CAAC,CAAC;wBAExB,mFAAmF;wBACnF,IAAI,cAAc,CAAC,MAAM,KAAK,CAAC,EAAE;4BAC/B,OAAA;gCAAA,EAAA,QAAA;gCAAO;oCAAE,MAAM,EAAA,MAAA;gCAAA,CAAE;6BAAA,CAAC;yBACnB;wBAED,OAAA;4BAAA,EAAA,QAAA;4BAAO;gCACL,YAAY,EAAE;oCACZ,KAAK,EAAE,IAAI,CAAC,qBAAqB;oCACjC,OAAO,EAAE,cAAc;iCACxB;gCACD,MAAM,EAAA,MAAA;6BACP;yBAAA,CAAC;;;;KACH;IAEO,iBAAA,SAAA,CAAA,sBAAsB,GAA9B,SAIE,UAAgC,EAChC,iBAAoC;QALtC,IAAA,QAAA,IAAA,CA+DC;QAxDC,IAAM,KAAK,GAAG,IAAI,CAAC,yBAAyB,CAAC,YAAY,CAAC,SAAS,CACjE,UAAU,EACV,IAAI,CAAC,qBAAqB,CAC3B,CAAC;QACF,IAAI,QAAQ,GAAG,KAAK,CAAC,GAAG,CAAC,SAAA,IAAI;YAC3B,IAAM,cAAc,GAAG,wRAAA,AAAkC,EACvD,IAAI,EACJ,UAAU,CACX,CAAC;YACF,IAAM,iBAAiB,GACrB,KAAI,CAAC,qBAAqB,CAAC,6BAA6B,CACtD,cAAc,CACf,CAAC;YACJ,IAAI,iBAAiB,IAAI,IAAI,EAAE;gBAC7B,OAAO,iBAAiB,CAAC;aAC1B;YACD,IAAM,UAAU,GAAG,IAAI,CAAC,WAAW,CAAC,gBAAgB,CAAC,cAAc,CAAC,CAAC;YACrE,IAAM,WAAW,GAAG,IAAI,iBAAiB,CACvC,cAAc,EACd,UAAU,EACV,IAAI,CAAC,mBAAmB,EACxB,KAAI,CAAC,yBAAyB,CAAC,gBAAgB,CAC3C,CAAC;YACP,KAAI,CAAC,qBAAqB,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC;YACjD,OAAO,WAAW,CAAC;QACrB,CAAC,CAAC,CAAC;QAEH,0FAA0F;QAC1F,IAAI,QAAQ,CAAC,MAAM,KAAK,CAAC,EAAE;YACzB,IAAM,wBAAwB,GAC5B,IAAI,CAAC,yBAAyB,CAAC,kBAAkB,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;YACrE,IAAM,iBAAiB,GAAG,wBAAwB,CAAC,GAAG,CACpD,SAAC,EAAwB;oBAAxB,KAAA,OAAA,IAAA,EAAwB,EAAvB,SAAS,GAAA,EAAA,CAAA,EAAA,EAAE,WAAW,GAAA,EAAA,CAAA,EAAA;gBACtB,IAAM,iBAAiB,GACrB,KAAI,CAAC,qBAAqB,CAAC,sCAAsC,CAC/D,SAAS,EACT,UAAU,CACX,CAAC;gBACJ,IAAI,iBAAiB,IAAI,IAAI,EAAE;oBAC7B,OAAO,iBAAiB,CAAC;iBAC1B;gBACD,IAAM,UAAU,GAAG,WAAW,CAAC,gBAAgB,CAAC,UAAU,CAAC,CAAC;gBAC5D,IAAM,OAAO,GAAG,IAAI,iBAAiB,CACnC,UAAU,EACV,UAAU,wPACV,sBAAmB,CAAC,IAAI,EAAE,EAC1B;oBAAC,SAAS;iBAAC,CACP,CAAC;gBACP,KAAI,CAAC,qBAAqB,CAAC,oBAAoB,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;gBACpE,OAAO,OAAO,CAAC;YACjB,CAAC,CACF,CAAC;YACF,QAAQ,GAAG,QAAQ,CAAC,MAAM,CAAC,iBAAiB,CAAC,CAAC;SAC/C;QAED,OAAO,QAAQ,CAAC;IAClB,CAAC;IACH,OAAA,gBAAC;AAAD,CAAC,AA5ID,IA4IC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5980, "column": 0}, "map": {"version": 3, "file": "MeterProviderSharedState.js", "sourceRoot": "", "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/node_modules/%40opentelemetry/sdk-node/node_modules/%40opentelemetry/sdk-metrics/src/state/MeterProviderSharedState.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { InstrumentationScope } from '@opentelemetry/core';\nimport { IResource } from '@opentelemetry/resources';\nimport { Aggregation, InstrumentType } from '..';\nimport { instrumentationScopeId } from '../utils';\nimport { ViewRegistry } from '../view/ViewRegistry';\nimport { MeterSharedState } from './MeterSharedState';\nimport { MetricCollector, MetricCollectorHandle } from './MetricCollector';\n\n/**\n * An internal record for shared meter provider states.\n */\nexport class MeterProviderSharedState {\n  viewRegistry = new ViewRegistry();\n\n  metricCollectors: MetricCollector[] = [];\n\n  meterSharedStates: Map<string, MeterSharedState> = new Map();\n\n  constructor(public resource: IResource) {}\n\n  getMeterSharedState(instrumentationScope: InstrumentationScope) {\n    const id = instrumentationScopeId(instrumentationScope);\n    let meterSharedState = this.meterSharedStates.get(id);\n    if (meterSharedState == null) {\n      meterSharedState = new MeterSharedState(this, instrumentationScope);\n      this.meterSharedStates.set(id, meterSharedState);\n    }\n    return meterSharedState;\n  }\n\n  selectAggregations(instrumentType: InstrumentType) {\n    const result: [MetricCollectorHandle, Aggregation][] = [];\n    for (const collector of this.metricCollectors) {\n      result.push([collector, collector.selectAggregation(instrumentType)]);\n    }\n    return result;\n  }\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;AAKH,OAAO,EAAE,sBAAsB,EAAE,MAAM,UAAU,CAAC;AAClD,OAAO,EAAE,YAAY,EAAE,MAAM,sBAAsB,CAAC;AACpD,OAAO,EAAE,gBAAgB,EAAE,MAAM,oBAAoB,CAAC;;;;;;;;;;;;;;;;;;AAGtD;;GAEG,CACH,IAAA,2BAAA;IAOE,SAAA,yBAAmB,QAAmB;QAAnB,IAAA,CAAA,QAAQ,GAAR,QAAQ,CAAW;QANtC,IAAA,CAAA,YAAY,GAAG,mPAAI,eAAY,EAAE,CAAC;QAElC,IAAA,CAAA,gBAAgB,GAAsB,EAAE,CAAC;QAEzC,IAAA,CAAA,iBAAiB,GAAkC,IAAI,GAAG,EAAE,CAAC;IAEpB,CAAC;IAE1C,yBAAA,SAAA,CAAA,mBAAmB,GAAnB,SAAoB,oBAA0C;QAC5D,IAAM,EAAE,uOAAG,yBAAA,AAAsB,EAAC,oBAAoB,CAAC,CAAC;QACxD,IAAI,gBAAgB,GAAG,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;QACtD,IAAI,gBAAgB,IAAI,IAAI,EAAE;YAC5B,gBAAgB,GAAG,IAAI,uQAAgB,CAAC,IAAI,EAAE,oBAAoB,CAAC,CAAC;YACpE,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,EAAE,EAAE,gBAAgB,CAAC,CAAC;SAClD;QACD,OAAO,gBAAgB,CAAC;IAC1B,CAAC;IAED,yBAAA,SAAA,CAAA,kBAAkB,GAAlB,SAAmB,cAA8B;;QAC/C,IAAM,MAAM,GAA2C,EAAE,CAAC;;YAC1D,IAAwB,IAAA,KAAA,SAAA,IAAI,CAAC,gBAAgB,CAAA,EAAA,KAAA,GAAA,IAAA,EAAA,EAAA,CAAA,GAAA,IAAA,EAAA,KAAA,GAAA,IAAA,GAAE;gBAA1C,IAAM,SAAS,GAAA,GAAA,KAAA;gBAClB,MAAM,CAAC,IAAI,CAAC;oBAAC,SAAS;oBAAE,SAAS,CAAC,iBAAiB,CAAC,cAAc,CAAC;iBAAC,CAAC,CAAC;aACvE;;;;;;;;;;;;QACD,OAAO,MAAM,CAAC;IAChB,CAAC;IACH,OAAA,wBAAC;AAAD,CAAC,AA1BD,IA0BC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 6069, "column": 0}, "map": {"version": 3, "file": "MetricCollector.js", "sourceRoot": "", "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/node_modules/%40opentelemetry/sdk-node/node_modules/%40opentelemetry/sdk-metrics/src/state/MetricCollector.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { millisToHrTime } from '@opentelemetry/core';\nimport { AggregationTemporalitySelector } from '../export/AggregationSelector';\nimport { CollectionResult, ScopeMetrics } from '../export/MetricData';\nimport { MetricProducer, MetricCollectOptions } from '../export/MetricProducer';\nimport { MetricReader } from '../export/MetricReader';\nimport { InstrumentType } from '../InstrumentDescriptor';\nimport { ForceFlushOptions, ShutdownOptions } from '../types';\nimport { MeterProviderSharedState } from './MeterProviderSharedState';\n\n/**\n * An internal opaque interface that the MetricReader receives as\n * MetricProducer. It acts as the storage key to the internal metric stream\n * state for each MetricReader.\n */\nexport class MetricCollector implements MetricProducer {\n  constructor(\n    private _sharedState: MeterProviderSharedState,\n    private _metricReader: MetricReader\n  ) {}\n\n  async collect(options?: MetricCollectOptions): Promise<CollectionResult> {\n    const collectionTime = millisToHrTime(Date.now());\n    const scopeMetrics: ScopeMetrics[] = [];\n    const errors: unknown[] = [];\n\n    const meterCollectionPromises = Array.from(\n      this._sharedState.meterSharedStates.values()\n    ).map(async meterSharedState => {\n      const current = await meterSharedState.collect(\n        this,\n        collectionTime,\n        options\n      );\n\n      // only add scope metrics if available\n      if (current?.scopeMetrics != null) {\n        scopeMetrics.push(current.scopeMetrics);\n      }\n\n      // only add errors if available\n      if (current?.errors != null) {\n        errors.push(...current.errors);\n      }\n    });\n    await Promise.all(meterCollectionPromises);\n\n    return {\n      resourceMetrics: {\n        resource: this._sharedState.resource,\n        scopeMetrics: scopeMetrics,\n      },\n      errors: errors,\n    };\n  }\n\n  /**\n   * Delegates for MetricReader.forceFlush.\n   */\n  async forceFlush(options?: ForceFlushOptions): Promise<void> {\n    await this._metricReader.forceFlush(options);\n  }\n\n  /**\n   * Delegates for MetricReader.shutdown.\n   */\n  async shutdown(options?: ShutdownOptions): Promise<void> {\n    await this._metricReader.shutdown(options);\n  }\n\n  selectAggregationTemporality(instrumentType: InstrumentType) {\n    return this._metricReader.selectAggregationTemporality(instrumentType);\n  }\n\n  selectAggregation(instrumentType: InstrumentType) {\n    return this._metricReader.selectAggregation(instrumentType);\n  }\n}\n\n/**\n * An internal interface for MetricCollector. Exposes the necessary\n * information for metric collection.\n */\nexport interface MetricCollectorHandle {\n  selectAggregationTemporality: AggregationTemporalitySelector;\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;AAEH,OAAO,EAAE,cAAc,EAAE,MAAM,qBAAqB,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASrD;;;;GAIG,CACH,IAAA,kBAAA;IACE,SAAA,gBACU,YAAsC,EACtC,aAA2B;QAD3B,IAAA,CAAA,YAAY,GAAZ,YAAY,CAA0B;QACtC,IAAA,CAAA,aAAa,GAAb,aAAa,CAAc;IAClC,CAAC;IAEE,gBAAA,SAAA,CAAA,OAAO,GAAb,SAAc,OAA8B;;;;;;;wBACpC,cAAc,sOAAG,iBAAA,AAAc,EAAC,IAAI,CAAC,GAAG,EAAE,CAAC,CAAC;wBAC5C,YAAY,GAAmB,EAAE,CAAC;wBAClC,MAAM,GAAc,EAAE,CAAC;wBAEvB,uBAAuB,GAAG,KAAK,CAAC,IAAI,CACxC,IAAI,CAAC,YAAY,CAAC,iBAAiB,CAAC,MAAM,EAAE,CAC7C,CAAC,GAAG,CAAC,SAAM,gBAAgB;4BAAA,OAAA,UAAA,OAAA,KAAA,GAAA,KAAA,GAAA;;;;;4CACV,OAAA;gDAAA,EAAA,OAAA;gDAAM,gBAAgB,CAAC,OAAO,CAC5C,IAAI,EACJ,cAAc,EACd,OAAO,CACR;6CAAA,CAAA;;4CAJK,OAAO,GAAG,GAAA,IAAA,EAIf;4CAED,sCAAsC;4CACtC,IAAI,CAAA,OAAO,KAAA,QAAP,OAAO,KAAA,KAAA,IAAA,KAAA,IAAP,OAAO,CAAE,YAAY,KAAI,IAAI,EAAE;gDACjC,YAAY,CAAC,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC;6CACzC;4CAED,+BAA+B;4CAC/B,IAAI,CAAA,OAAO,KAAA,QAAP,OAAO,KAAA,KAAA,IAAA,KAAA,IAAP,OAAO,CAAE,MAAM,KAAI,IAAI,EAAE;gDAC3B,MAAM,CAAC,IAAI,CAAA,KAAA,CAAX,MAAM,EAAA,cAAA,EAAA,EAAA,OAAS,OAAO,CAAC,MAAM,GAAA,QAAE;6CAChC;;;;;;;yBACF,CAAC,CAAC;wBACH,OAAA;4BAAA,EAAA,OAAA;4BAAM,OAAO,CAAC,GAAG,CAAC,uBAAuB,CAAC;yBAAA,CAAA;;wBAA1C,GAAA,IAAA,EAA0C,CAAC;wBAE3C,OAAA;4BAAA,EAAA,QAAA;4BAAO;gCACL,eAAe,EAAE;oCACf,QAAQ,EAAE,IAAI,CAAC,YAAY,CAAC,QAAQ;oCACpC,YAAY,EAAE,YAAY;iCAC3B;gCACD,MAAM,EAAE,MAAM;6BACf;yBAAA,CAAC;;;;KACH;IAED;;OAEG,CACG,gBAAA,SAAA,CAAA,UAAU,GAAhB,SAAiB,OAA2B;;;;;wBAC1C,OAAA;4BAAA,EAAA,OAAA;4BAAM,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC,OAAO,CAAC;yBAAA,CAAA;;wBAA5C,GAAA,IAAA,EAA4C,CAAC;;;;;;;KAC9C;IAED;;OAEG,CACG,gBAAA,SAAA,CAAA,QAAQ,GAAd,SAAe,OAAyB;;;;;wBACtC,OAAA;4BAAA,EAAA,OAAA;4BAAM,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,OAAO,CAAC;yBAAA,CAAA;;wBAA1C,GAAA,IAAA,EAA0C,CAAC;;;;;;;KAC5C;IAED,gBAAA,SAAA,CAAA,4BAA4B,GAA5B,SAA6B,cAA8B;QACzD,OAAO,IAAI,CAAC,aAAa,CAAC,4BAA4B,CAAC,cAAc,CAAC,CAAC;IACzE,CAAC;IAED,gBAAA,SAAA,CAAA,iBAAiB,GAAjB,SAAkB,cAA8B;QAC9C,OAAO,IAAI,CAAC,aAAa,CAAC,iBAAiB,CAAC,cAAc,CAAC,CAAC;IAC9D,CAAC;IACH,OAAA,eAAC;AAAD,CAAC,AA9DD,IA8DC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 6361, "column": 0}, "map": {"version": 3, "file": "MeterProvider.js", "sourceRoot": "", "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/node_modules/%40opentelemetry/sdk-node/node_modules/%40opentelemetry/sdk-metrics/src/MeterProvider.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  diag,\n  MeterProvider as IMeterProvider,\n  Meter as IMeter,\n  MeterOptions,\n  createNoopMeter,\n} from '@opentelemetry/api';\nimport { IResource, Resource } from '@opentelemetry/resources';\nimport { MetricReader } from './export/MetricReader';\nimport { MeterProviderSharedState } from './state/MeterProviderSharedState';\nimport { MetricCollector } from './state/MetricCollector';\nimport { ForceFlushOptions, ShutdownOptions } from './types';\nimport { View } from './view/View';\n\n/**\n * MeterProviderOptions provides an interface for configuring a MeterProvider.\n */\nexport interface MeterProviderOptions {\n  /** Resource associated with metric telemetry  */\n  resource?: IResource;\n  views?: View[];\n  readers?: MetricReader[];\n}\n\n/**\n * This class implements the {@link MeterProvider} interface.\n */\nexport class MeterProvider implements IMeterProvider {\n  private _sharedState: MeterProviderSharedState;\n  private _shutdown = false;\n\n  constructor(options?: MeterProviderOptions) {\n    const resource = Resource.default().merge(\n      options?.resource ?? Resource.empty()\n    );\n    this._sharedState = new MeterProviderSharedState(resource);\n    if (options?.views != null && options.views.length > 0) {\n      for (const view of options.views) {\n        this._sharedState.viewRegistry.addView(view);\n      }\n    }\n\n    if (options?.readers != null && options.readers.length > 0) {\n      for (const metricReader of options.readers) {\n        this.addMetricReader(metricReader);\n      }\n    }\n  }\n\n  /**\n   * Get a meter with the configuration of the MeterProvider.\n   */\n  getMeter(name: string, version = '', options: MeterOptions = {}): IMeter {\n    // https://github.com/open-telemetry/opentelemetry-specification/blob/main/specification/metrics/sdk.md#meter-creation\n    if (this._shutdown) {\n      diag.warn('A shutdown MeterProvider cannot provide a Meter');\n      return createNoopMeter();\n    }\n\n    return this._sharedState.getMeterSharedState({\n      name,\n      version,\n      schemaUrl: options.schemaUrl,\n    }).meter;\n  }\n\n  /**\n   * Register a {@link MetricReader} to the meter provider. After the\n   * registration, the MetricReader can start metrics collection.\n   *\n   * <p> NOTE: {@link MetricReader} instances MUST be added before creating any instruments.\n   * A {@link MetricReader} instance registered later may receive no or incomplete metric data.\n   *\n   * @param metricReader the metric reader to be registered.\n   *\n   * @deprecated This method will be removed in SDK 2.0. Please use\n   * {@link MeterProviderOptions.readers} via the {@link MeterProvider} constructor instead\n   */\n  addMetricReader(metricReader: MetricReader) {\n    const collector = new MetricCollector(this._sharedState, metricReader);\n    metricReader.setMetricProducer(collector);\n    this._sharedState.metricCollectors.push(collector);\n  }\n\n  /**\n   * Flush all buffered data and shut down the MeterProvider and all registered\n   * MetricReaders.\n   *\n   * Returns a promise which is resolved when all flushes are complete.\n   */\n  async shutdown(options?: ShutdownOptions): Promise<void> {\n    if (this._shutdown) {\n      diag.warn('shutdown may only be called once per MeterProvider');\n      return;\n    }\n\n    this._shutdown = true;\n\n    await Promise.all(\n      this._sharedState.metricCollectors.map(collector => {\n        return collector.shutdown(options);\n      })\n    );\n  }\n\n  /**\n   * Notifies all registered MetricReaders to flush any buffered data.\n   *\n   * Returns a promise which is resolved when all flushes are complete.\n   */\n  async forceFlush(options?: ForceFlushOptions): Promise<void> {\n    // do not flush after shutdown\n    if (this._shutdown) {\n      diag.warn('invalid attempt to force flush after MeterProvider shutdown');\n      return;\n    }\n\n    await Promise.all(\n      this._sharedState.metricCollectors.map(collector => {\n        return collector.forceFlush(options);\n      })\n    );\n  }\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;;AAEH,OAAO,EACL,IAAI,EAIJ,eAAe,GAChB,MAAM,oBAAoB,CAAC;AAC5B,OAAO,EAAa,QAAQ,EAAE,MAAM,0BAA0B,CAAC;AAE/D,OAAO,EAAE,wBAAwB,EAAE,MAAM,kCAAkC,CAAC;AAC5E,OAAO,EAAE,eAAe,EAAE,MAAM,yBAAyB,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAc1D;;GAEG,CACH,IAAA,gBAAA;IAIE,SAAA,cAAY,OAA8B;;;QAFlC,IAAA,CAAA,SAAS,GAAG,KAAK,CAAC;QAGxB,IAAM,QAAQ,iOAAG,WAAQ,CAAC,OAAO,EAAE,CAAC,KAAK,CACvC,CAAA,KAAA,OAAO,KAAA,QAAP,OAAO,KAAA,KAAA,IAAA,KAAA,IAAP,OAAO,CAAE,QAAQ,MAAA,QAAA,OAAA,KAAA,IAAA,KAAI,yOAAQ,CAAC,KAAK,EAAE,CACtC,CAAC;QACF,IAAI,CAAC,YAAY,GAAG,gQAAI,2BAAwB,CAAC,QAAQ,CAAC,CAAC;QAC3D,IAAI,CAAA,OAAO,KAAA,QAAP,OAAO,KAAA,KAAA,IAAA,KAAA,IAAP,OAAO,CAAE,KAAK,KAAI,IAAI,IAAI,OAAO,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE;;gBACtD,IAAmB,IAAA,KAAA,SAAA,OAAO,CAAC,KAAK,CAAA,EAAA,KAAA,GAAA,IAAA,EAAA,EAAA,CAAA,GAAA,IAAA,EAAA,KAAA,GAAA,IAAA,GAAE;oBAA7B,IAAM,IAAI,GAAA,GAAA,KAAA;oBACb,IAAI,CAAC,YAAY,CAAC,YAAY,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;iBAC9C;;;;;;;;;;;;SACF;QAED,IAAI,CAAA,OAAO,KAAA,QAAP,OAAO,KAAA,KAAA,IAAA,KAAA,IAAP,OAAO,CAAE,OAAO,KAAI,IAAI,IAAI,OAAO,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE;;gBAC1D,IAA2B,IAAA,KAAA,SAAA,OAAO,CAAC,OAAO,CAAA,EAAA,KAAA,GAAA,IAAA,EAAA,EAAA,CAAA,GAAA,IAAA,EAAA,KAAA,GAAA,IAAA,GAAE;oBAAvC,IAAM,YAAY,GAAA,GAAA,KAAA;oBACrB,IAAI,CAAC,eAAe,CAAC,YAAY,CAAC,CAAC;iBACpC;;;;;;;;;;;;SACF;IACH,CAAC;IAED;;OAEG,CACH,cAAA,SAAA,CAAA,QAAQ,GAAR,SAAS,IAAY,EAAE,OAAY,EAAE,OAA0B;QAAxC,IAAA,YAAA,KAAA,GAAA;YAAA,UAAA,EAAY;QAAA;QAAE,IAAA,YAAA,KAAA,GAAA;YAAA,UAAA,CAAA,CAA0B;QAAA;QAC7D,sHAAsH;QACtH,IAAI,IAAI,CAAC,SAAS,EAAE;qLAClB,OAAI,CAAC,IAAI,CAAC,iDAAiD,CAAC,CAAC;YAC7D,6LAAO,kBAAA,AAAe,EAAE,CAAC;SAC1B;QAED,OAAO,IAAI,CAAC,YAAY,CAAC,mBAAmB,CAAC;YAC3C,IAAI,EAAA,IAAA;YACJ,OAAO,EAAA,OAAA;YACP,SAAS,EAAE,OAAO,CAAC,SAAS;SAC7B,CAAC,CAAC,KAAK,CAAC;IACX,CAAC;IAED;;;;;;;;;;;OAWG,CACH,cAAA,SAAA,CAAA,eAAe,GAAf,SAAgB,YAA0B;QACxC,IAAM,SAAS,GAAG,uPAAI,kBAAe,CAAC,IAAI,CAAC,YAAY,EAAE,YAAY,CAAC,CAAC;QACvE,YAAY,CAAC,iBAAiB,CAAC,SAAS,CAAC,CAAC;QAC1C,IAAI,CAAC,YAAY,CAAC,gBAAgB,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;IACrD,CAAC;IAED;;;;;OAKG,CACG,cAAA,SAAA,CAAA,QAAQ,GAAd,SAAe,OAAyB;;;;;wBACtC,IAAI,IAAI,CAAC,SAAS,EAAE;oMAClB,QAAI,CAAC,IAAI,CAAC,oDAAoD,CAAC,CAAC;4BAChE,OAAA;gCAAA,EAAA,QAAA;6BAAA,CAAO;yBACR;wBAED,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;wBAEtB,OAAA;4BAAA,EAAA,OAAA;4BAAM,OAAO,CAAC,GAAG,CACf,IAAI,CAAC,YAAY,CAAC,gBAAgB,CAAC,GAAG,CAAC,SAAA,SAAS;gCAC9C,OAAO,SAAS,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;4BACrC,CAAC,CAAC,CACH;yBAAA,CAAA;;wBAJD,GAAA,IAAA,EAIC,CAAC;;;;;;;KACH;IAED;;;;OAIG,CACG,cAAA,SAAA,CAAA,UAAU,GAAhB,SAAiB,OAA2B;;;;;wBAC1C,8BAA8B;wBAC9B,IAAI,IAAI,CAAC,SAAS,EAAE;qMAClB,OAAI,CAAC,IAAI,CAAC,6DAA6D,CAAC,CAAC;4BACzE,OAAA;gCAAA,EAAA,QAAA;6BAAA,CAAO;yBACR;wBAED,OAAA;4BAAA,EAAA,OAAA;4BAAM,OAAO,CAAC,GAAG,CACf,IAAI,CAAC,YAAY,CAAC,gBAAgB,CAAC,GAAG,CAAC,SAAA,SAAS;gCAC9C,OAAO,SAAS,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC;4BACvC,CAAC,CAAC,CACH;yBAAA,CAAA;;wBAJD,GAAA,IAAA,EAIC,CAAC;;;;;;;KACH;IACH,OAAA,aAAC;AAAD,CAAC,AAhGD,IAgGC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 6679, "column": 0}, "map": {"version": 3, "file": "Predicate.js", "sourceRoot": "", "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/node_modules/%40opentelemetry/sdk-node/node_modules/%40opentelemetry/sdk-metrics/src/view/Predicate.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n// https://tc39.es/proposal-regex-escaping\n// escape ^ $ \\ .  + ? ( ) [ ] { } |\n// do not need to escape * as we interpret it as wildcard\nconst ESCAPE = /[\\^$\\\\.+?()[\\]{}|]/g;\n\nexport interface Predicate {\n  match(str: string): boolean;\n}\n\n/**\n * Wildcard pattern predicate, supports patterns like `*`, `foo*`, `*bar`.\n */\nexport class PatternPredicate implements Predicate {\n  private _matchAll: boolean;\n  private _regexp: RegExp;\n\n  constructor(pattern: string) {\n    if (pattern === '*') {\n      this._matchAll = true;\n      this._regexp = /.*/;\n    } else {\n      this._matchAll = false;\n      this._regexp = new RegExp(PatternPredicate.escapePattern(pattern));\n    }\n  }\n\n  match(str: string): boolean {\n    if (this._matchAll) {\n      return true;\n    }\n\n    return this._regexp.test(str);\n  }\n\n  static escapePattern(pattern: string): string {\n    return `^${pattern.replace(ESCAPE, '\\\\$&').replace('*', '.*')}$`;\n  }\n\n  static hasWildcard(pattern: string): boolean {\n    return pattern.includes('*');\n  }\n}\n\nexport class ExactPredicate implements Predicate {\n  private _matchAll: boolean;\n  private _pattern?: string;\n\n  constructor(pattern?: string) {\n    this._matchAll = pattern === undefined;\n    this._pattern = pattern;\n  }\n\n  match(str: string): boolean {\n    if (this._matchAll) {\n      return true;\n    }\n    if (str === this._pattern) {\n      return true;\n    }\n    return false;\n  }\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG,CAEH,0CAA0C;AAC1C,oCAAoC;AACpC,yDAAyD;;;;;AACzD,IAAM,MAAM,GAAG,qBAAqB,CAAC;AAMrC;;GAEG,CACH,IAAA,mBAAA;IAIE,SAAA,iBAAY,OAAe;QACzB,IAAI,OAAO,KAAK,GAAG,EAAE;YACnB,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;YACtB,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;SACrB,MAAM;YACL,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC;YACvB,IAAI,CAAC,OAAO,GAAG,IAAI,MAAM,CAAC,gBAAgB,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC,CAAC;SACpE;IACH,CAAC;IAED,iBAAA,SAAA,CAAA,KAAK,GAAL,SAAM,GAAW;QACf,IAAI,IAAI,CAAC,SAAS,EAAE;YAClB,OAAO,IAAI,CAAC;SACb;QAED,OAAO,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;IAChC,CAAC;IAEM,iBAAA,aAAa,GAApB,SAAqB,OAAe;QAClC,OAAO,MAAI,OAAO,CAAC,OAAO,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC,OAAO,CAAC,GAAG,EAAE,IAAI,CAAC,GAAA,GAAG,CAAC;IACnE,CAAC;IAEM,iBAAA,WAAW,GAAlB,SAAmB,OAAe;QAChC,OAAO,OAAO,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;IAC/B,CAAC;IACH,OAAA,gBAAC;AAAD,CAAC,AA7BD,IA6BC;;AAED,IAAA,iBAAA;IAIE,SAAA,eAAY,OAAgB;QAC1B,IAAI,CAAC,SAAS,GAAG,OAAO,KAAK,SAAS,CAAC;QACvC,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC;IAC1B,CAAC;IAED,eAAA,SAAA,CAAA,KAAK,GAAL,SAAM,GAAW;QACf,IAAI,IAAI,CAAC,SAAS,EAAE;YAClB,OAAO,IAAI,CAAC;SACb;QACD,IAAI,GAAG,KAAK,IAAI,CAAC,QAAQ,EAAE;YACzB,OAAO,IAAI,CAAC;SACb;QACD,OAAO,KAAK,CAAC;IACf,CAAC;IACH,OAAA,cAAC;AAAD,CAAC,AAlBD,IAkBC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 6752, "column": 0}, "map": {"version": 3, "file": "InstrumentSelector.js", "sourceRoot": "", "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/node_modules/%40opentelemetry/sdk-node/node_modules/%40opentelemetry/sdk-metrics/src/view/InstrumentSelector.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { InstrumentType } from '../InstrumentDescriptor';\nimport { ExactPredicate, PatternPredicate, Predicate } from './Predicate';\n\nexport interface InstrumentSelectorCriteria {\n  name?: string;\n  type?: InstrumentType;\n  unit?: string;\n}\n\nexport class InstrumentSelector {\n  private _nameFilter: Predicate;\n  private _type?: InstrumentType;\n  private _unitFilter: Predicate;\n\n  constructor(criteria?: InstrumentSelectorCriteria) {\n    this._nameFilter = new PatternPredicate(criteria?.name ?? '*');\n    this._type = criteria?.type;\n    this._unitFilter = new ExactPredicate(criteria?.unit);\n  }\n\n  getType() {\n    return this._type;\n  }\n\n  getNameFilter() {\n    return this._nameFilter;\n  }\n\n  getUnitFilter() {\n    return this._unitFilter;\n  }\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;AAGH,OAAO,EAAE,cAAc,EAAE,gBAAgB,EAAa,MAAM,aAAa,CAAC;;AAQ1E,IAAA,qBAAA;IAKE,SAAA,mBAAY,QAAqC;;QAC/C,IAAI,CAAC,WAAW,GAAG,gPAAI,mBAAgB,CAAC,CAAA,KAAA,QAAQ,KAAA,QAAR,QAAQ,KAAA,KAAA,IAAA,KAAA,IAAR,QAAQ,CAAE,IAAI,MAAA,QAAA,OAAA,KAAA,IAAA,KAAI,GAAG,CAAC,CAAC;QAC/D,IAAI,CAAC,KAAK,GAAG,QAAQ,KAAA,QAAR,QAAQ,KAAA,KAAA,IAAA,KAAA,IAAR,QAAQ,CAAE,IAAI,CAAC;QAC5B,IAAI,CAAC,WAAW,GAAG,gPAAI,iBAAc,CAAC,QAAQ,KAAA,QAAR,QAAQ,KAAA,KAAA,IAAA,KAAA,IAAR,QAAQ,CAAE,IAAI,CAAC,CAAC;IACxD,CAAC;IAED,mBAAA,SAAA,CAAA,OAAO,GAAP;QACE,OAAO,IAAI,CAAC,KAAK,CAAC;IACpB,CAAC;IAED,mBAAA,SAAA,CAAA,aAAa,GAAb;QACE,OAAO,IAAI,CAAC,WAAW,CAAC;IAC1B,CAAC;IAED,mBAAA,SAAA,CAAA,aAAa,GAAb;QACE,OAAO,IAAI,CAAC,WAAW,CAAC;IAC1B,CAAC;IACH,OAAA,kBAAC;AAAD,CAAC,AAtBD,IAsBC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 6797, "column": 0}, "map": {"version": 3, "file": "MeterSelector.js", "sourceRoot": "", "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/node_modules/%40opentelemetry/sdk-node/node_modules/%40opentelemetry/sdk-metrics/src/view/MeterSelector.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { ExactPredicate, Predicate } from './Predicate';\n\nexport interface MeterSelectorCriteria {\n  name?: string;\n  version?: string;\n  schemaUrl?: string;\n}\n\nexport class MeterSelector {\n  private _nameFilter: Predicate;\n  private _versionFilter: Predicate;\n  private _schemaUrlFilter: Predicate;\n\n  constructor(criteria?: MeterSelectorCriteria) {\n    this._nameFilter = new ExactPredicate(criteria?.name);\n    this._versionFilter = new ExactPredicate(criteria?.version);\n    this._schemaUrlFilter = new ExactPredicate(criteria?.schemaUrl);\n  }\n\n  getNameFilter() {\n    return this._nameFilter;\n  }\n\n  /**\n   * TODO: semver filter? no spec yet.\n   */\n  getVersionFilter() {\n    return this._versionFilter;\n  }\n\n  getSchemaUrlFilter() {\n    return this._schemaUrlFilter;\n  }\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;AAEH,OAAO,EAAE,cAAc,EAAa,MAAM,aAAa,CAAC;;AAQxD,IAAA,gBAAA;IAKE,SAAA,cAAY,QAAgC;QAC1C,IAAI,CAAC,WAAW,GAAG,gPAAI,iBAAc,CAAC,QAAQ,KAAA,QAAR,QAAQ,KAAA,KAAA,IAAA,KAAA,IAAR,QAAQ,CAAE,IAAI,CAAC,CAAC;QACtD,IAAI,CAAC,cAAc,GAAG,gPAAI,iBAAc,CAAC,QAAQ,KAAA,QAAR,QAAQ,KAAA,KAAA,IAAA,KAAA,IAAR,QAAQ,CAAE,OAAO,CAAC,CAAC;QAC5D,IAAI,CAAC,gBAAgB,GAAG,gPAAI,iBAAc,CAAC,QAAQ,KAAA,QAAR,QAAQ,KAAA,KAAA,IAAA,KAAA,IAAR,QAAQ,CAAE,SAAS,CAAC,CAAC;IAClE,CAAC;IAED,cAAA,SAAA,CAAA,aAAa,GAAb;QACE,OAAO,IAAI,CAAC,WAAW,CAAC;IAC1B,CAAC;IAED;;OAEG,CACH,cAAA,SAAA,CAAA,gBAAgB,GAAhB;QACE,OAAO,IAAI,CAAC,cAAc,CAAC;IAC7B,CAAC;IAED,cAAA,SAAA,CAAA,kBAAkB,GAAlB;QACE,OAAO,IAAI,CAAC,gBAAgB,CAAC;IAC/B,CAAC;IACH,OAAA,aAAC;AAAD,CAAC,AAzBD,IAyBC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 6843, "column": 0}, "map": {"version": 3, "file": "View.js", "sourceRoot": "", "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/node_modules/%40opentelemetry/sdk-node/node_modules/%40opentelemetry/sdk-metrics/src/view/View.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { PatternPredicate } from './Predicate';\nimport {\n  AttributesProcessor,\n  FilteringAttributesProcessor,\n} from './AttributesProcessor';\nimport { InstrumentSelector } from './InstrumentSelector';\nimport { MeterSelector } from './MeterSelector';\nimport { Aggregation } from './Aggregation';\nimport { InstrumentType } from '../InstrumentDescriptor';\n\nexport type ViewOptions = {\n  /**\n   *  Alters the metric stream:\n   *  This will be used as the name of the metrics stream.\n   *  If not provided, the original Instrument name will be used.\n   */\n  name?: string;\n  /**\n   * Alters the metric stream:\n   * This will be used as the description of the metrics stream.\n   * If not provided, the original Instrument description will be used by default.\n   *\n   * @example <caption>changes the description of all selected instruments to 'sample description'</caption>\n   * description: 'sample description'\n   */\n  description?: string;\n  /**\n   * Alters the metric stream:\n   * If provided, the attributes that are not in the list will be ignored.\n   * If not provided, all attribute keys will be used by default.\n   *\n   * @example <caption>drops all attributes with top-level keys except for 'myAttr' and 'myOtherAttr'</caption>\n   * attributeKeys: ['myAttr', 'myOtherAttr']\n   * @example <caption>drops all attributes</caption>\n   * attributeKeys: []\n   */\n  attributeKeys?: string[];\n  /**\n   * Alters the metric stream:\n   * Alters the {@link Aggregation} of the metric stream.\n   *\n   * @example <caption>changes the aggregation of the selected instrument(s) to ExplicitBucketHistogramAggregation</caption>\n   * aggregation: new ExplicitBucketHistogramAggregation([1, 10, 100])\n   * @example <caption>changes the aggregation of the selected instrument(s) to LastValueAggregation</caption>\n   * aggregation: new LastValueAggregation()\n   */\n  aggregation?: Aggregation;\n  /**\n   * Instrument selection criteria:\n   * The original type of the Instrument(s).\n   *\n   * @example <caption>selects all counters</caption>\n   * instrumentType: InstrumentType.COUNTER\n   * @example <caption>selects all histograms</caption>\n   * instrumentType: InstrumentType.HISTOGRAM\n   */\n  instrumentType?: InstrumentType;\n  /**\n   * Instrument selection criteria:\n   * Original name of the Instrument(s) with wildcard support.\n   *\n   * @example <caption>select all instruments</caption>\n   * instrumentName: '*'\n   * @example <caption>select all instruments starting with 'my.instruments.'</caption>\n   * instrumentName: 'my.instruments.*'\n   * @example <caption>select all instruments named 'my.instrument.requests' exactly</caption>\n   * instrumentName: 'my.instruments.requests'\n   */\n  instrumentName?: string;\n  /**\n   * Instrument selection criteria:\n   * The unit of the Instrument(s).\n   *\n   * @example <caption>select all instruments with unit 'ms'</caption>\n   * instrumentUnit: 'ms'\n   */\n  instrumentUnit?: string;\n  /**\n   * Instrument selection criteria:\n   * The name of the Meter. No wildcard support, name must match the meter exactly.\n   *\n   * @example <caption>select all meters named 'example.component.app' exactly</caption>\n   * meterName: 'example.component.app'\n   */\n  meterName?: string;\n  /**\n   * Instrument selection criteria:\n   * The version of the Meter. No wildcard support, version must match exactly.\n   *\n   * @example\n   * meterVersion: '1.0.1'\n   */\n  meterVersion?: string;\n  /**\n   * Instrument selection criteria:\n   * The schema URL of the Meter. No wildcard support, schema URL must match exactly.\n   *\n   * @example <caption>Select all meters with schema URL 'https://example.com/schema' exactly.</caption>\n   * meterSchemaUrl: 'https://example.com/schema'\n   */\n  meterSchemaUrl?: string;\n};\n\nfunction isSelectorNotProvided(options: ViewOptions): boolean {\n  return (\n    options.instrumentName == null &&\n    options.instrumentType == null &&\n    options.instrumentUnit == null &&\n    options.meterName == null &&\n    options.meterVersion == null &&\n    options.meterSchemaUrl == null\n  );\n}\n\n/**\n * Can be passed to a {@link MeterProvider} to select instruments and alter their metric stream.\n */\nexport class View {\n  readonly name?: string;\n  readonly description?: string;\n  readonly aggregation: Aggregation;\n  readonly attributesProcessor: AttributesProcessor;\n  readonly instrumentSelector: InstrumentSelector;\n  readonly meterSelector: MeterSelector;\n\n  /**\n   * Create a new {@link View} instance.\n   *\n   * Parameters can be categorized as two types:\n   *  Instrument selection criteria: Used to describe the instrument(s) this view will be applied to.\n   *  Will be treated as additive (the Instrument has to meet all the provided criteria to be selected).\n   *\n   *  Metric stream altering: Alter the metric stream of instruments selected by instrument selection criteria.\n   *\n   * @param viewOptions {@link ViewOptions} for altering the metric stream and instrument selection.\n   * @param viewOptions.name\n   * Alters the metric stream:\n   *  This will be used as the name of the metrics stream.\n   *  If not provided, the original Instrument name will be used.\n   * @param viewOptions.description\n   * Alters the metric stream:\n   *  This will be used as the description of the metrics stream.\n   *  If not provided, the original Instrument description will be used by default.\n   * @param viewOptions.attributeKeys\n   * Alters the metric stream:\n   *  If provided, the attributes that are not in the list will be ignored.\n   *  If not provided, all attribute keys will be used by default.\n   * @param viewOptions.aggregation\n   * Alters the metric stream:\n   *  Alters the {@link Aggregation} of the metric stream.\n   * @param viewOptions.instrumentName\n   * Instrument selection criteria:\n   *  Original name of the Instrument(s) with wildcard support.\n   * @param viewOptions.instrumentType\n   * Instrument selection criteria:\n   *  The original type of the Instrument(s).\n   * @param viewOptions.instrumentUnit\n   * Instrument selection criteria:\n   *  The unit of the Instrument(s).\n   * @param viewOptions.meterName\n   * Instrument selection criteria:\n   *  The name of the Meter. No wildcard support, name must match the meter exactly.\n   * @param viewOptions.meterVersion\n   * Instrument selection criteria:\n   *  The version of the Meter. No wildcard support, version must match exactly.\n   * @param viewOptions.meterSchemaUrl\n   * Instrument selection criteria:\n   *  The schema URL of the Meter. No wildcard support, schema URL must match exactly.\n   *\n   * @example\n   * // Create a view that changes the Instrument 'my.instrument' to use to an\n   * // ExplicitBucketHistogramAggregation with the boundaries [20, 30, 40]\n   * new View({\n   *   aggregation: new ExplicitBucketHistogramAggregation([20, 30, 40]),\n   *   instrumentName: 'my.instrument'\n   * })\n   */\n  constructor(viewOptions: ViewOptions) {\n    // If no criteria is provided, the SDK SHOULD treat it as an error.\n    // It is recommended that the SDK implementations fail fast.\n    if (isSelectorNotProvided(viewOptions)) {\n      throw new Error('Cannot create view with no selector arguments supplied');\n    }\n\n    // the SDK SHOULD NOT allow Views with a specified name to be declared with instrument selectors that\n    // may select more than one instrument (e.g. wild card instrument name) in the same Meter.\n    if (\n      viewOptions.name != null &&\n      (viewOptions?.instrumentName == null ||\n        PatternPredicate.hasWildcard(viewOptions.instrumentName))\n    ) {\n      throw new Error(\n        'Views with a specified name must be declared with an instrument selector that selects at most one instrument per meter.'\n      );\n    }\n\n    // Create AttributesProcessor if attributeKeys are defined set.\n    if (viewOptions.attributeKeys != null) {\n      this.attributesProcessor = new FilteringAttributesProcessor(\n        viewOptions.attributeKeys\n      );\n    } else {\n      this.attributesProcessor = AttributesProcessor.Noop();\n    }\n\n    this.name = viewOptions.name;\n    this.description = viewOptions.description;\n    this.aggregation = viewOptions.aggregation ?? Aggregation.Default();\n    this.instrumentSelector = new InstrumentSelector({\n      name: viewOptions.instrumentName,\n      type: viewOptions.instrumentType,\n      unit: viewOptions.instrumentUnit,\n    });\n    this.meterSelector = new MeterSelector({\n      name: viewOptions.meterName,\n      version: viewOptions.meterVersion,\n      schemaUrl: viewOptions.meterSchemaUrl,\n    });\n  }\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;AAEH,OAAO,EAAE,gBAAgB,EAAE,MAAM,aAAa,CAAC;AAC/C,OAAO,EACL,mBAAmB,EACnB,4BAA4B,GAC7B,MAAM,uBAAuB,CAAC;AAC/B,OAAO,EAAE,kBAAkB,EAAE,MAAM,sBAAsB,CAAC;AAC1D,OAAO,EAAE,aAAa,EAAE,MAAM,iBAAiB,CAAC;AAChD,OAAO,EAAE,WAAW,EAAE,MAAM,eAAe,CAAC;;;;;;AAgG5C,SAAS,qBAAqB,CAAC,OAAoB;IACjD,OAAO,AACL,OAAO,CAAC,cAAc,IAAI,IAAI,IAC9B,OAAO,CAAC,cAAc,IAAI,IAAI,IAC9B,OAAO,CAAC,cAAc,IAAI,IAAI,IAC9B,OAAO,CAAC,SAAS,IAAI,IAAI,IACzB,OAAO,CAAC,YAAY,IAAI,IAAI,IAC5B,OAAO,CAAC,cAAc,IAAI,IAAI,CAC/B,CAAC;AACJ,CAAC;AAED;;GAEG,CACH,IAAA,OAAA;IAQE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;OAmDG,CACH,SAAA,KAAY,WAAwB;;QAClC,mEAAmE;QACnE,4DAA4D;QAC5D,IAAI,qBAAqB,CAAC,WAAW,CAAC,EAAE;YACtC,MAAM,IAAI,KAAK,CAAC,wDAAwD,CAAC,CAAC;SAC3E;QAED,qGAAqG;QACrG,0FAA0F;QAC1F,IACE,WAAW,CAAC,IAAI,IAAI,IAAI,IACxB,CAAC,CAAA,WAAW,KAAA,QAAX,WAAW,KAAA,KAAA,IAAA,KAAA,IAAX,WAAW,CAAE,cAAc,KAAI,IAAI,gPAClC,mBAAgB,CAAC,WAAW,CAAC,WAAW,CAAC,cAAc,CAAC,CAAC,EAC3D;YACA,MAAM,IAAI,KAAK,CACb,yHAAyH,CAC1H,CAAC;SACH;QAED,+DAA+D;QAC/D,IAAI,WAAW,CAAC,aAAa,IAAI,IAAI,EAAE;YACrC,IAAI,CAAC,mBAAmB,GAAG,0PAAI,+BAA4B,CACzD,WAAW,CAAC,aAAa,CAC1B,CAAC;SACH,MAAM;YACL,IAAI,CAAC,mBAAmB,yPAAG,sBAAmB,CAAC,IAAI,EAAE,CAAC;SACvD;QAED,IAAI,CAAC,IAAI,GAAG,WAAW,CAAC,IAAI,CAAC;QAC7B,IAAI,CAAC,WAAW,GAAG,WAAW,CAAC,WAAW,CAAC;QAC3C,IAAI,CAAC,WAAW,GAAG,CAAA,KAAA,WAAW,CAAC,WAAW,MAAA,QAAA,OAAA,KAAA,IAAA,mPAAI,cAAW,CAAC,OAAO,EAAE,CAAC;QACpE,IAAI,CAAC,kBAAkB,GAAG,yPAAI,qBAAkB,CAAC;YAC/C,IAAI,EAAE,WAAW,CAAC,cAAc;YAChC,IAAI,EAAE,WAAW,CAAC,cAAc;YAChC,IAAI,EAAE,WAAW,CAAC,cAAc;SACjC,CAAC,CAAC;QACH,IAAI,CAAC,aAAa,GAAG,oPAAI,gBAAa,CAAC;YACrC,IAAI,EAAE,WAAW,CAAC,SAAS;YAC3B,OAAO,EAAE,WAAW,CAAC,YAAY;YACjC,SAAS,EAAE,WAAW,CAAC,cAAc;SACtC,CAAC,CAAC;IACL,CAAC;IACH,OAAA,IAAC;AAAD,CAAC,AAtGD,IAsGC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 7035, "column": 0}, "map": {"version": 3, "file": "AggregationTemporality.js", "sourceRoot": "", "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/node_modules/%40opentelemetry/otlp-transformer/node_modules/%40opentelemetry/sdk-metrics/src/export/AggregationTemporality.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n/**\n * AggregationTemporality indicates the way additive quantities are expressed.\n */\nexport enum AggregationTemporality {\n  DELTA,\n  CUMULATIVE,\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG,CAEH;;GAEG;;;AACH,IAAY,sBAGX;AAHD,CAAA,SAAY,sBAAsB;IAChC,sBAAA,CAAA,sBAAA,CAAA,QAAA,GAAA,EAAA,GAAA,OAAK,CAAA;IACL,sBAAA,CAAA,sBAAA,CAAA,aAAA,GAAA,EAAA,GAAA,YAAU,CAAA;AACZ,CAAC,EAHW,sBAAsB,IAAA,CAAtB,sBAAsB,GAAA,CAAA,CAAA,GAGjC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 7065, "column": 0}, "map": {"version": 3, "file": "MetricData.js", "sourceRoot": "", "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/node_modules/%40opentelemetry/otlp-transformer/node_modules/%40opentelemetry/sdk-metrics/src/export/MetricData.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { HrTime, MetricAttributes, ValueType } from '@opentelemetry/api';\nimport { InstrumentationScope } from '@opentelemetry/core';\nimport { IResource } from '@opentelemetry/resources';\nimport { InstrumentType } from '../InstrumentDescriptor';\nimport { AggregationTemporality } from './AggregationTemporality';\nimport { Histogram, ExponentialHistogram } from '../aggregator/types';\n\nexport interface MetricDescriptor {\n  readonly name: string;\n  readonly description: string;\n  readonly unit: string;\n  /**\n   * @deprecated exporter should avoid depending on the type of the instrument\n   * as their resulting aggregator can be re-mapped with views.\n   */\n  readonly type: InstrumentType;\n  readonly valueType: ValueType;\n}\n\n/**\n * Basic metric data fields.\n */\ninterface BaseMetricData {\n  readonly descriptor: MetricDescriptor;\n  readonly aggregationTemporality: AggregationTemporality;\n  /**\n   * DataPointType of the metric instrument.\n   */\n  readonly dataPointType: DataPointType;\n}\n\n/**\n * Represents a metric data aggregated by either a LastValueAggregation or\n * SumAggregation.\n */\nexport interface SumMetricData extends BaseMetricData {\n  readonly dataPointType: DataPointType.SUM;\n  readonly dataPoints: DataPoint<number>[];\n  readonly isMonotonic: boolean;\n}\n\nexport interface GaugeMetricData extends BaseMetricData {\n  readonly dataPointType: DataPointType.GAUGE;\n  readonly dataPoints: DataPoint<number>[];\n}\n\n/**\n * Represents a metric data aggregated by a HistogramAggregation.\n */\nexport interface HistogramMetricData extends BaseMetricData {\n  readonly dataPointType: DataPointType.HISTOGRAM;\n  readonly dataPoints: DataPoint<Histogram>[];\n}\n\n/**\n * Represents a metric data aggregated by a ExponentialHistogramAggregation.\n */\nexport interface ExponentialHistogramMetricData extends BaseMetricData {\n  readonly dataPointType: DataPointType.EXPONENTIAL_HISTOGRAM;\n  readonly dataPoints: DataPoint<ExponentialHistogram>[];\n}\n\n/**\n * Represents an aggregated metric data.\n */\nexport type MetricData =\n  | SumMetricData\n  | GaugeMetricData\n  | HistogramMetricData\n  | ExponentialHistogramMetricData;\n\nexport interface ScopeMetrics {\n  scope: InstrumentationScope;\n  metrics: MetricData[];\n}\n\nexport interface ResourceMetrics {\n  resource: IResource;\n  scopeMetrics: ScopeMetrics[];\n}\n\n/**\n * Represents the collection result of the metrics. If there are any\n * non-critical errors in the collection, like throwing in a single observable\n * callback, these errors are aggregated in the {@link CollectionResult.errors}\n * array and other successfully collected metrics are returned.\n */\nexport interface CollectionResult {\n  /**\n   * Collected metrics.\n   */\n  resourceMetrics: ResourceMetrics;\n  /**\n   * Arbitrary JavaScript exception values.\n   */\n  errors: unknown[];\n}\n\n/**\n * The aggregated point data type.\n */\nexport enum DataPointType {\n  /**\n   * A histogram data point contains a histogram statistics of collected\n   * values with a list of explicit bucket boundaries and statistics such\n   * as min, max, count, and sum of all collected values.\n   */\n  HISTOGRAM,\n  /**\n   * An exponential histogram data point contains a histogram statistics of\n   * collected values where bucket boundaries are automatically calculated\n   * using an exponential function, and statistics such as min, max, count,\n   * and sum of all collected values.\n   */\n  EXPONENTIAL_HISTOGRAM,\n  /**\n   * A gauge metric data point has only a single numeric value.\n   */\n  GAUGE,\n  /**\n   * A sum metric data point has a single numeric value and a\n   * monotonicity-indicator.\n   */\n  SUM,\n}\n\n/**\n * Represents an aggregated point data with start time, end time and their\n * associated attributes and points.\n */\nexport interface DataPoint<T> {\n  /**\n   * The start epoch timestamp of the DataPoint, usually the time when\n   * the metric was created when the preferred AggregationTemporality is\n   * CUMULATIVE, or last collection time otherwise.\n   */\n  readonly startTime: HrTime;\n  /**\n   * The end epoch timestamp when data were collected, usually it represents\n   * the moment when `MetricReader.collect` was called.\n   */\n  readonly endTime: HrTime;\n  /**\n   * The attributes associated with this DataPoint.\n   */\n  readonly attributes: MetricAttributes;\n  /**\n   * The value for this DataPoint. The type of the value is indicated by the\n   * {@link DataPointType}.\n   */\n  readonly value: T;\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG,CAoGH;;GAEG;;;AACH,IAAY,aAuBX;AAvBD,CAAA,SAAY,aAAa;IACvB;;;;OAIG,CACH,aAAA,CAAA,aAAA,CAAA,YAAA,GAAA,EAAA,GAAA,WAAS,CAAA;IACT;;;;;OAKG,CACH,aAAA,CAAA,aAAA,CAAA,wBAAA,GAAA,EAAA,GAAA,uBAAqB,CAAA;IACrB;;OAEG,CACH,aAAA,CAAA,aAAA,CAAA,QAAA,GAAA,EAAA,GAAA,OAAK,CAAA;IACL;;;OAGG,CACH,aAAA,CAAA,aAAA,CAAA,MAAA,GAAA,EAAA,GAAA,KAAG,CAAA;AACL,CAAC,EAvBW,aAAa,IAAA,CAAb,aAAa,GAAA,CAAA,CAAA,GAuBxB", "ignoreList": [0], "debugId": null}}]}