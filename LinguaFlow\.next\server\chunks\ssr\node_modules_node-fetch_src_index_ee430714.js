module.exports = {

"[project]/node_modules/node-fetch/src/index.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/ssr/node_modules_node-fetch_src_utils_multipart-parser_3e916efb.js",
  "server/chunks/ssr/node_modules_0a0b7879._.js",
  "server/chunks/ssr/[root of the server]__43729c7d._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/node-fetch/src/index.js [app-ssr] (ecmascript)");
    });
});
}}),

};