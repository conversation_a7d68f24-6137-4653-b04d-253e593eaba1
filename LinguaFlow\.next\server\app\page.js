const CHUNK_PUBLIC_PATH = "server/app/page.js";
const runtime = require("../chunks/ssr/[turbopack]_runtime.js");
runtime.loadChunk("server/chunks/ssr/node_modules_next_dist_36bb0a82._.js");
runtime.loadChunk("server/chunks/ssr/[root of the server]__b6b8c913._.js");
runtime.loadChunk("server/chunks/ssr/src_app_a4430781._.js");
runtime.loadChunk("server/chunks/ssr/_d33f8db4._.js");
runtime.loadChunk("server/chunks/ssr/node_modules_96715ba7._.js");
runtime.loadChunk("server/chunks/ssr/node_modules_next_dist_client_components_forbidden-error_ea7ea172.js");
runtime.loadChunk("server/chunks/ssr/node_modules_next_dist_client_components_unauthorized-error_c8949b27.js");
runtime.loadChunk("server/chunks/ssr/node_modules_node-fetch_src_index_7445613f.js");
runtime.loadChunk("server/chunks/ssr/node_modules_next_dist_19a4c8c7._.js");
runtime.loadChunk("server/chunks/ssr/node_modules_@genkit-ai_core_lib_7fa917ad._.js");
runtime.loadChunk("server/chunks/ssr/node_modules_zod_lib_9973ecc6._.js");
runtime.loadChunk("server/chunks/ssr/node_modules_ajv_dist_e6327f72._.js");
runtime.loadChunk("server/chunks/ssr/node_modules_zod-to-json-schema_dist_c1dfbad3._.js");
runtime.loadChunk("server/chunks/ssr/node_modules_@opentelemetry_d2960040._.js");
runtime.loadChunk("server/chunks/ssr/node_modules_@opentelemetry_714181f1._.js");
runtime.loadChunk("server/chunks/ssr/node_modules_@opentelemetry_46cc0f05._.js");
runtime.loadChunk("server/chunks/ssr/node_modules_@opentelemetry_b57e52c4._.js");
runtime.loadChunk("server/chunks/ssr/node_modules_@opentelemetry_5ebe4a09._.js");
runtime.loadChunk("server/chunks/ssr/node_modules_protobufjs_c6ba58b0._.js");
runtime.loadChunk("server/chunks/ssr/node_modules_@opentelemetry_otlp-transformer_build_esm_7b4c78bd._.js");
runtime.loadChunk("server/chunks/ssr/node_modules_@grpc_grpc-js_cc2986af._.js");
runtime.loadChunk("server/chunks/ssr/node_modules_handlebars_097a2825._.js");
runtime.loadChunk("server/chunks/ssr/node_modules_yaml_dist_5cbea403._.js");
runtime.loadChunk("server/chunks/ssr/node_modules_@genkit-ai_ai_lib_e3351302._.js");
runtime.loadChunk("server/chunks/ssr/node_modules_@opentelemetry_a366fa1b._.js");
runtime.loadChunk("server/chunks/ssr/node_modules_4e7abed0._.js");
runtime.loadChunk("server/chunks/ssr/[root of the server]__cde5d4e5._.js");
runtime.getOrInstantiateRuntimeModule("[project]/.next-internal/server/app/page/actions.js { ACTIONS_MODULE0 => \"[project]/src/ai/flows/language-detection-flow.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE1 => \"[project]/src/ai/flows/text-analysis-flow.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE2 => \"[project]/src/ai/flows/ai-tone-analysis.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE3 => \"[project]/src/ai/flows/ai-text-generation.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE4 => \"[project]/src/ai/flows/contextual-ai-rephraser.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE5 => \"[project]/src/ai/flows/plagiarism-detection-flow.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE6 => \"[project]/src/ai/flows/ai-writing-detection-flow.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE7 => \"[project]/src/ai/flows/humanize-ai-text-flow.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE8 => \"[project]/src/ai/flows/word-toolkit-flow.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE9 => \"[project]/src/ai/flows/text-to-speech-flow.ts [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript)", CHUNK_PUBLIC_PATH);
runtime.getOrInstantiateRuntimeModule("[project]/node_modules/next/dist/esm/build/templates/app-page.js?page=/page { METADATA_0 => \"[project]/src/app/favicon.ico.mjs { IMAGE => \\\"[project]/src/app/favicon.ico (static in ecmascript)\\\" } [app-rsc] (structured image object, ecmascript, Next.js server component)\", MODULE_1 => \"[project]/src/app/layout.tsx [app-rsc] (ecmascript, Next.js server component)\", MODULE_2 => \"[project]/node_modules/next/dist/client/components/not-found-error.js [app-rsc] (ecmascript, Next.js server component)\", MODULE_3 => \"[project]/node_modules/next/dist/client/components/forbidden-error.js [app-rsc] (ecmascript, Next.js server component)\", MODULE_4 => \"[project]/node_modules/next/dist/client/components/unauthorized-error.js [app-rsc] (ecmascript, Next.js server component)\", MODULE_5 => \"[project]/src/app/page.tsx [app-rsc] (ecmascript, Next.js server component)\" } [app-rsc] (ecmascript)", CHUNK_PUBLIC_PATH);
module.exports = runtime.getOrInstantiateRuntimeModule("[project]/node_modules/next/dist/esm/build/templates/app-page.js?page=/page { METADATA_0 => \"[project]/src/app/favicon.ico.mjs { IMAGE => \\\"[project]/src/app/favicon.ico (static in ecmascript)\\\" } [app-rsc] (structured image object, ecmascript, Next.js server component)\", MODULE_1 => \"[project]/src/app/layout.tsx [app-rsc] (ecmascript, Next.js server component)\", MODULE_2 => \"[project]/node_modules/next/dist/client/components/not-found-error.js [app-rsc] (ecmascript, Next.js server component)\", MODULE_3 => \"[project]/node_modules/next/dist/client/components/forbidden-error.js [app-rsc] (ecmascript, Next.js server component)\", MODULE_4 => \"[project]/node_modules/next/dist/client/components/unauthorized-error.js [app-rsc] (ecmascript, Next.js server component)\", MODULE_5 => \"[project]/src/app/page.tsx [app-rsc] (ecmascript, Next.js server component)\" } [app-rsc] (ecmascript)", CHUNK_PUBLIC_PATH).exports;
