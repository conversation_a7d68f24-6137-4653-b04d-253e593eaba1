{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/node_modules/%40opentelemetry/sdk-logs/src/index.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nexport {\n  LoggerProviderConfig,\n  LogRecordLimits,\n  BufferConfig,\n  BatchLogRecordProcessorBrowserConfig,\n} from './types';\nexport { LoggerProvider } from './LoggerProvider';\nexport { LogRecord } from './LogRecord';\nexport { LogRecordProcessor } from './LogRecordProcessor';\nexport { ReadableLogRecord } from './export/ReadableLogRecord';\nexport { NoopLogRecordProcessor } from './export/NoopLogRecordProcessor';\nexport { ConsoleLogRecordExporter } from './export/ConsoleLogRecordExporter';\nexport { LogRecordExporter } from './export/LogRecordExporter';\nexport { SimpleLogRecordProcessor } from './export/SimpleLogRecordProcessor';\nexport { InMemoryLogRecordExporter } from './export/InMemoryLogRecordExporter';\nexport { BatchLogRecordProcessor } from './platform';\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 44, "column": 0}, "map": {"version": 3, "file": "LogRecord.js", "sourceRoot": "", "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/node_modules/%40opentelemetry/sdk-logs/src/LogRecord.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { AttributeValue, diag } from '@opentelemetry/api';\nimport type * as logsAPI from '@opentelemetry/api-logs';\nimport * as api from '@opentelemetry/api';\nimport {\n  timeInputToHrTime,\n  isAttributeValue,\n  InstrumentationScope,\n} from '@opentelemetry/core';\nimport type { IResource } from '@opentelemetry/resources';\n\nimport type { ReadableLogRecord } from './export/ReadableLogRecord';\nimport type { LogRecordLimits } from './types';\nimport { LogAttributes, LogBody } from '@opentelemetry/api-logs';\nimport { LoggerProviderSharedState } from './internal/LoggerProviderSharedState';\n\nexport class LogRecord implements ReadableLogRecord {\n  readonly hrTime: api.HrTime;\n  readonly hrTimeObserved: api.HrTime;\n  readonly spanContext?: api.SpanContext;\n  readonly resource: IResource;\n  readonly instrumentationScope: InstrumentationScope;\n  readonly attributes: logsAPI.LogAttributes = {};\n  private _severityText?: string;\n  private _severityNumber?: logsAPI.SeverityNumber;\n  private _body?: LogBody;\n  private totalAttributesCount: number = 0;\n\n  private _isReadonly: boolean = false;\n  private readonly _logRecordLimits: Required<LogRecordLimits>;\n\n  set severityText(severityText: string | undefined) {\n    if (this._isLogRecordReadonly()) {\n      return;\n    }\n    this._severityText = severityText;\n  }\n  get severityText(): string | undefined {\n    return this._severityText;\n  }\n\n  set severityNumber(severityNumber: logsAPI.SeverityNumber | undefined) {\n    if (this._isLogRecordReadonly()) {\n      return;\n    }\n    this._severityNumber = severityNumber;\n  }\n  get severityNumber(): logsAPI.SeverityNumber | undefined {\n    return this._severityNumber;\n  }\n\n  set body(body: LogBody | undefined) {\n    if (this._isLogRecordReadonly()) {\n      return;\n    }\n    this._body = body;\n  }\n  get body(): LogBody | undefined {\n    return this._body;\n  }\n\n  get droppedAttributesCount(): number {\n    return this.totalAttributesCount - Object.keys(this.attributes).length;\n  }\n\n  constructor(\n    _sharedState: LoggerProviderSharedState,\n    instrumentationScope: InstrumentationScope,\n    logRecord: logsAPI.LogRecord\n  ) {\n    const {\n      timestamp,\n      observedTimestamp,\n      severityNumber,\n      severityText,\n      body,\n      attributes = {},\n      context,\n    } = logRecord;\n\n    const now = Date.now();\n    this.hrTime = timeInputToHrTime(timestamp ?? now);\n    this.hrTimeObserved = timeInputToHrTime(observedTimestamp ?? now);\n\n    if (context) {\n      const spanContext = api.trace.getSpanContext(context);\n      if (spanContext && api.isSpanContextValid(spanContext)) {\n        this.spanContext = spanContext;\n      }\n    }\n    this.severityNumber = severityNumber;\n    this.severityText = severityText;\n    this.body = body;\n    this.resource = _sharedState.resource;\n    this.instrumentationScope = instrumentationScope;\n    this._logRecordLimits = _sharedState.logRecordLimits;\n    this.setAttributes(attributes);\n  }\n\n  public setAttribute(key: string, value?: LogAttributes | AttributeValue) {\n    if (this._isLogRecordReadonly()) {\n      return this;\n    }\n    if (value === null) {\n      return this;\n    }\n    if (key.length === 0) {\n      api.diag.warn(`Invalid attribute key: ${key}`);\n      return this;\n    }\n    if (\n      !isAttributeValue(value) &&\n      !(\n        typeof value === 'object' &&\n        !Array.isArray(value) &&\n        Object.keys(value).length > 0\n      )\n    ) {\n      api.diag.warn(`Invalid attribute value set for key: ${key}`);\n      return this;\n    }\n    this.totalAttributesCount += 1;\n    if (\n      Object.keys(this.attributes).length >=\n        this._logRecordLimits.attributeCountLimit &&\n      !Object.prototype.hasOwnProperty.call(this.attributes, key)\n    ) {\n      // This logic is to create drop message at most once per LogRecord to prevent excessive logging.\n      if (this.droppedAttributesCount === 1) {\n        api.diag.warn('Dropping extra attributes.');\n      }\n      return this;\n    }\n    if (isAttributeValue(value)) {\n      this.attributes[key] = this._truncateToSize(value);\n    } else {\n      this.attributes[key] = value;\n    }\n    return this;\n  }\n\n  public setAttributes(attributes: LogAttributes) {\n    for (const [k, v] of Object.entries(attributes)) {\n      this.setAttribute(k, v);\n    }\n    return this;\n  }\n\n  public setBody(body: LogBody) {\n    this.body = body;\n    return this;\n  }\n\n  public setSeverityNumber(severityNumber: logsAPI.SeverityNumber) {\n    this.severityNumber = severityNumber;\n    return this;\n  }\n\n  public setSeverityText(severityText: string) {\n    this.severityText = severityText;\n    return this;\n  }\n\n  /**\n   * @internal\n   * A LogRecordProcessor may freely modify logRecord for the duration of the OnEmit call.\n   * If logRecord is needed after OnEmit returns (i.e. for asynchronous processing) only reads are permitted.\n   */\n  _makeReadonly() {\n    this._isReadonly = true;\n  }\n\n  private _truncateToSize(value: AttributeValue): AttributeValue {\n    const limit = this._logRecordLimits.attributeValueLengthLimit;\n    // Check limit\n    if (limit <= 0) {\n      // Negative values are invalid, so do not truncate\n      api.diag.warn(`Attribute value limit must be positive, got ${limit}`);\n      return value;\n    }\n\n    // String\n    if (typeof value === 'string') {\n      return this._truncateToLimitUtil(value, limit);\n    }\n\n    // Array of strings\n    if (Array.isArray(value)) {\n      return (value as []).map(val =>\n        typeof val === 'string' ? this._truncateToLimitUtil(val, limit) : val\n      );\n    }\n\n    // Other types, no need to apply value length limit\n    return value;\n  }\n\n  private _truncateToLimitUtil(value: string, limit: number): string {\n    if (value.length <= limit) {\n      return value;\n    }\n    return value.substring(0, limit);\n  }\n\n  private _isLogRecordReadonly(): boolean {\n    if (this._isReadonly) {\n      diag.warn('Can not execute the operation on emitted log record');\n    }\n    return this._isReadonly;\n  }\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;AAEH,OAAO,EAAkB,IAAI,EAAE,MAAM,oBAAoB,CAAC;;AAE1D,OAAO,KAAK,GAAG,MAAM,oBAAoB,CAAC;;AAC1C,OAAO,EACL,iBAAiB,EACjB,gBAAgB,GAEjB,MAAM,qBAAqB,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQ7B,IAAA,YAAA;IAiDE,SAAA,UACE,YAAuC,EACvC,oBAA0C,EAC1C,SAA4B;QA9CrB,IAAA,CAAA,UAAU,GAA0B,CAAA,CAAE,CAAC;QAIxC,IAAA,CAAA,oBAAoB,GAAW,CAAC,CAAC;QAEjC,IAAA,CAAA,WAAW,GAAY,KAAK,CAAC;QA2CjC,IAAA,SAAS,GAOP,SAAS,CAAA,SAPF,EACT,iBAAiB,GAMf,SAAS,CAAA,iBANM,EACjB,cAAc,GAKZ,SAAS,CAAA,cALG,EACd,YAAY,GAIV,SAAS,CAAA,YAJC,EACZ,IAAI,GAGF,SAAS,CAAA,IAHP,EACJ,KAEE,SAAS,CAAA,UAFI,EAAf,UAAU,GAAA,OAAA,KAAA,IAAG,CAAA,CAAE,GAAA,EAAA,EACf,OAAO,GACL,SAAS,CAAA,OADJ,CACK;QAEd,IAAM,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QACvB,IAAI,CAAC,MAAM,sOAAG,oBAAA,AAAiB,EAAC,SAAS,KAAA,QAAT,SAAS,KAAA,KAAA,IAAT,SAAS,GAAI,GAAG,CAAC,CAAC;QAClD,IAAI,CAAC,cAAc,sOAAG,oBAAA,AAAiB,EAAC,iBAAiB,KAAA,QAAjB,iBAAiB,KAAA,KAAA,IAAjB,iBAAiB,GAAI,GAAG,CAAC,CAAC;QAElE,IAAI,OAAO,EAAE;YACX,IAAM,WAAW,6KAAG,GAAG,CAAC,IAAK,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC;YACtD,IAAI,WAAW,mMAAI,GAAG,CAAC,iBAAA,AAAkB,EAAC,WAAW,CAAC,EAAE;gBACtD,IAAI,CAAC,WAAW,GAAG,WAAW,CAAC;aAChC;SACF;QACD,IAAI,CAAC,cAAc,GAAG,cAAc,CAAC;QACrC,IAAI,CAAC,YAAY,GAAG,YAAY,CAAC;QACjC,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;QACjB,IAAI,CAAC,QAAQ,GAAG,YAAY,CAAC,QAAQ,CAAC;QACtC,IAAI,CAAC,oBAAoB,GAAG,oBAAoB,CAAC;QACjD,IAAI,CAAC,gBAAgB,GAAG,YAAY,CAAC,eAAe,CAAC;QACrD,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC,CAAC;IACjC,CAAC;IAlED,OAAA,cAAA,CAAI,UAAA,SAAA,EAAA,cAAY,EAAA;aAMhB;YACE,OAAO,IAAI,CAAC,aAAa,CAAC;QAC5B,CAAC;aARD,SAAiB,YAAgC;YAC/C,IAAI,IAAI,CAAC,oBAAoB,EAAE,EAAE;gBAC/B,OAAO;aACR;YACD,IAAI,CAAC,aAAa,GAAG,YAAY,CAAC;QACpC,CAAC;;;OAAA;IAKD,OAAA,cAAA,CAAI,UAAA,SAAA,EAAA,gBAAc,EAAA;aAMlB;YACE,OAAO,IAAI,CAAC,eAAe,CAAC;QAC9B,CAAC;aARD,SAAmB,cAAkD;YACnE,IAAI,IAAI,CAAC,oBAAoB,EAAE,EAAE;gBAC/B,OAAO;aACR;YACD,IAAI,CAAC,eAAe,GAAG,cAAc,CAAC;QACxC,CAAC;;;OAAA;IAKD,OAAA,cAAA,CAAI,UAAA,SAAA,EAAA,MAAI,EAAA;aAMR;YACE,OAAO,IAAI,CAAC,KAAK,CAAC;QACpB,CAAC;aARD,SAAS,IAAyB;YAChC,IAAI,IAAI,CAAC,oBAAoB,EAAE,EAAE;gBAC/B,OAAO;aACR;YACD,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;QACpB,CAAC;;;OAAA;IAKD,OAAA,cAAA,CAAI,UAAA,SAAA,EAAA,wBAAsB,EAAA;aAA1B;YACE,OAAO,IAAI,CAAC,oBAAoB,GAAG,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,MAAM,CAAC;QACzE,CAAC;;;OAAA;IAoCM,UAAA,SAAA,CAAA,YAAY,GAAnB,SAAoB,GAAW,EAAE,KAAsC;QACrE,IAAI,IAAI,CAAC,oBAAoB,EAAE,EAAE;YAC/B,OAAO,IAAI,CAAC;SACb;QACD,IAAI,KAAK,KAAK,IAAI,EAAE;YAClB,OAAO,IAAI,CAAC;SACb;QACD,IAAI,GAAG,CAAC,MAAM,KAAK,CAAC,EAAE;qLACpB,GAAG,CAAC,GAAI,CAAC,IAAI,CAAC,4BAA0B,GAAK,CAAC,CAAC;YAC/C,OAAO,IAAI,CAAC;SACb;QACD,IACE,EAAC,2PAAA,AAAgB,EAAC,KAAK,CAAC,IACxB,CAAC,CACC,OAAO,KAAK,KAAK,QAAQ,IACzB,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,IACrB,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,MAAM,GAAG,CAAC,CAC9B,EACD;qLACA,GAAG,CAAC,GAAI,CAAC,IAAI,CAAC,0CAAwC,GAAK,CAAC,CAAC;YAC7D,OAAO,IAAI,CAAC;SACb;QACD,IAAI,CAAC,oBAAoB,IAAI,CAAC,CAAC;QAC/B,IACE,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,MAAM,IACjC,IAAI,CAAC,gBAAgB,CAAC,mBAAmB,IAC3C,CAAC,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,GAAG,CAAC,EAC3D;YACA,gGAAgG;YAChG,IAAI,IAAI,CAAC,sBAAsB,KAAK,CAAC,EAAE;yLACrC,GAAG,CAAC,GAAI,CAAC,IAAI,CAAC,4BAA4B,CAAC,CAAC;aAC7C;YACD,OAAO,IAAI,CAAC;SACb;QACD,6OAAI,mBAAA,AAAgB,EAAC,KAAK,CAAC,EAAE;YAC3B,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC;SACpD,MAAM;YACL,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC;SAC9B;QACD,OAAO,IAAI,CAAC;IACd,CAAC;IAEM,UAAA,SAAA,CAAA,aAAa,GAApB,SAAqB,UAAyB;;;YAC5C,IAAqB,IAAA,KAAA,SAAA,MAAM,CAAC,OAAO,CAAC,UAAU,CAAC,CAAA,EAAA,KAAA,GAAA,IAAA,EAAA,EAAA,CAAA,GAAA,IAAA,EAAA,KAAA,GAAA,IAAA,GAAE;gBAAtC,IAAA,KAAA,OAAA,GAAA,KAAA,EAAA,EAAM,EAAL,CAAC,GAAA,EAAA,CAAA,EAAA,EAAE,CAAC,GAAA,EAAA,CAAA,EAAA;gBACd,IAAI,CAAC,YAAY,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;aACzB;;;;;;;;;;;;QACD,OAAO,IAAI,CAAC;IACd,CAAC;IAEM,UAAA,SAAA,CAAA,OAAO,GAAd,SAAe,IAAa;QAC1B,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;QACjB,OAAO,IAAI,CAAC;IACd,CAAC;IAEM,UAAA,SAAA,CAAA,iBAAiB,GAAxB,SAAyB,cAAsC;QAC7D,IAAI,CAAC,cAAc,GAAG,cAAc,CAAC;QACrC,OAAO,IAAI,CAAC;IACd,CAAC;IAEM,UAAA,SAAA,CAAA,eAAe,GAAtB,SAAuB,YAAoB;QACzC,IAAI,CAAC,YAAY,GAAG,YAAY,CAAC;QACjC,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;;;OAIG,CACH,UAAA,SAAA,CAAA,aAAa,GAAb;QACE,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;IAC1B,CAAC;IAEO,UAAA,SAAA,CAAA,eAAe,GAAvB,SAAwB,KAAqB;QAA7C,IAAA,QAAA,IAAA,CAuBC;QAtBC,IAAM,KAAK,GAAG,IAAI,CAAC,gBAAgB,CAAC,yBAAyB,CAAC;QAC9D,cAAc;QACd,IAAI,KAAK,IAAI,CAAC,EAAE;YACd,kDAAkD;oLAClD,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,iDAA+C,KAAO,CAAC,CAAC;YACtE,OAAO,KAAK,CAAC;SACd;QAED,SAAS;QACT,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE;YAC7B,OAAO,IAAI,CAAC,oBAAoB,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;SAChD;QAED,mBAAmB;QACnB,IAAI,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;YACxB,OAAQ,KAAY,CAAC,GAAG,CAAC,SAAA,GAAG;gBAC1B,OAAA,OAAO,GAAG,KAAK,QAAQ,CAAC,CAAC,CAAC,KAAI,CAAC,oBAAoB,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG;YAArE,CAAqE,CACtE,CAAC;SACH;QAED,mDAAmD;QACnD,OAAO,KAAK,CAAC;IACf,CAAC;IAEO,UAAA,SAAA,CAAA,oBAAoB,GAA5B,SAA6B,KAAa,EAAE,KAAa;QACvD,IAAI,KAAK,CAAC,MAAM,IAAI,KAAK,EAAE;YACzB,OAAO,KAAK,CAAC;SACd;QACD,OAAO,KAAK,CAAC,SAAS,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC;IACnC,CAAC;IAEO,UAAA,SAAA,CAAA,oBAAoB,GAA5B;QACE,IAAI,IAAI,CAAC,WAAW,EAAE;qLACpB,OAAI,CAAC,IAAI,CAAC,qDAAqD,CAAC,CAAC;SAClE;QACD,OAAO,IAAI,CAAC,WAAW,CAAC;IAC1B,CAAC;IACH,OAAA,SAAC;AAAD,CAAC,AAlMD,IAkMC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 284, "column": 0}, "map": {"version": 3, "file": "Logger.js", "sourceRoot": "", "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/node_modules/%40opentelemetry/sdk-logs/src/Logger.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport type * as logsAPI from '@opentelemetry/api-logs';\nimport type { InstrumentationScope } from '@opentelemetry/core';\nimport { context } from '@opentelemetry/api';\n\nimport { LogRecord } from './LogRecord';\nimport { LoggerProviderSharedState } from './internal/LoggerProviderSharedState';\n\nexport class Logger implements logsAPI.Logger {\n  constructor(\n    public readonly instrumentationScope: InstrumentationScope,\n    private _sharedState: LoggerProviderSharedState\n  ) {}\n\n  public emit(logRecord: logsAPI.LogRecord): void {\n    const currentContext = logRecord.context || context.active();\n    /**\n     * If a Logger was obtained with include_trace_context=true,\n     * the LogRecords it emits MUST automatically include the Trace Context from the active Context,\n     * if Context has not been explicitly set.\n     */\n    const logRecordInstance = new LogRecord(\n      this._sharedState,\n      this.instrumentationScope,\n      {\n        context: currentContext,\n        ...logRecord,\n      }\n    );\n    /**\n     * the explicitly passed Context,\n     * the current Context, or an empty Context if the Logger was obtained with include_trace_context=false\n     */\n    this._sharedState.activeProcessor.onEmit(logRecordInstance, currentContext);\n    /**\n     * A LogRecordProcessor may freely modify logRecord for the duration of the OnEmit call.\n     * If logRecord is needed after OnEmit returns (i.e. for asynchronous processing) only reads are permitted.\n     */\n    logRecordInstance._makeReadonly();\n  }\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;AAIH,OAAO,EAAE,OAAO,EAAE,MAAM,oBAAoB,CAAC;AAE7C,OAAO,EAAE,SAAS,EAAE,MAAM,aAAa,CAAC;;;;;;;;;;;;;AAGxC,IAAA,SAAA;IACE,SAAA,OACkB,oBAA0C,EAClD,YAAuC;QAD/B,IAAA,CAAA,oBAAoB,GAApB,oBAAoB,CAAsB;QAClD,IAAA,CAAA,YAAY,GAAZ,YAAY,CAA2B;IAC9C,CAAC;IAEG,OAAA,SAAA,CAAA,IAAI,GAAX,SAAY,SAA4B;QACtC,IAAM,cAAc,GAAG,SAAS,CAAC,OAAO,+KAAI,WAAO,CAAC,MAAM,EAAE,CAAC;QAC7D;;;;WAIG,CACH,IAAM,iBAAiB,GAAG,mLAAI,YAAS,CACrC,IAAI,CAAC,YAAY,EACjB,IAAI,CAAC,oBAAoB,EAAA,SAAA;YAEvB,OAAO,EAAE,cAAc;QAAA,GACpB,SAAS,EAEf,CAAC;QACF;;;WAGG,CACH,IAAI,CAAC,YAAY,CAAC,eAAe,CAAC,MAAM,CAAC,iBAAiB,EAAE,cAAc,CAAC,CAAC;QAC5E;;;WAGG,CACH,iBAAiB,CAAC,aAAa,EAAE,CAAC;IACpC,CAAC;IACH,OAAA,MAAC;AAAD,CAAC,AAhCD,IAgCC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 348, "column": 0}, "map": {"version": 3, "file": "config.js", "sourceRoot": "", "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/node_modules/%40opentelemetry/sdk-logs/src/config.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  DEFAULT_ATTRIBUTE_COUNT_LIMIT,\n  DEFAULT_ATTRIBUTE_VALUE_LENGTH_LIMIT,\n  getEnv,\n  getEnvWithoutDefaults,\n} from '@opentelemetry/core';\nimport { LogRecordLimits } from './types';\n\nexport function loadDefaultConfig() {\n  return {\n    forceFlushTimeoutMillis: 30000,\n    logRecordLimits: {\n      attributeValueLengthLimit:\n        getEnv().OTEL_LOGRECORD_ATTRIBUTE_VALUE_LENGTH_LIMIT,\n      attributeCountLimit: getEnv().OTEL_LOGRECORD_ATTRIBUTE_COUNT_LIMIT,\n    },\n    includeTraceContext: true,\n  };\n}\n\n/**\n * When general limits are provided and model specific limits are not,\n * configures the model specific limits by using the values from the general ones.\n * @param logRecordLimits User provided limits configuration\n */\nexport function reconfigureLimits(\n  logRecordLimits: LogRecordLimits\n): Required<LogRecordLimits> {\n  const parsedEnvConfig = getEnvWithoutDefaults();\n\n  return {\n    /**\n     * Reassign log record attribute count limit to use first non null value defined by user or use default value\n     */\n    attributeCountLimit:\n      logRecordLimits.attributeCountLimit ??\n      parsedEnvConfig.OTEL_LOGRECORD_ATTRIBUTE_COUNT_LIMIT ??\n      parsedEnvConfig.OTEL_ATTRIBUTE_COUNT_LIMIT ??\n      DEFAULT_ATTRIBUTE_COUNT_LIMIT,\n    /**\n     * Reassign log record attribute value length limit to use first non null value defined by user or use default value\n     */\n    attributeValueLengthLimit:\n      logRecordLimits.attributeValueLengthLimit ??\n      parsedEnvConfig.OTEL_LOGRECORD_ATTRIBUTE_VALUE_LENGTH_LIMIT ??\n      parsedEnvConfig.OTEL_ATTRIBUTE_VALUE_LENGTH_LIMIT ??\n      DEFAULT_ATTRIBUTE_VALUE_LENGTH_LIMIT,\n  };\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;;AAEH,OAAO,EACL,6BAA6B,EAC7B,oCAAoC,EACpC,MAAM,EACN,qBAAqB,GACtB,MAAM,qBAAqB,CAAC;;;AAGvB,SAAU,iBAAiB;IAC/B,OAAO;QACL,uBAAuB,EAAE,KAAK;QAC9B,eAAe,EAAE;YACf,yBAAyB,yPACvB,SAAA,AAAM,EAAE,EAAC,2CAA2C;YACtD,mBAAmB,GAAE,+PAAA,AAAM,EAAE,EAAC,oCAAoC;SACnE;QACD,mBAAmB,EAAE,IAAI;KAC1B,CAAC;AACJ,CAAC;AAOK,SAAU,iBAAiB,CAC/B,eAAgC;;IAEhC,IAAM,eAAe,0PAAG,wBAAA,AAAqB,EAAE,CAAC;IAEhD,OAAO;QACL;;WAEG,CACH,mBAAmB,EACjB,CAAA,KAAA,CAAA,KAAA,CAAA,KAAA,eAAe,CAAC,mBAAmB,MAAA,QAAA,OAAA,KAAA,IAAA,KACnC,eAAe,CAAC,oCAAoC,MAAA,QAAA,OAAA,KAAA,IAAA,KACpD,eAAe,CAAC,0BAA0B,MAAA,QAAA,OAAA,KAAA,IAAA,0OAC1C,gCAA6B;QAC/B;;WAEG,CACH,yBAAyB,EACvB,CAAA,KAAA,CAAA,KAAA,CAAA,KAAA,eAAe,CAAC,yBAAyB,MAAA,QAAA,OAAA,KAAA,IAAA,KACzC,eAAe,CAAC,2CAA2C,MAAA,QAAA,OAAA,KAAA,IAAA,KAC3D,eAAe,CAAC,iCAAiC,MAAA,QAAA,OAAA,KAAA,IAAA,0OACjD,uCAAoC;KACvC,CAAC;AACJ,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 397, "column": 0}, "map": {"version": 3, "file": "MultiLogRecordProcessor.js", "sourceRoot": "", "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/node_modules/%40opentelemetry/sdk-logs/src/MultiLogRecordProcessor.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { callWithTimeout } from '@opentelemetry/core';\nimport type { Context } from '@opentelemetry/api';\nimport type { LogRecordProcessor } from './LogRecordProcessor';\nimport type { LogRecord } from './LogRecord';\n\n/**\n * Implementation of the {@link LogRecordProcessor} that simply forwards all\n * received events to a list of {@link LogRecordProcessor}s.\n */\nexport class MultiLogRecordProcessor implements LogRecordProcessor {\n  constructor(\n    public readonly processors: LogRecordProcessor[],\n    public readonly forceFlushTimeoutMillis: number\n  ) {}\n\n  public async forceFlush(): Promise<void> {\n    const timeout = this.forceFlushTimeoutMillis;\n    await Promise.all(\n      this.processors.map(processor =>\n        callWithTimeout(processor.forceFlush(), timeout)\n      )\n    );\n  }\n\n  public onEmit(logRecord: LogRecord, context?: Context): void {\n    this.processors.forEach(processors =>\n      processors.onEmit(logRecord, context)\n    );\n  }\n\n  public async shutdown(): Promise<void> {\n    await Promise.all(this.processors.map(processor => processor.shutdown()));\n  }\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;AAEH,OAAO,EAAE,eAAe,EAAE,MAAM,qBAAqB,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAKtD;;;GAGG,CACH,IAAA,0BAAA;IACE,SAAA,wBACkB,UAAgC,EAChC,uBAA+B;QAD/B,IAAA,CAAA,UAAU,GAAV,UAAU,CAAsB;QAChC,IAAA,CAAA,uBAAuB,GAAvB,uBAAuB,CAAQ;IAC9C,CAAC;IAES,wBAAA,SAAA,CAAA,UAAU,GAAvB;;;;;;wBACQ,OAAO,GAAG,IAAI,CAAC,uBAAuB,CAAC;wBAC7C,OAAA;4BAAA,EAAA,OAAA;4BAAM,OAAO,CAAC,GAAG,CACf,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,SAAA,SAAS;gCAC3B,WAAA,mPAAA,AAAe,EAAC,SAAS,CAAC,UAAU,EAAE,EAAE,OAAO,CAAC;4BAAhD,CAAgD,CACjD,CACF;yBAAA,CAAA;;wBAJD,GAAA,IAAA,EAIC,CAAC;;;;;;;KACH;IAEM,wBAAA,SAAA,CAAA,MAAM,GAAb,SAAc,SAAoB,EAAE,OAAiB;QACnD,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,SAAA,UAAU;YAChC,OAAA,UAAU,CAAC,MAAM,CAAC,SAAS,EAAE,OAAO,CAAC;QAArC,CAAqC,CACtC,CAAC;IACJ,CAAC;IAEY,wBAAA,SAAA,CAAA,QAAQ,GAArB;;;;;wBACE,OAAA;4BAAA,EAAA,OAAA;4BAAM,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,SAAA,SAAS;gCAAI,OAAA,SAAS,CAAC,QAAQ,EAAE;4BAApB,CAAoB,CAAC,CAAC;yBAAA,CAAA;;wBAAzE,GAAA,IAAA,EAAyE,CAAC;;;;;;;KAC3E;IACH,OAAA,uBAAC;AAAD,CAAC,AAxBD,IAwBC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 604, "column": 0}, "map": {"version": 3, "file": "NoopLogRecordProcessor.js", "sourceRoot": "", "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/node_modules/%40opentelemetry/sdk-logs/src/export/NoopLogRecordProcessor.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { Context } from '@opentelemetry/api';\nimport { LogRecordProcessor } from '../LogRecordProcessor';\nimport { ReadableLogRecord } from './ReadableLogRecord';\n\nexport class NoopLogRecordProcessor implements LogRecordProcessor {\n  forceFlush(): Promise<void> {\n    return Promise.resolve();\n  }\n\n  onEmit(_logRecord: ReadableLogRecord, _context: Context): void {}\n\n  shutdown(): Promise<void> {\n    return Promise.resolve();\n  }\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;AAMH,IAAA,yBAAA;IAAA,SAAA,0BAUA,CAAC;IATC,uBAAA,SAAA,CAAA,UAAU,GAAV;QACE,OAAO,OAAO,CAAC,OAAO,EAAE,CAAC;IAC3B,CAAC;IAED,uBAAA,SAAA,CAAA,MAAM,GAAN,SAAO,UAA6B,EAAE,QAAiB,GAAS,CAAC;IAEjE,uBAAA,SAAA,CAAA,QAAQ,GAAR;QACE,OAAO,OAAO,CAAC,OAAO,EAAE,CAAC;IAC3B,CAAC;IACH,OAAA,sBAAC;AAAD,CAAC,AAVD,IAUC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 640, "column": 0}, "map": {"version": 3, "file": "LoggerProviderSharedState.js", "sourceRoot": "", "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/node_modules/%40opentelemetry/sdk-logs/src/internal/LoggerProviderSharedState.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { Logger } from '@opentelemetry/api-logs';\nimport { IResource } from '@opentelemetry/resources';\nimport { LogRecordProcessor } from '../LogRecordProcessor';\nimport { LogRecordLimits } from '../types';\nimport { NoopLogRecordProcessor } from '../export/NoopLogRecordProcessor';\n\nexport class LoggerProviderSharedState {\n  readonly loggers: Map<string, Logger> = new Map();\n  activeProcessor: LogRecordProcessor;\n  readonly registeredLogRecordProcessors: LogRecordProcessor[] = [];\n\n  constructor(\n    readonly resource: IResource,\n    readonly forceFlushTimeoutMillis: number,\n    readonly logRecordLimits: Required<LogRecordLimits>\n  ) {\n    this.activeProcessor = new NoopLogRecordProcessor();\n  }\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;AAMH,OAAO,EAAE,sBAAsB,EAAE,MAAM,kCAAkC,CAAC;;AAE1E,IAAA,4BAAA;IAKE,SAAA,0BACW,QAAmB,EACnB,uBAA+B,EAC/B,eAA0C;QAF1C,IAAA,CAAA,QAAQ,GAAR,QAAQ,CAAW;QACnB,IAAA,CAAA,uBAAuB,GAAvB,uBAAuB,CAAQ;QAC/B,IAAA,CAAA,eAAe,GAAf,eAAe,CAA2B;QAP5C,IAAA,CAAA,OAAO,GAAwB,IAAI,GAAG,EAAE,CAAC;QAEzC,IAAA,CAAA,6BAA6B,GAAyB,EAAE,CAAC;QAOhE,IAAI,CAAC,eAAe,GAAG,0MAAI,yBAAsB,EAAE,CAAC;IACtD,CAAC;IACH,OAAA,yBAAC;AAAD,CAAC,AAZD,IAYC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 678, "column": 0}, "map": {"version": 3, "file": "LoggerProvider.js", "sourceRoot": "", "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/node_modules/%40opentelemetry/sdk-logs/src/LoggerProvider.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nimport { diag } from '@opentelemetry/api';\nimport type * as logsAPI from '@opentelemetry/api-logs';\nimport { NOOP_LOGGER } from '@opentelemetry/api-logs';\nimport { Resource } from '@opentelemetry/resources';\nimport { BindOnceFuture, merge } from '@opentelemetry/core';\n\nimport type { LoggerProviderConfig } from './types';\nimport type { LogRecordProcessor } from './LogRecordProcessor';\nimport { Logger } from './Logger';\nimport { loadDefaultConfig, reconfigureLimits } from './config';\nimport { MultiLogRecordProcessor } from './MultiLogRecordProcessor';\nimport { LoggerProviderSharedState } from './internal/LoggerProviderSharedState';\n\nexport const DEFAULT_LOGGER_NAME = 'unknown';\n\nexport class LoggerProvider implements logsAPI.LoggerProvider {\n  private _shutdownOnce: BindOnceFuture<void>;\n  private readonly _sharedState: LoggerProviderSharedState;\n\n  constructor(config: LoggerProviderConfig = {}) {\n    const mergedConfig = merge({}, loadDefaultConfig(), config);\n    const resource = Resource.default().merge(\n      mergedConfig.resource ?? Resource.empty()\n    );\n    this._sharedState = new LoggerProviderSharedState(\n      resource,\n      mergedConfig.forceFlushTimeoutMillis,\n      reconfigureLimits(mergedConfig.logRecordLimits)\n    );\n    this._shutdownOnce = new BindOnceFuture(this._shutdown, this);\n  }\n\n  /**\n   * Get a logger with the configuration of the LoggerProvider.\n   */\n  public getLogger(\n    name: string,\n    version?: string,\n    options?: logsAPI.LoggerOptions\n  ): logsAPI.Logger {\n    if (this._shutdownOnce.isCalled) {\n      diag.warn('A shutdown LoggerProvider cannot provide a Logger');\n      return NOOP_LOGGER;\n    }\n\n    if (!name) {\n      diag.warn('Logger requested without instrumentation scope name.');\n    }\n    const loggerName = name || DEFAULT_LOGGER_NAME;\n    const key = `${loggerName}@${version || ''}:${options?.schemaUrl || ''}`;\n    if (!this._sharedState.loggers.has(key)) {\n      this._sharedState.loggers.set(\n        key,\n        new Logger(\n          { name: loggerName, version, schemaUrl: options?.schemaUrl },\n          this._sharedState\n        )\n      );\n    }\n    // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n    return this._sharedState.loggers.get(key)!;\n  }\n\n  /**\n   * Adds a new {@link LogRecordProcessor} to this logger.\n   * @param processor the new LogRecordProcessor to be added.\n   */\n  public addLogRecordProcessor(processor: LogRecordProcessor) {\n    if (this._sharedState.registeredLogRecordProcessors.length === 0) {\n      // since we might have enabled by default a batchProcessor, we disable it\n      // before adding the new one\n      this._sharedState.activeProcessor\n        .shutdown()\n        .catch(err =>\n          diag.error(\n            'Error while trying to shutdown current log record processor',\n            err\n          )\n        );\n    }\n    this._sharedState.registeredLogRecordProcessors.push(processor);\n    this._sharedState.activeProcessor = new MultiLogRecordProcessor(\n      this._sharedState.registeredLogRecordProcessors,\n      this._sharedState.forceFlushTimeoutMillis\n    );\n  }\n\n  /**\n   * Notifies all registered LogRecordProcessor to flush any buffered data.\n   *\n   * Returns a promise which is resolved when all flushes are complete.\n   */\n  public forceFlush(): Promise<void> {\n    // do not flush after shutdown\n    if (this._shutdownOnce.isCalled) {\n      diag.warn('invalid attempt to force flush after LoggerProvider shutdown');\n      return this._shutdownOnce.promise;\n    }\n    return this._sharedState.activeProcessor.forceFlush();\n  }\n\n  /**\n   * Flush all buffered data and shut down the LoggerProvider and all registered\n   * LogRecordProcessor.\n   *\n   * Returns a promise which is resolved when all flushes are complete.\n   */\n  public shutdown(): Promise<void> {\n    if (this._shutdownOnce.isCalled) {\n      diag.warn('shutdown may only be called once per LoggerProvider');\n      return this._shutdownOnce.promise;\n    }\n    return this._shutdownOnce.call();\n  }\n\n  private _shutdown(): Promise<void> {\n    return this._sharedState.activeProcessor.shutdown();\n  }\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;;AACH,OAAO,EAAE,IAAI,EAAE,MAAM,oBAAoB,CAAC;AAE1C,OAAO,EAAE,WAAW,EAAE,MAAM,yBAAyB,CAAC;AACtD,OAAO,EAAE,QAAQ,EAAE,MAAM,0BAA0B,CAAC;;AACpD,OAAO,EAAE,cAAc,EAAE,KAAK,EAAE,MAAM,qBAAqB,CAAC;AAI5D,OAAO,EAAE,MAAM,EAAE,MAAM,UAAU,CAAC;AAClC,OAAO,EAAE,iBAAiB,EAAE,iBAAiB,EAAE,MAAM,UAAU,CAAC;AAChE,OAAO,EAAE,uBAAuB,EAAE,MAAM,2BAA2B,CAAC;AACpE,OAAO,EAAE,yBAAyB,EAAE,MAAM,sCAAsC,CAAC;;;;;;;;;AAE1E,IAAM,mBAAmB,GAAG,SAAS,CAAC;AAE7C,IAAA,iBAAA;IAIE,SAAA,eAAY,MAAiC;QAAjC,IAAA,WAAA,KAAA,GAAA;YAAA,SAAA,CAAA,CAAiC;QAAA;;QAC3C,IAAM,YAAY,sOAAG,QAAA,AAAK,EAAC,CAAA,CAAE,EAAE,oMAAA,AAAiB,EAAE,GAAE,MAAM,CAAC,CAAC;QAC5D,IAAM,QAAQ,iOAAG,WAAQ,CAAC,OAAO,EAAE,CAAC,KAAK,CACvC,CAAA,KAAA,YAAY,CAAC,QAAQ,MAAA,QAAA,OAAA,KAAA,IAAA,mOAAI,WAAQ,CAAC,KAAK,EAAE,CAC1C,CAAC;QACF,IAAI,CAAC,YAAY,GAAG,+MAAI,4BAAyB,CAC/C,QAAQ,EACR,YAAY,CAAC,uBAAuB,kLACpC,oBAAA,AAAiB,EAAC,YAAY,CAAC,eAAe,CAAC,CAChD,CAAC;QACF,IAAI,CAAC,aAAa,GAAG,sOAAI,iBAAc,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,CAAC;IAChE,CAAC;IAED;;OAEG,CACI,eAAA,SAAA,CAAA,SAAS,GAAhB,SACE,IAAY,EACZ,OAAgB,EAChB,OAA+B;QAE/B,IAAI,IAAI,CAAC,aAAa,CAAC,QAAQ,EAAE;qLAC/B,OAAI,CAAC,IAAI,CAAC,mDAAmD,CAAC,CAAC;YAC/D,uLAAO,cAAW,CAAC;SACpB;QAED,IAAI,CAAC,IAAI,EAAE;qLACT,OAAI,CAAC,IAAI,CAAC,sDAAsD,CAAC,CAAC;SACnE;QACD,IAAM,UAAU,GAAG,IAAI,IAAI,mBAAmB,CAAC;QAC/C,IAAM,GAAG,GAAM,UAAU,GAAA,MAAA,CAAI,OAAO,IAAI,EAAE,IAAA,MAAA,CAAI,CAAA,OAAO,KAAA,QAAP,OAAO,KAAA,KAAA,IAAA,KAAA,IAAP,OAAO,CAAE,SAAS,KAAI,EAAE,CAAE,CAAC;QACzE,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE;YACvC,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,GAAG,CAC3B,GAAG,EACH,gLAAI,SAAM,CACR;gBAAE,IAAI,EAAE,UAAU;gBAAE,OAAO,EAAA,OAAA;gBAAE,SAAS,EAAE,OAAO,KAAA,QAAP,OAAO,KAAA,KAAA,IAAA,KAAA,IAAP,OAAO,CAAE,SAAS;YAAA,CAAE,EAC5D,IAAI,CAAC,YAAY,CAClB,CACF,CAAC;SACH;QACD,oEAAoE;QACpE,OAAO,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,GAAG,CAAC,GAAG,CAAE,CAAC;IAC7C,CAAC;IAED;;;OAGG,CACI,eAAA,SAAA,CAAA,qBAAqB,GAA5B,SAA6B,SAA6B;QACxD,IAAI,IAAI,CAAC,YAAY,CAAC,6BAA6B,CAAC,MAAM,KAAK,CAAC,EAAE;YAChE,yEAAyE;YACzE,4BAA4B;YAC5B,IAAI,CAAC,YAAY,CAAC,eAAe,CAC9B,QAAQ,EAAE,CACV,KAAK,CAAC,SAAA,GAAG;gBACR,gLAAA,OAAI,CAAC,KAAK,CACR,6DAA6D,EAC7D,GAAG,CACJ;YAHD,CAGC,CACF,CAAC;SACL;QACD,IAAI,CAAC,YAAY,CAAC,6BAA6B,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QAChE,IAAI,CAAC,YAAY,CAAC,eAAe,GAAG,iMAAI,0BAAuB,CAC7D,IAAI,CAAC,YAAY,CAAC,6BAA6B,EAC/C,IAAI,CAAC,YAAY,CAAC,uBAAuB,CAC1C,CAAC;IACJ,CAAC;IAED;;;;OAIG,CACI,eAAA,SAAA,CAAA,UAAU,GAAjB;QACE,8BAA8B;QAC9B,IAAI,IAAI,CAAC,aAAa,CAAC,QAAQ,EAAE;YAC/B,gLAAI,CAAC,IAAI,CAAC,8DAA8D,CAAC,CAAC;YAC1E,OAAO,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC;SACnC;QACD,OAAO,IAAI,CAAC,YAAY,CAAC,eAAe,CAAC,UAAU,EAAE,CAAC;IACxD,CAAC;IAED;;;;;OAKG,CACI,eAAA,SAAA,CAAA,QAAQ,GAAf;QACE,IAAI,IAAI,CAAC,aAAa,CAAC,QAAQ,EAAE;oLAC/B,QAAI,CAAC,IAAI,CAAC,qDAAqD,CAAC,CAAC;YACjE,OAAO,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC;SACnC;QACD,OAAO,IAAI,CAAC,aAAa,CAAC,IAAI,EAAE,CAAC;IACnC,CAAC;IAEO,eAAA,SAAA,CAAA,SAAS,GAAjB;QACE,OAAO,IAAI,CAAC,YAAY,CAAC,eAAe,CAAC,QAAQ,EAAE,CAAC;IACtD,CAAC;IACH,OAAA,cAAC;AAAD,CAAC,AAvGD,IAuGC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 798, "column": 0}, "map": {"version": 3, "file": "ConsoleLogRecordExporter.js", "sourceRoot": "", "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/node_modules/%40opentelemetry/sdk-logs/src/export/ConsoleLogRecordExporter.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { ExportResult, hrTimeToMicroseconds } from '@opentelemetry/core';\nimport { ExportResultCode } from '@opentelemetry/core';\n\nimport type { ReadableLogRecord } from './ReadableLogRecord';\nimport type { LogRecordExporter } from './LogRecordExporter';\n\n/**\n * This is implementation of {@link LogRecordExporter} that prints LogRecords to the\n * console. This class can be used for diagnostic purposes.\n */\n\n/* eslint-disable no-console */\nexport class ConsoleLogRecordExporter implements LogRecordExporter {\n  /**\n   * Export logs.\n   * @param logs\n   * @param resultCallback\n   */\n  public export(\n    logs: ReadableLogRecord[],\n    resultCallback: (result: ExportResult) => void\n  ) {\n    this._sendLogRecords(logs, resultCallback);\n  }\n\n  /**\n   * Shutdown the exporter.\n   */\n  public shutdown(): Promise<void> {\n    return Promise.resolve();\n  }\n\n  /**\n   * converts logRecord info into more readable format\n   * @param logRecord\n   */\n  private _exportInfo(logRecord: ReadableLogRecord) {\n    return {\n      resource: {\n        attributes: logRecord.resource.attributes,\n      },\n      timestamp: hrTimeToMicroseconds(logRecord.hrTime),\n      traceId: logRecord.spanContext?.traceId,\n      spanId: logRecord.spanContext?.spanId,\n      traceFlags: logRecord.spanContext?.traceFlags,\n      severityText: logRecord.severityText,\n      severityNumber: logRecord.severityNumber,\n      body: logRecord.body,\n      attributes: logRecord.attributes,\n    };\n  }\n\n  /**\n   * Showing logs  in console\n   * @param logRecords\n   * @param done\n   */\n  private _sendLogRecords(\n    logRecords: ReadableLogRecord[],\n    done?: (result: ExportResult) => void\n  ): void {\n    for (const logRecord of logRecords) {\n      console.dir(this._exportInfo(logRecord), { depth: 3 });\n    }\n    done?.({ code: ExportResultCode.SUCCESS });\n  }\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;AAEH,OAAO,EAAgB,oBAAoB,EAAE,MAAM,qBAAqB,CAAC;AACzE,OAAO,EAAE,gBAAgB,EAAE,MAAM,qBAAqB,CAAC;;;;;;;;;;;;;;;;;AAKvD;;;GAGG,CAEH,6BAAA,EAA+B,CAC/B,IAAA,2BAAA;IAAA,SAAA,4BAsDA,CAAC;IArDC;;;;OAIG,CACI,yBAAA,SAAA,CAAA,MAAM,GAAb,SACE,IAAyB,EACzB,cAA8C;QAE9C,IAAI,CAAC,eAAe,CAAC,IAAI,EAAE,cAAc,CAAC,CAAC;IAC7C,CAAC;IAED;;OAEG,CACI,yBAAA,SAAA,CAAA,QAAQ,GAAf;QACE,OAAO,OAAO,CAAC,OAAO,EAAE,CAAC;IAC3B,CAAC;IAED;;;OAGG,CACK,yBAAA,SAAA,CAAA,WAAW,GAAnB,SAAoB,SAA4B;;QAC9C,OAAO;YACL,QAAQ,EAAE;gBACR,UAAU,EAAE,SAAS,CAAC,QAAQ,CAAC,UAAU;aAC1C;YACD,SAAS,EAAE,0PAAA,AAAoB,EAAC,SAAS,CAAC,MAAM,CAAC;YACjD,OAAO,EAAE,CAAA,KAAA,SAAS,CAAC,WAAW,MAAA,QAAA,OAAA,KAAA,IAAA,KAAA,IAAA,GAAE,OAAO;YACvC,MAAM,EAAE,CAAA,KAAA,SAAS,CAAC,WAAW,MAAA,QAAA,OAAA,KAAA,IAAA,KAAA,IAAA,GAAE,MAAM;YACrC,UAAU,EAAE,CAAA,KAAA,SAAS,CAAC,WAAW,MAAA,QAAA,OAAA,KAAA,IAAA,KAAA,IAAA,GAAE,UAAU;YAC7C,YAAY,EAAE,SAAS,CAAC,YAAY;YACpC,cAAc,EAAE,SAAS,CAAC,cAAc;YACxC,IAAI,EAAE,SAAS,CAAC,IAAI;YACpB,UAAU,EAAE,SAAS,CAAC,UAAU;SACjC,CAAC;IACJ,CAAC;IAED;;;;OAIG,CACK,yBAAA,SAAA,CAAA,eAAe,GAAvB,SACE,UAA+B,EAC/B,IAAqC;;;YAErC,IAAwB,IAAA,eAAA,SAAA,UAAU,CAAA,EAAA,iBAAA,aAAA,IAAA,EAAA,EAAA,CAAA,eAAA,IAAA,EAAA,iBAAA,aAAA,IAAA,GAAE;gBAA/B,IAAM,SAAS,GAAA,eAAA,KAAA;gBAClB,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,EAAE;oBAAE,KAAK,EAAE,CAAC;gBAAA,CAAE,CAAC,CAAC;aACxD;;;;;;;;;;;;QACD,IAAI,KAAA,QAAJ,IAAI,KAAA,KAAA,IAAA,KAAA,IAAJ,IAAI,CAAG;YAAE,IAAI,+NAAE,mBAAgB,CAAC,OAAO;QAAA,CAAE,CAAC,CAAC;IAC7C,CAAC;IACH,OAAA,wBAAC;AAAD,CAAC,AAtDD,IAsDC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 907, "column": 0}, "map": {"version": 3, "file": "SimpleLogRecordProcessor.js", "sourceRoot": "", "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/node_modules/%40opentelemetry/sdk-logs/src/export/SimpleLogRecordProcessor.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport type { ExportResult } from '@opentelemetry/core';\nimport {\n  BindOnceFuture,\n  ExportResultCode,\n  globalErrorHandler,\n  internal,\n} from '@opentelemetry/core';\nimport type { LogRecordExporter } from './LogRecordExporter';\nimport type { LogRecordProcessor } from '../LogRecordProcessor';\nimport type { LogRecord } from './../LogRecord';\n\nexport class SimpleLogRecordProcessor implements LogRecordProcessor {\n  private _shutdownOnce: BindOnceFuture<void>;\n  private _unresolvedExports: Set<Promise<void>>;\n\n  constructor(private readonly _exporter: LogRecordExporter) {\n    this._shutdownOnce = new BindOnceFuture(this._shutdown, this);\n    this._unresolvedExports = new Set<Promise<void>>();\n  }\n\n  public onEmit(logRecord: LogRecord): void {\n    if (this._shutdownOnce.isCalled) {\n      return;\n    }\n\n    const doExport = () =>\n      internal\n        ._export(this._exporter, [logRecord])\n        .then((result: ExportResult) => {\n          if (result.code !== ExportResultCode.SUCCESS) {\n            globalErrorHandler(\n              result.error ??\n                new Error(\n                  `SimpleLogRecordProcessor: log record export failed (status ${result})`\n                )\n            );\n          }\n        })\n        .catch(globalErrorHandler);\n\n    // Avoid scheduling a promise to make the behavior more predictable and easier to test\n    if (logRecord.resource.asyncAttributesPending) {\n      const exportPromise = logRecord.resource\n        .waitForAsyncAttributes?.()\n        .then(() => {\n          // Using TS Non-null assertion operator because exportPromise could not be null in here\n          // if waitForAsyncAttributes is not present this code will never be reached\n          // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n          this._unresolvedExports.delete(exportPromise!);\n          return doExport();\n        }, globalErrorHandler);\n\n      // store the unresolved exports\n      if (exportPromise != null) {\n        this._unresolvedExports.add(exportPromise);\n      }\n    } else {\n      void doExport();\n    }\n  }\n\n  public async forceFlush(): Promise<void> {\n    // await unresolved resources before resolving\n    await Promise.all(Array.from(this._unresolvedExports));\n  }\n\n  public shutdown(): Promise<void> {\n    return this._shutdownOnce.call();\n  }\n\n  private _shutdown(): Promise<void> {\n    return this._exporter.shutdown();\n  }\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;;;;AAGH,OAAO,EACL,cAAc,EACd,gBAAgB,EAChB,kBAAkB,EAClB,QAAQ,GACT,MAAM,qBAAqB,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAK7B,IAAA,2BAAA;IAIE,SAAA,yBAA6B,SAA4B;QAA5B,IAAA,CAAA,SAAS,GAAT,SAAS,CAAmB;QACvD,IAAI,CAAC,aAAa,GAAG,sOAAI,iBAAc,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,CAAC;QAC9D,IAAI,CAAC,kBAAkB,GAAG,IAAI,GAAG,EAAiB,CAAC;IACrD,CAAC;IAEM,yBAAA,SAAA,CAAA,MAAM,GAAb,SAAc,SAAoB;QAAlC,IAAA,QAAA,IAAA,CAuCC;;QAtCC,IAAI,IAAI,CAAC,aAAa,CAAC,QAAQ,EAAE;YAC/B,OAAO;SACR;QAED,IAAM,QAAQ,GAAG;YACf,OAAA,iPAAQ,CACL,OAAO,CAAC,KAAI,CAAC,SAAS,EAAE;gBAAC,SAAS;aAAC,CAAC,CACpC,IAAI,CAAC,SAAC,MAAoB;;gBACzB,IAAI,MAAM,CAAC,IAAI,KAAK,gPAAgB,CAAC,OAAO,EAAE;6QAC5C,qBAAA,AAAkB,EAChB,CAAA,KAAA,MAAM,CAAC,KAAK,MAAA,QAAA,OAAA,KAAA,IAAA,KACV,IAAI,KAAK,CACP,gEAA8D,MAAM,GAAA,GAAG,CACxE,CACJ,CAAC;iBACH;YACH,CAAC,CAAC,CACD,KAAK,sPAAC,qBAAkB,CAAC;QAZ5B,CAY4B,CAAC;QAE/B,sFAAsF;QACtF,IAAI,SAAS,CAAC,QAAQ,CAAC,sBAAsB,EAAE;YAC7C,IAAM,eAAa,GAAG,CAAA,KAAA,CAAA,KAAA,SAAS,CAAC,QAAQ,EACrC,sBAAsB,MAAA,QAAA,OAAA,KAAA,IAAA,KAAA,IAAA,GAAA,IAAA,CAAA,IACtB,IAAI,CAAC;gBACJ,uFAAuF;gBACvF,2EAA2E;gBAC3E,oEAAoE;gBACpE,KAAI,CAAC,kBAAkB,CAAC,MAAM,CAAC,eAAc,CAAC,CAAC;gBAC/C,OAAO,QAAQ,EAAE,CAAC;YACpB,CAAC,uPAAE,qBAAkB,CAAC,CAAC;YAEzB,+BAA+B;YAC/B,IAAI,eAAa,IAAI,IAAI,EAAE;gBACzB,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,eAAa,CAAC,CAAC;aAC5C;SACF,MAAM;YACL,KAAK,QAAQ,EAAE,CAAC;SACjB;IACH,CAAC;IAEY,yBAAA,SAAA,CAAA,UAAU,GAAvB;;;;;wBACE,8CAA8C;wBAC9C,OAAA;4BAAA,EAAA,OAAA;4BAAM,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;yBAAA,CAAA;;wBADtD,8CAA8C;wBAC9C,GAAA,IAAA,EAAsD,CAAC;;;;;;;KACxD;IAEM,yBAAA,SAAA,CAAA,QAAQ,GAAf;QACE,OAAO,IAAI,CAAC,aAAa,CAAC,IAAI,EAAE,CAAC;IACnC,CAAC;IAEO,yBAAA,SAAA,CAAA,SAAS,GAAjB;QACE,OAAO,IAAI,CAAC,SAAS,CAAC,QAAQ,EAAE,CAAC;IACnC,CAAC;IACH,OAAA,wBAAC;AAAD,CAAC,AA9DD,IA8DC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1127, "column": 0}, "map": {"version": 3, "file": "InMemoryLogRecordExporter.js", "sourceRoot": "", "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/node_modules/%40opentelemetry/sdk-logs/src/export/InMemoryLogRecordExporter.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport type { ExportResult } from '@opentelemetry/core';\nimport { ExportResultCode } from '@opentelemetry/core';\n\nimport type { ReadableLogRecord } from './ReadableLogRecord';\nimport type { LogRecordExporter } from './LogRecordExporter';\n\n/**\n * This class can be used for testing purposes. It stores the exported LogRecords\n * in a list in memory that can be retrieved using the `getFinishedLogRecords()`\n * method.\n */\nexport class InMemoryLogRecordExporter implements LogRecordExporter {\n  private _finishedLogRecords: ReadableLogRecord[] = [];\n\n  /**\n   * Indicates if the exporter has been \"shutdown.\"\n   * When false, exported log records will not be stored in-memory.\n   */\n  protected _stopped = false;\n\n  public export(\n    logs: ReadableLogRecord[],\n    resultCallback: (result: ExportResult) => void\n  ) {\n    if (this._stopped) {\n      return resultCallback({\n        code: ExportResultCode.FAILED,\n        error: new Error('Exporter has been stopped'),\n      });\n    }\n\n    this._finishedLogRecords.push(...logs);\n    resultCallback({ code: ExportResultCode.SUCCESS });\n  }\n\n  public shutdown(): Promise<void> {\n    this._stopped = true;\n    this.reset();\n    return Promise.resolve();\n  }\n\n  public getFinishedLogRecords(): ReadableLogRecord[] {\n    return this._finishedLogRecords;\n  }\n\n  public reset(): void {\n    this._finishedLogRecords = [];\n  }\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;AAGH,OAAO,EAAE,gBAAgB,EAAE,MAAM,qBAAqB,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAKvD;;;;GAIG,CACH,IAAA,4BAAA;IAAA,SAAA;QACU,IAAA,CAAA,mBAAmB,GAAwB,EAAE,CAAC;QAEtD;;;WAGG,CACO,IAAA,CAAA,QAAQ,GAAG,KAAK,CAAC;IA8B7B,CAAC;IA5BQ,0BAAA,SAAA,CAAA,MAAM,GAAb,SACE,IAAyB,EACzB,cAA8C;;QAE9C,IAAI,IAAI,CAAC,QAAQ,EAAE;YACjB,OAAO,cAAc,CAAC;gBACpB,IAAI,+NAAE,mBAAgB,CAAC,MAAM;gBAC7B,KAAK,EAAE,IAAI,KAAK,CAAC,2BAA2B,CAAC;aAC9C,CAAC,CAAC;SACJ;QAED,CAAA,KAAA,IAAI,CAAC,mBAAmB,CAAA,CAAC,IAAI,CAAA,KAAA,CAAA,IAAA,cAAA,EAAA,EAAA,OAAI,IAAI,GAAA,QAAE;QACvC,cAAc,CAAC;YAAE,IAAI,+NAAE,mBAAgB,CAAC,OAAO;QAAA,CAAE,CAAC,CAAC;IACrD,CAAC;IAEM,0BAAA,SAAA,CAAA,QAAQ,GAAf;QACE,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;QACrB,IAAI,CAAC,KAAK,EAAE,CAAC;QACb,OAAO,OAAO,CAAC,OAAO,EAAE,CAAC;IAC3B,CAAC;IAEM,0BAAA,SAAA,CAAA,qBAAqB,GAA5B;QACE,OAAO,IAAI,CAAC,mBAAmB,CAAC;IAClC,CAAC;IAEM,0BAAA,SAAA,CAAA,KAAK,GAAZ;QACE,IAAI,CAAC,mBAAmB,GAAG,EAAE,CAAC;IAChC,CAAC;IACH,OAAA,yBAAC;AAAD,CAAC,AArCD,IAqCC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1220, "column": 0}, "map": {"version": 3, "file": "BatchLogRecordProcessorBase.js", "sourceRoot": "", "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/node_modules/%40opentelemetry/sdk-logs/src/export/BatchLogRecordProcessorBase.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport type { ExportResult } from '@opentelemetry/core';\nimport { diag } from '@opentelemetry/api';\nimport {\n  ExportResultCode,\n  getEnv,\n  globalErrorHandler,\n  unrefTimer,\n  BindOnceFuture,\n  internal,\n  callWithTimeout,\n} from '@opentelemetry/core';\n\nimport type { BufferConfig } from '../types';\nimport type { LogRecord } from '../LogRecord';\nimport type { LogRecordExporter } from './LogRecordExporter';\nimport type { LogRecordProcessor } from '../LogRecordProcessor';\n\nexport abstract class BatchLogRecordProcessorBase<T extends BufferConfig>\n  implements LogRecordProcessor\n{\n  private readonly _maxExportBatchSize: number;\n  private readonly _maxQueueSize: number;\n  private readonly _scheduledDelayMillis: number;\n  private readonly _exportTimeoutMillis: number;\n\n  private _finishedLogRecords: LogRecord[] = [];\n  private _timer: NodeJS.Timeout | undefined;\n  private _shutdownOnce: BindOnceFuture<void>;\n\n  constructor(\n    private readonly _exporter: LogRecordExporter,\n    config?: T\n  ) {\n    const env = getEnv();\n    this._maxExportBatchSize =\n      config?.maxExportBatchSize ?? env.OTEL_BLRP_MAX_EXPORT_BATCH_SIZE;\n    this._maxQueueSize = config?.maxQueueSize ?? env.OTEL_BLRP_MAX_QUEUE_SIZE;\n    this._scheduledDelayMillis =\n      config?.scheduledDelayMillis ?? env.OTEL_BLRP_SCHEDULE_DELAY;\n    this._exportTimeoutMillis =\n      config?.exportTimeoutMillis ?? env.OTEL_BLRP_EXPORT_TIMEOUT;\n\n    this._shutdownOnce = new BindOnceFuture(this._shutdown, this);\n\n    if (this._maxExportBatchSize > this._maxQueueSize) {\n      diag.warn(\n        'BatchLogRecordProcessor: maxExportBatchSize must be smaller or equal to maxQueueSize, setting maxExportBatchSize to match maxQueueSize'\n      );\n      this._maxExportBatchSize = this._maxQueueSize;\n    }\n  }\n\n  public onEmit(logRecord: LogRecord): void {\n    if (this._shutdownOnce.isCalled) {\n      return;\n    }\n    this._addToBuffer(logRecord);\n  }\n\n  public forceFlush(): Promise<void> {\n    if (this._shutdownOnce.isCalled) {\n      return this._shutdownOnce.promise;\n    }\n    return this._flushAll();\n  }\n\n  public shutdown(): Promise<void> {\n    return this._shutdownOnce.call();\n  }\n\n  private async _shutdown(): Promise<void> {\n    this.onShutdown();\n    await this._flushAll();\n    await this._exporter.shutdown();\n  }\n\n  /** Add a LogRecord in the buffer. */\n  private _addToBuffer(logRecord: LogRecord) {\n    if (this._finishedLogRecords.length >= this._maxQueueSize) {\n      return;\n    }\n    this._finishedLogRecords.push(logRecord);\n    this._maybeStartTimer();\n  }\n\n  /**\n   * Send all LogRecords to the exporter respecting the batch size limit\n   * This function is used only on forceFlush or shutdown,\n   * for all other cases _flush should be used\n   * */\n  private _flushAll(): Promise<void> {\n    return new Promise((resolve, reject) => {\n      const promises = [];\n      const batchCount = Math.ceil(\n        this._finishedLogRecords.length / this._maxExportBatchSize\n      );\n      for (let i = 0; i < batchCount; i++) {\n        promises.push(this._flushOneBatch());\n      }\n      Promise.all(promises)\n        .then(() => {\n          resolve();\n        })\n        .catch(reject);\n    });\n  }\n\n  private _flushOneBatch(): Promise<void> {\n    this._clearTimer();\n    if (this._finishedLogRecords.length === 0) {\n      return Promise.resolve();\n    }\n    return new Promise((resolve, reject) => {\n      callWithTimeout(\n        this._export(\n          this._finishedLogRecords.splice(0, this._maxExportBatchSize)\n        ),\n        this._exportTimeoutMillis\n      )\n        .then(() => resolve())\n        .catch(reject);\n    });\n  }\n\n  private _maybeStartTimer() {\n    if (this._timer !== undefined) {\n      return;\n    }\n    this._timer = setTimeout(() => {\n      this._flushOneBatch()\n        .then(() => {\n          if (this._finishedLogRecords.length > 0) {\n            this._clearTimer();\n            this._maybeStartTimer();\n          }\n        })\n        .catch(e => {\n          globalErrorHandler(e);\n        });\n    }, this._scheduledDelayMillis);\n    unrefTimer(this._timer);\n  }\n\n  private _clearTimer() {\n    if (this._timer !== undefined) {\n      clearTimeout(this._timer);\n      this._timer = undefined;\n    }\n  }\n\n  private _export(logRecords: LogRecord[]): Promise<void> {\n    const doExport = () =>\n      internal\n        ._export(this._exporter, logRecords)\n        .then((result: ExportResult) => {\n          if (result.code !== ExportResultCode.SUCCESS) {\n            globalErrorHandler(\n              result.error ??\n                new Error(\n                  `BatchLogRecordProcessor: log record export failed (status ${result})`\n                )\n            );\n          }\n        })\n        .catch(globalErrorHandler);\n\n    const pendingResources = logRecords\n      .map(logRecord => logRecord.resource)\n      .filter(resource => resource.asyncAttributesPending);\n\n    // Avoid scheduling a promise to make the behavior more predictable and easier to test\n    if (pendingResources.length === 0) {\n      return doExport();\n    } else {\n      return Promise.all(\n        pendingResources.map(resource => resource.waitForAsyncAttributes?.())\n      ).then(doExport, globalErrorHandler);\n    }\n  }\n\n  protected abstract onShutdown(): void;\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;AAGH,OAAO,EAAE,IAAI,EAAE,MAAM,oBAAoB,CAAC;;;AAC1C,OAAO,EACL,gBAAgB,EAChB,MAAM,EACN,kBAAkB,EAClB,UAAU,EACV,cAAc,EACd,QAAQ,EACR,eAAe,GAChB,MAAM,qBAAqB,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAO7B,IAAA,8BAAA;IAYE,SAAA,4BACmB,SAA4B,EAC7C,MAAU;;QADO,IAAA,CAAA,SAAS,GAAT,SAAS,CAAmB;QALvC,IAAA,CAAA,mBAAmB,GAAgB,EAAE,CAAC;QAQ5C,IAAM,GAAG,IAAG,+PAAA,AAAM,EAAE,CAAC;QACrB,IAAI,CAAC,mBAAmB,GACtB,CAAA,KAAA,MAAM,KAAA,QAAN,MAAM,KAAA,KAAA,IAAA,KAAA,IAAN,MAAM,CAAE,kBAAkB,MAAA,QAAA,OAAA,KAAA,IAAA,KAAI,GAAG,CAAC,+BAA+B,CAAC;QACpE,IAAI,CAAC,aAAa,GAAG,CAAA,KAAA,MAAM,KAAA,QAAN,MAAM,KAAA,KAAA,IAAA,KAAA,IAAN,MAAM,CAAE,YAAY,MAAA,QAAA,OAAA,KAAA,IAAA,KAAI,GAAG,CAAC,wBAAwB,CAAC;QAC1E,IAAI,CAAC,qBAAqB,GACxB,CAAA,KAAA,MAAM,KAAA,QAAN,MAAM,KAAA,KAAA,IAAA,KAAA,IAAN,MAAM,CAAE,oBAAoB,MAAA,QAAA,OAAA,KAAA,IAAA,KAAI,GAAG,CAAC,wBAAwB,CAAC;QAC/D,IAAI,CAAC,oBAAoB,GACvB,CAAA,KAAA,MAAM,KAAA,QAAN,MAAM,KAAA,KAAA,IAAA,KAAA,IAAN,MAAM,CAAE,mBAAmB,MAAA,QAAA,OAAA,KAAA,IAAA,KAAI,GAAG,CAAC,wBAAwB,CAAC;QAE9D,IAAI,CAAC,aAAa,GAAG,sOAAI,iBAAc,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,CAAC;QAE9D,IAAI,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC,aAAa,EAAE;qLACjD,OAAI,CAAC,IAAI,CACP,wIAAwI,CACzI,CAAC;YACF,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC,aAAa,CAAC;SAC/C;IACH,CAAC;IAEM,4BAAA,SAAA,CAAA,MAAM,GAAb,SAAc,SAAoB;QAChC,IAAI,IAAI,CAAC,aAAa,CAAC,QAAQ,EAAE;YAC/B,OAAO;SACR;QACD,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC;IAC/B,CAAC;IAEM,4BAAA,SAAA,CAAA,UAAU,GAAjB;QACE,IAAI,IAAI,CAAC,aAAa,CAAC,QAAQ,EAAE;YAC/B,OAAO,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC;SACnC;QACD,OAAO,IAAI,CAAC,SAAS,EAAE,CAAC;IAC1B,CAAC;IAEM,4BAAA,SAAA,CAAA,QAAQ,GAAf;QACE,OAAO,IAAI,CAAC,aAAa,CAAC,IAAI,EAAE,CAAC;IACnC,CAAC;IAEa,4BAAA,SAAA,CAAA,SAAS,GAAvB;;;;;wBACE,IAAI,CAAC,UAAU,EAAE,CAAC;wBAClB,OAAA;4BAAA,EAAA,OAAA;4BAAM,IAAI,CAAC,SAAS,EAAE;yBAAA,CAAA;;wBAAtB,GAAA,IAAA,EAAsB,CAAC;wBACvB,OAAA;4BAAA,EAAA,OAAA;4BAAM,IAAI,CAAC,SAAS,CAAC,QAAQ,EAAE;yBAAA,CAAA;;wBAA/B,GAAA,IAAA,EAA+B,CAAC;;;;;;;KACjC;IAED,mCAAA,EAAqC,CAC7B,4BAAA,SAAA,CAAA,YAAY,GAApB,SAAqB,SAAoB;QACvC,IAAI,IAAI,CAAC,mBAAmB,CAAC,MAAM,IAAI,IAAI,CAAC,aAAa,EAAE;YACzD,OAAO;SACR;QACD,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QACzC,IAAI,CAAC,gBAAgB,EAAE,CAAC;IAC1B,CAAC;IAED;;;;SAIK,CACG,4BAAA,SAAA,CAAA,SAAS,GAAjB;QAAA,IAAA,QAAA,IAAA,CAeC;QAdC,OAAO,IAAI,OAAO,CAAC,SAAC,OAAO,EAAE,MAAM;YACjC,IAAM,QAAQ,GAAG,EAAE,CAAC;YACpB,IAAM,UAAU,GAAG,IAAI,CAAC,IAAI,CAC1B,KAAI,CAAC,mBAAmB,CAAC,MAAM,GAAG,KAAI,CAAC,mBAAmB,CAC3D,CAAC;YACF,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,UAAU,EAAE,CAAC,EAAE,CAAE;gBACnC,QAAQ,CAAC,IAAI,CAAC,KAAI,CAAC,cAAc,EAAE,CAAC,CAAC;aACtC;YACD,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,CAClB,IAAI,CAAC;gBACJ,OAAO,EAAE,CAAC;YACZ,CAAC,CAAC,CACD,KAAK,CAAC,MAAM,CAAC,CAAC;QACnB,CAAC,CAAC,CAAC;IACL,CAAC;IAEO,4BAAA,SAAA,CAAA,cAAc,GAAtB;QAAA,IAAA,QAAA,IAAA,CAeC;QAdC,IAAI,CAAC,WAAW,EAAE,CAAC;QACnB,IAAI,IAAI,CAAC,mBAAmB,CAAC,MAAM,KAAK,CAAC,EAAE;YACzC,OAAO,OAAO,CAAC,OAAO,EAAE,CAAC;SAC1B;QACD,OAAO,IAAI,OAAO,CAAC,SAAC,OAAO,EAAE,MAAM;iPACjC,kBAAA,AAAe,EACb,KAAI,CAAC,OAAO,CACV,KAAI,CAAC,mBAAmB,CAAC,MAAM,CAAC,CAAC,EAAE,KAAI,CAAC,mBAAmB,CAAC,CAC7D,EACD,KAAI,CAAC,oBAAoB,CAC1B,CACE,IAAI,CAAC;gBAAM,OAAA,OAAO,EAAE;YAAT,CAAS,CAAC,CACrB,KAAK,CAAC,MAAM,CAAC,CAAC;QACnB,CAAC,CAAC,CAAC;IACL,CAAC;IAEO,4BAAA,SAAA,CAAA,gBAAgB,GAAxB;QAAA,IAAA,QAAA,IAAA,CAiBC;QAhBC,IAAI,IAAI,CAAC,MAAM,KAAK,SAAS,EAAE;YAC7B,OAAO;SACR;QACD,IAAI,CAAC,MAAM,GAAG,UAAU,CAAC;YACvB,KAAI,CAAC,cAAc,EAAE,CAClB,IAAI,CAAC;gBACJ,IAAI,KAAI,CAAC,mBAAmB,CAAC,MAAM,GAAG,CAAC,EAAE;oBACvC,KAAI,CAAC,WAAW,EAAE,CAAC;oBACnB,KAAI,CAAC,gBAAgB,EAAE,CAAC;iBACzB;YACH,CAAC,CAAC,CACD,KAAK,CAAC,SAAA,CAAC;oBACN,0QAAA,AAAkB,EAAC,CAAC,CAAC,CAAC;YACxB,CAAC,CAAC,CAAC;QACP,CAAC,EAAE,IAAI,CAAC,qBAAqB,CAAC,CAAC;iQAC/B,aAAA,AAAU,EAAC,IAAI,CAAC,MAAM,CAAC,CAAC;IAC1B,CAAC;IAEO,4BAAA,SAAA,CAAA,WAAW,GAAnB;QACE,IAAI,IAAI,CAAC,MAAM,KAAK,SAAS,EAAE;YAC7B,YAAY,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YAC1B,IAAI,CAAC,MAAM,GAAG,SAAS,CAAC;SACzB;IACH,CAAC;IAEO,4BAAA,SAAA,CAAA,OAAO,GAAf,SAAgB,UAAuB;QAAvC,IAAA,QAAA,IAAA,CA4BC;QA3BC,IAAM,QAAQ,GAAG;YACf,6OAAA,WAAQ,CACL,OAAO,CAAC,KAAI,CAAC,SAAS,EAAE,UAAU,CAAC,CACnC,IAAI,CAAC,SAAC,MAAoB;;gBACzB,IAAI,MAAM,CAAC,IAAI,kOAAK,mBAAgB,CAAC,OAAO,EAAE;6QAC5C,qBAAA,AAAkB,EAChB,CAAA,KAAA,MAAM,CAAC,KAAK,MAAA,QAAA,OAAA,KAAA,IAAA,KACV,IAAI,KAAK,CACP,+DAA6D,MAAM,GAAA,GAAG,CACvE,CACJ,CAAC;iBACH;YACH,CAAC,CAAC,CACD,KAAK,qPAAC,sBAAkB,CAAC;QAZ5B,CAY4B,CAAC;QAE/B,IAAM,gBAAgB,GAAG,UAAU,CAChC,GAAG,CAAC,SAAA,SAAS;YAAI,OAAA,SAAS,CAAC,QAAQ;QAAlB,CAAkB,CAAC,CACpC,MAAM,CAAC,SAAA,QAAQ;YAAI,OAAA,QAAQ,CAAC,sBAAsB;QAA/B,CAA+B,CAAC,CAAC;QAEvD,sFAAsF;QACtF,IAAI,gBAAgB,CAAC,MAAM,KAAK,CAAC,EAAE;YACjC,OAAO,QAAQ,EAAE,CAAC;SACnB,MAAM;YACL,OAAO,OAAO,CAAC,GAAG,CAChB,gBAAgB,CAAC,GAAG,CAAC,SAAA,QAAQ;gBAAA,IAAA;gBAAI,OAAA,CAAA,KAAA,QAAQ,CAAC,sBAAsB,MAAA,QAAA,OAAA,KAAA,IAAA,KAAA,IAAA,GAAA,IAAA,CAA/B,QAAQ,CAA2B,CAAA;YAAA,CAAA,CAAC,CACtE,CAAC,IAAI,CAAC,QAAQ,uPAAE,qBAAkB,CAAC,CAAC;SACtC;IACH,CAAC;IAGH,OAAA,2BAAC;AAAD,CAAC,AApKD,IAoKC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1520, "column": 0}, "map": {"version": 3, "file": "BatchLogRecordProcessor.js", "sourceRoot": "", "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/node_modules/%40opentelemetry/sdk-logs/src/platform/browser/export/BatchLogRecordProcessor.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport type { LogRecordExporter } from './../../../export/LogRecordExporter';\nimport type { BatchLogRecordProcessorBrowserConfig } from '../../../types';\nimport { BatchLogRecordProcessorBase } from '../../../export/BatchLogRecordProcessorBase';\n\nexport class BatchLogRecordProcessor extends BatchLogRecordProcessorBase<BatchLogRecordProcessorBrowserConfig> {\n  private _visibilityChangeListener?: () => void;\n  private _pageHideListener?: () => void;\n\n  constructor(\n    exporter: LogRecordExporter,\n    config?: BatchLogRecordProcessorBrowserConfig\n  ) {\n    super(exporter, config);\n    this._onInit(config);\n  }\n\n  protected onShutdown(): void {\n    if (typeof document === 'undefined') {\n      return;\n    }\n    if (this._visibilityChangeListener) {\n      document.removeEventListener(\n        'visibilitychange',\n        this._visibilityChangeListener\n      );\n    }\n    if (this._pageHideListener) {\n      document.removeEventListener('pagehide', this._pageHideListener);\n    }\n  }\n\n  private _onInit(config?: BatchLogRecordProcessorBrowserConfig): void {\n    if (\n      config?.disableAutoFlushOnDocumentHide === true ||\n      typeof document === 'undefined'\n    ) {\n      return;\n    }\n    this._visibilityChangeListener = () => {\n      if (document.visibilityState === 'hidden') {\n        void this.forceFlush();\n      }\n    };\n    this._pageHideListener = () => {\n      void this.forceFlush();\n    };\n    document.addEventListener(\n      'visibilitychange',\n      this._visibilityChangeListener\n    );\n\n    // use 'pagehide' event as a fallback for Safari; see https://bugs.webkit.org/show_bug.cgi?id=116769\n    document.addEventListener('pagehide', this._pageHideListener);\n  }\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;AAIH,OAAO,EAAE,2BAA2B,EAAE,MAAM,6CAA6C,CAAC;;;;;;;;;;;;;;;;;;;;;;AAE1F,IAAA,0BAAA,SAAA,MAAA;IAA6C,UAAA,yBAAA,QAAiE;IAI5G,SAAA,wBACE,QAA2B,EAC3B,MAA6C;QAF/C,IAAA,QAIE,OAAA,IAAA,CAAA,IAAA,EAAM,QAAQ,EAAE,MAAM,CAAC,IAAA,IAAA,CAExB;QADC,KAAI,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;;IACvB,CAAC;IAES,wBAAA,SAAA,CAAA,UAAU,GAApB;QACE,IAAI,OAAO,QAAQ,KAAK,WAAW,EAAE;YACnC,OAAO;SACR;QACD,IAAI,IAAI,CAAC,yBAAyB,EAAE;YAClC,QAAQ,CAAC,mBAAmB,CAC1B,kBAAkB,EAClB,IAAI,CAAC,yBAAyB,CAC/B,CAAC;SACH;QACD,IAAI,IAAI,CAAC,iBAAiB,EAAE;YAC1B,QAAQ,CAAC,mBAAmB,CAAC,UAAU,EAAE,IAAI,CAAC,iBAAiB,CAAC,CAAC;SAClE;IACH,CAAC;IAEO,wBAAA,SAAA,CAAA,OAAO,GAAf,SAAgB,MAA6C;QAA7D,IAAA,QAAA,IAAA,CAsBC;QArBC,IACE,CAAA,MAAM,KAAA,QAAN,MAAM,KAAA,KAAA,IAAA,KAAA,IAAN,MAAM,CAAE,8BAA8B,MAAK,IAAI,IAC/C,OAAO,QAAQ,KAAK,WAAW,EAC/B;YACA,OAAO;SACR;QACD,IAAI,CAAC,yBAAyB,GAAG;YAC/B,IAAI,QAAQ,CAAC,eAAe,KAAK,QAAQ,EAAE;gBACzC,KAAK,KAAI,CAAC,UAAU,EAAE,CAAC;aACxB;QACH,CAAC,CAAC;QACF,IAAI,CAAC,iBAAiB,GAAG;YACvB,KAAK,KAAI,CAAC,UAAU,EAAE,CAAC;QACzB,CAAC,CAAC;QACF,QAAQ,CAAC,gBAAgB,CACvB,kBAAkB,EAClB,IAAI,CAAC,yBAAyB,CAC/B,CAAC;QAEF,oGAAoG;QACpG,QAAQ,CAAC,gBAAgB,CAAC,UAAU,EAAE,IAAI,CAAC,iBAAiB,CAAC,CAAC;IAChE,CAAC;IACH,OAAA,uBAAC;AAAD,CAAC,AAlDD,4MAA6C,8BAA2B,GAkDvE", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1644, "column": 0}, "map": {"version": 3, "file": "NoopLogger.js", "sourceRoot": "", "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/node_modules/%40opentelemetry/api-logs/src/NoopLogger.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { Logger } from './types/Logger';\nimport { LogRecord } from './types/LogRecord';\n\nexport class NoopLogger implements Logger {\n  emit(_logRecord: LogRecord): void {}\n}\n\nexport const NOOP_LOGGER = new NoopLogger();\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;;AAKH,IAAA,aAAA;IAAA,SAAA,cAEA,CAAC;IADC,WAAA,SAAA,CAAA,IAAI,GAAJ,SAAK,UAAqB,GAAS,CAAC;IACtC,OAAA,UAAC;AAAD,CAAC,AAFD,IAEC;;AAEM,IAAM,WAAW,GAAG,IAAI,UAAU,EAAE,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1675, "column": 0}, "map": {"version": 3, "file": "globalThis.js", "sourceRoot": "", "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/node_modules/%40opentelemetry/api-logs/src/platform/browser/globalThis.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n// Updates to this file should also be replicated to @opentelemetry/api and\n// @opentelemetry/core too.\n\n/**\n * - globalThis (New standard)\n * - self (Will return the current window instance for supported browsers)\n * - window (fallback for older browser implementations)\n * - global (NodeJS implementation)\n * - <object> (When all else fails)\n */\n\n/** only globals that common to node and browsers are allowed */\n// eslint-disable-next-line node/no-unsupported-features/es-builtins, no-undef\nexport const _globalThis: typeof globalThis =\n  typeof globalThis === 'object'\n    ? globalThis\n    : typeof self === 'object'\n    ? self\n    : typeof window === 'object'\n    ? window\n    : typeof global === 'object'\n    ? global\n    : ({} as typeof globalThis);\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG,CAEH,2EAA2E;AAC3E,2BAA2B;AAE3B;;;;;;GAMG,CAEH,8DAAA,EAAgE,CAChE,8EAA8E;;;;AACvE,IAAM,WAAW,GACtB,OAAO,UAAU,KAAK,QAAQ,GAC1B,UAAU,GACV,OAAO,IAAI,KAAK,QAAQ,GACxB,IAAI,GACJ,OAAO,MAAM,KAAK,QAAQ,GAC1B,MAAM,GACN,OAAO,MAAM,KAAK,QAAQ,GAC1B,MAAM,GACL,CAAA,CAAwB,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1708, "column": 0}, "map": {"version": 3, "file": "global-utils.js", "sourceRoot": "", "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/node_modules/%40opentelemetry/api-logs/src/internal/global-utils.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { LoggerProvider } from '../types/LoggerProvider';\nimport { _globalThis } from '../platform';\n\nexport const GLOBAL_LOGS_API_KEY = Symbol.for('io.opentelemetry.js.api.logs');\n\ntype Get<T> = (version: number) => T;\ntype OtelGlobal = Partial<{\n  [GLOBAL_LOGS_API_KEY]: Get<LoggerProvider>;\n}>;\n\nexport const _global = _globalThis as OtelGlobal;\n\n/**\n * Make a function which accepts a version integer and returns the instance of an API if the version\n * is compatible, or a fallback version (usually NOOP) if it is not.\n *\n * @param requiredVersion Backwards compatibility version which is required to return the instance\n * @param instance Instance which should be returned if the required version is compatible\n * @param fallback Fallback instance, usually NOOP, which will be returned if the required version is not compatible\n */\nexport function makeGetter<T>(\n  requiredVersion: number,\n  instance: T,\n  fallback: T\n): Get<T> {\n  return (version: number): T =>\n    version === requiredVersion ? instance : fallback;\n}\n\n/**\n * A number which should be incremented each time a backwards incompatible\n * change is made to the API. This number is used when an API package\n * attempts to access the global API to ensure it is getting a compatible\n * version. If the global API is not compatible with the API package\n * attempting to get it, a NOOP API implementation will be returned.\n */\nexport const API_BACKWARDS_COMPATIBILITY_VERSION = 1;\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;;;;AAGH,OAAO,EAAE,WAAW,EAAE,MAAM,aAAa,CAAC;;AAEnC,IAAM,mBAAmB,GAAG,MAAM,CAAC,GAAG,CAAC,8BAA8B,CAAC,CAAC;AAOvE,IAAM,OAAO,0MAAG,cAAyB,CAAC;AAU3C,SAAU,UAAU,CACxB,eAAuB,EACvB,QAAW,EACX,QAAW;IAEX,OAAO,SAAC,OAAe;QACrB,OAAA,OAAO,KAAK,eAAe,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,QAAQ;IAAjD,CAAiD,CAAC;AACtD,CAAC;AASM,IAAM,mCAAmC,GAAG,CAAC,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1744, "column": 0}, "map": {"version": 3, "file": "NoopLoggerProvider.js", "sourceRoot": "", "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/node_modules/%40opentelemetry/api-logs/src/NoopLoggerProvider.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { LoggerProvider } from './types/LoggerProvider';\nimport { Logger } from './types/Logger';\nimport { LoggerOptions } from './types/LoggerOptions';\nimport { NoopLogger } from './NoopLogger';\n\nexport class NoopLoggerProvider implements LoggerProvider {\n  getLogger(\n    _name: string,\n    _version?: string | undefined,\n    _options?: LoggerOptions | undefined\n  ): Logger {\n    return new NoopLogger();\n  }\n}\n\nexport const NOOP_LOGGER_PROVIDER = new NoopLoggerProvider();\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;;AAKH,OAAO,EAAE,UAAU,EAAE,MAAM,cAAc,CAAC;;AAE1C,IAAA,qBAAA;IAAA,SAAA,sBAQA,CAAC;IAPC,mBAAA,SAAA,CAAA,SAAS,GAAT,SACE,KAAa,EACb,QAA6B,EAC7B,QAAoC;QAEpC,OAAO,oLAAI,aAAU,EAAE,CAAC;IAC1B,CAAC;IACH,OAAA,kBAAC;AAAD,CAAC,AARD,IAQC;;AAEM,IAAM,oBAAoB,GAAG,IAAI,kBAAkB,EAAE,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1779, "column": 0}, "map": {"version": 3, "file": "logs.js", "sourceRoot": "", "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/node_modules/%40opentelemetry/api-logs/src/api/logs.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  API_BACKWARDS_COMPATIBILITY_VERSION,\n  GLOBAL_LOGS_API_KEY,\n  _global,\n  makeGetter,\n} from '../internal/global-utils';\nimport { LoggerProvider } from '../types/LoggerProvider';\nimport { NOOP_LOGGER_PROVIDER } from '../NoopLoggerProvider';\nimport { Logger } from '../types/Logger';\nimport { LoggerOptions } from '../types/LoggerOptions';\n\nexport class LogsAPI {\n  private static _instance?: LogsAPI;\n\n  private constructor() {}\n\n  public static getInstance(): LogsAPI {\n    if (!this._instance) {\n      this._instance = new LogsAPI();\n    }\n\n    return this._instance;\n  }\n\n  public setGlobalLoggerProvider(provider: LoggerProvider): LoggerProvider {\n    if (_global[GLOBAL_LOGS_API_KEY]) {\n      return this.getLoggerProvider();\n    }\n\n    _global[GLOBAL_LOGS_API_KEY] = makeGetter<LoggerProvider>(\n      API_BACKWARDS_COMPATIBILITY_VERSION,\n      provider,\n      NOOP_LOGGER_PROVIDER\n    );\n\n    return provider;\n  }\n\n  /**\n   * Returns the global logger provider.\n   *\n   * @returns LoggerProvider\n   */\n  public getLoggerProvider(): LoggerProvider {\n    return (\n      _global[GLOBAL_LOGS_API_KEY]?.(API_BACKWARDS_COMPATIBILITY_VERSION) ??\n      NOOP_LOGGER_PROVIDER\n    );\n  }\n\n  /**\n   * Returns a logger from the global logger provider.\n   *\n   * @returns Logger\n   */\n  public getLogger(\n    name: string,\n    version?: string,\n    options?: LoggerOptions\n  ): Logger {\n    return this.getLoggerProvider().getLogger(name, version, options);\n  }\n\n  /** Remove the global logger provider */\n  public disable(): void {\n    delete _global[GLOBAL_LOGS_API_KEY];\n  }\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;AAEH,OAAO,EACL,mCAAmC,EACnC,mBAAmB,EACnB,OAAO,EACP,UAAU,GACX,MAAM,0BAA0B,CAAC;AAElC,OAAO,EAAE,oBAAoB,EAAE,MAAM,uBAAuB,CAAC;;;AAI7D,IAAA,UAAA;IAGE,SAAA,WAAuB,CAAC;IAEV,QAAA,WAAW,GAAzB;QACE,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;YACnB,IAAI,CAAC,SAAS,GAAG,IAAI,OAAO,EAAE,CAAC;SAChC;QAED,OAAO,IAAI,CAAC,SAAS,CAAC;IACxB,CAAC;IAEM,QAAA,SAAA,CAAA,uBAAuB,GAA9B,SAA+B,QAAwB;QACrD,qMAAI,UAAO,kMAAC,sBAAmB,CAAC,EAAE;YAChC,OAAO,IAAI,CAAC,iBAAiB,EAAE,CAAC;SACjC;yMAED,UAAO,kMAAC,sBAAmB,CAAC,wMAAG,aAAA,AAAU,kMACvC,uCAAmC,EACnC,QAAQ,0LACR,uBAAoB,CACrB,CAAC;QAEF,OAAO,QAAQ,CAAC;IAClB,CAAC;IAED;;;;OAIG,CACI,QAAA,SAAA,CAAA,iBAAiB,GAAxB;;QACE,OAAO,AACL,CAAA,KAAA,CAAA,sMAAA,UAAO,kMAAC,sBAAmB,CAAC,MAAA,QAAA,OAAA,KAAA,IAAA,KAAA,IAAA,GAAA,IAAA,kMAA5B,UAAO,EAAwB,uOAAmC,CAAC,MAAA,QAAA,OAAA,KAAA,IAAA,6LACnE,uBAAoB,CACrB,CAAC;IACJ,CAAC;IAED;;;;OAIG,CACI,QAAA,SAAA,CAAA,SAAS,GAAhB,SACE,IAAY,EACZ,OAAgB,EAChB,OAAuB;QAEvB,OAAO,IAAI,CAAC,iBAAiB,EAAE,CAAC,SAAS,CAAC,IAAI,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC;IACpE,CAAC;IAED,sCAAA,EAAwC,CACjC,QAAA,SAAA,CAAA,OAAO,GAAd;QACE,wMAAO,UAAO,kMAAC,sBAAmB,CAAC,CAAC;IACtC,CAAC;IACH,OAAA,OAAC;AAAD,CAAC,AAxDD,IAwDC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1843, "column": 0}, "map": {"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/node_modules/%40opentelemetry/api-logs/src/index.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nexport * from './types/Logger';\nexport * from './types/LoggerProvider';\nexport * from './types/LogRecord';\nexport * from './types/LoggerOptions';\nexport * from './types/AnyValue';\nexport * from './NoopLogger';\nexport * from './NoopLoggerProvider';\n\nimport { LogsAPI } from './api/logs';\nexport const logs = LogsAPI.getInstance();\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;AAUH,OAAO,EAAE,OAAO,EAAE,MAAM,YAAY,CAAC;;;;;;;;;AAC9B,IAAM,IAAI,oLAAG,UAAO,CAAC,WAAW,EAAE,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1884, "column": 0}, "map": {"version": 3, "file": "Logger.js", "sourceRoot": "", "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/node_modules/%40opentelemetry/api-logs/src/types/Logger.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { LogRecord } from './LogRecord';\n\nexport interface Logger {\n  /**\n   * Emit a log record. This method should only be used by log appenders.\n   *\n   * @param logRecord\n   */\n  emit(logRecord: LogRecord): void;\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1907, "column": 0}, "map": {"version": 3, "file": "LoggerProvider.js", "sourceRoot": "", "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/node_modules/%40opentelemetry/api-logs/src/types/LoggerProvider.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { Logger } from './Logger';\nimport { LoggerOptions } from './LoggerOptions';\n\n/**\n * A registry for creating named {@link Logger}s.\n */\nexport interface LoggerProvider {\n  /**\n   * Returns a Logger, creating one if one with the given name, version, and\n   * schemaUrl pair is not already created.\n   *\n   * @param name The name of the logger or instrumentation library.\n   * @param version The version of the logger or instrumentation library.\n   * @param options The options of the logger or instrumentation library.\n   * @returns Logger A Logger with the given name and version\n   */\n  getLogger(name: string, version?: string, options?: LoggerOptions): Logger;\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1930, "column": 0}, "map": {"version": 3, "file": "LogRecord.js", "sourceRoot": "", "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/node_modules/%40opentelemetry/api-logs/src/types/LogRecord.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { Context, TimeInput } from '@opentelemetry/api';\nimport { AnyValue, AnyValueMap } from './AnyValue';\n\nexport type LogBody = AnyValue;\nexport type LogAttributes = AnyValueMap;\n\nexport enum SeverityNumber {\n  UNSPECIFIED = 0,\n  TRACE = 1,\n  TRACE2 = 2,\n  TRACE3 = 3,\n  TRACE4 = 4,\n  DEBUG = 5,\n  DEBUG2 = 6,\n  DEBUG3 = 7,\n  DEBUG4 = 8,\n  INFO = 9,\n  INFO2 = 10,\n  INFO3 = 11,\n  INFO4 = 12,\n  WARN = 13,\n  WARN2 = 14,\n  WARN3 = 15,\n  WARN4 = 16,\n  ERROR = 17,\n  ERROR2 = 18,\n  ERROR3 = 19,\n  ERROR4 = 20,\n  FATAL = 21,\n  FATAL2 = 22,\n  FATAL3 = 23,\n  FATAL4 = 24,\n}\n\nexport interface LogRecord {\n  /**\n   * The time when the log record occurred as UNIX Epoch time in nanoseconds.\n   */\n  timestamp?: TimeInput;\n\n  /**\n   * Time when the event was observed by the collection system.\n   */\n  observedTimestamp?: TimeInput;\n\n  /**\n   * Numerical value of the severity.\n   */\n  severityNumber?: SeverityNumber;\n\n  /**\n   * The severity text.\n   */\n  severityText?: string;\n\n  /**\n   * A value containing the body of the log record.\n   */\n  body?: LogBody;\n\n  /**\n   * Attributes that define the log record.\n   */\n  attributes?: LogAttributes;\n\n  /**\n   * The Context associated with the LogRecord.\n   */\n  context?: Context;\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;AAQH,IAAY,cA0BX;AA1BD,CAAA,SAAY,cAAc;IACxB,cAAA,CAAA,cAAA,CAAA,cAAA,GAAA,EAAA,GAAA,aAAe,CAAA;IACf,cAAA,CAAA,cAAA,CAAA,QAAA,GAAA,EAAA,GAAA,OAAS,CAAA;IACT,cAAA,CAAA,cAAA,CAAA,SAAA,GAAA,EAAA,GAAA,QAAU,CAAA;IACV,cAAA,CAAA,cAAA,CAAA,SAAA,GAAA,EAAA,GAAA,QAAU,CAAA;IACV,cAAA,CAAA,cAAA,CAAA,SAAA,GAAA,EAAA,GAAA,QAAU,CAAA;IACV,cAAA,CAAA,cAAA,CAAA,QAAA,GAAA,EAAA,GAAA,OAAS,CAAA;IACT,cAAA,CAAA,cAAA,CAAA,SAAA,GAAA,EAAA,GAAA,QAAU,CAAA;IACV,cAAA,CAAA,cAAA,CAAA,SAAA,GAAA,EAAA,GAAA,QAAU,CAAA;IACV,cAAA,CAAA,cAAA,CAAA,SAAA,GAAA,EAAA,GAAA,QAAU,CAAA;IACV,cAAA,CAAA,cAAA,CAAA,OAAA,GAAA,EAAA,GAAA,MAAQ,CAAA;IACR,cAAA,CAAA,cAAA,CAAA,QAAA,GAAA,GAAA,GAAA,OAAU,CAAA;IACV,cAAA,CAAA,cAAA,CAAA,QAAA,GAAA,GAAA,GAAA,OAAU,CAAA;IACV,cAAA,CAAA,cAAA,CAAA,QAAA,GAAA,GAAA,GAAA,OAAU,CAAA;IACV,cAAA,CAAA,cAAA,CAAA,OAAA,GAAA,GAAA,GAAA,MAAS,CAAA;IACT,cAAA,CAAA,cAAA,CAAA,QAAA,GAAA,GAAA,GAAA,OAAU,CAAA;IACV,cAAA,CAAA,cAAA,CAAA,QAAA,GAAA,GAAA,GAAA,OAAU,CAAA;IACV,cAAA,CAAA,cAAA,CAAA,QAAA,GAAA,GAAA,GAAA,OAAU,CAAA;IACV,cAAA,CAAA,cAAA,CAAA,QAAA,GAAA,GAAA,GAAA,OAAU,CAAA;IACV,cAAA,CAAA,cAAA,CAAA,SAAA,GAAA,GAAA,GAAA,QAAW,CAAA;IACX,cAAA,CAAA,cAAA,CAAA,SAAA,GAAA,GAAA,GAAA,QAAW,CAAA;IACX,cAAA,CAAA,cAAA,CAAA,SAAA,GAAA,GAAA,GAAA,QAAW,CAAA;IACX,cAAA,CAAA,cAAA,CAAA,QAAA,GAAA,GAAA,GAAA,OAAU,CAAA;IACV,cAAA,CAAA,cAAA,CAAA,SAAA,GAAA,GAAA,GAAA,QAAW,CAAA;IACX,cAAA,CAAA,cAAA,CAAA,SAAA,GAAA,GAAA,GAAA,QAAW,CAAA;IACX,cAAA,CAAA,cAAA,CAAA,SAAA,GAAA,GAAA,GAAA,QAAW,CAAA;AACb,CAAC,EA1BW,cAAc,IAAA,CAAd,cAAc,GAAA,CAAA,CAAA,GA0BzB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1981, "column": 0}, "map": {"version": 3, "file": "LoggerOptions.js", "sourceRoot": "", "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/node_modules/%40opentelemetry/api-logs/src/types/LoggerOptions.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nimport { Attributes } from '@opentelemetry/api';\nexport interface LoggerOptions {\n  /**\n   * The schemaUrl of the tracer or instrumentation library\n   * @default ''\n   */\n  schemaUrl?: string;\n\n  /**\n   * The instrumentation scope attributes to associate with emitted telemetry\n   */\n  scopeAttributes?: Attributes;\n\n  /**\n   * Specifies whether the Trace Context should automatically be passed on to the LogRecords emitted by the Logger.\n   * @default true\n   */\n  includeTraceContext?: boolean;\n}\n"], "names": [], "mappings": "", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1990, "column": 0}, "map": {"version": 3, "file": "AnyValue.js", "sourceRoot": "", "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/node_modules/%40opentelemetry/api-logs/src/types/AnyValue.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { AttributeValue } from '@opentelemetry/api';\n\n/**\n * AnyValueMap is a map from string to AnyValue (attribute value or a nested map)\n */\nexport interface AnyValueMap {\n  [attributeKey: string]: AnyValue | undefined;\n}\n\n/**\n * AnyValue is a either an attribute value or a map of AnyValue(s)\n */\nexport type AnyValue = AttributeValue | AnyValueMap;\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2050, "column": 0}, "map": {"version": 3, "file": "AbstractAsyncHooksContextManager.js", "sourceRoot": "", "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/node_modules/%40opentelemetry/sdk-trace-node/node_modules/%40opentelemetry/context-async-hooks/src/AbstractAsyncHooksContextManager.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { ContextManager, Context } from '@opentelemetry/api';\nimport { EventEmitter } from 'events';\n\ntype Func<T> = (...args: unknown[]) => T;\n\n/**\n * Store a map for each event of all original listeners and their \"patched\"\n * version. So when a listener is removed by the user, the corresponding\n * patched function will be also removed.\n */\ninterface PatchMap {\n  [name: string]: WeakMap<Func<void>, Func<void>>;\n}\n\nconst ADD_LISTENER_METHODS = [\n  'addListener' as const,\n  'on' as const,\n  'once' as const,\n  'prependListener' as const,\n  'prependOnceListener' as const,\n];\n\nexport abstract class AbstractAsyncHooksContextManager\n  implements ContextManager\n{\n  abstract active(): Context;\n\n  abstract with<A extends unknown[], F extends (...args: A) => ReturnType<F>>(\n    context: Context,\n    fn: F,\n    thisArg?: ThisParameterType<F>,\n    ...args: A\n  ): ReturnType<F>;\n\n  abstract enable(): this;\n\n  abstract disable(): this;\n\n  /**\n   * Binds a the certain context or the active one to the target function and then returns the target\n   * @param context A context (span) to be bind to target\n   * @param target a function or event emitter. When target or one of its callbacks is called,\n   *  the provided context will be used as the active context for the duration of the call.\n   */\n  bind<T>(context: Context, target: T): T {\n    if (target instanceof EventEmitter) {\n      return this._bindEventEmitter(context, target);\n    }\n\n    if (typeof target === 'function') {\n      return this._bindFunction(context, target);\n    }\n    return target;\n  }\n\n  private _bindFunction<T extends Function>(context: Context, target: T): T {\n    const manager = this;\n    const contextWrapper = function (this: never, ...args: unknown[]) {\n      return manager.with(context, () => target.apply(this, args));\n    };\n    Object.defineProperty(contextWrapper, 'length', {\n      enumerable: false,\n      configurable: true,\n      writable: false,\n      value: target.length,\n    });\n    /**\n     * It isn't possible to tell Typescript that contextWrapper is the same as T\n     * so we forced to cast as any here.\n     */\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    return contextWrapper as any;\n  }\n\n  /**\n   * By default, EventEmitter call their callback with their context, which we do\n   * not want, instead we will bind a specific context to all callbacks that\n   * go through it.\n   * @param context the context we want to bind\n   * @param ee EventEmitter an instance of EventEmitter to patch\n   */\n  private _bindEventEmitter<T extends EventEmitter>(\n    context: Context,\n    ee: T\n  ): T {\n    const map = this._getPatchMap(ee);\n    if (map !== undefined) return ee;\n    this._createPatchMap(ee);\n\n    // patch methods that add a listener to propagate context\n    ADD_LISTENER_METHODS.forEach(methodName => {\n      if (ee[methodName] === undefined) return;\n      ee[methodName] = this._patchAddListener(ee, ee[methodName], context);\n    });\n    // patch methods that remove a listener\n    if (typeof ee.removeListener === 'function') {\n      ee.removeListener = this._patchRemoveListener(ee, ee.removeListener);\n    }\n    if (typeof ee.off === 'function') {\n      ee.off = this._patchRemoveListener(ee, ee.off);\n    }\n    // patch method that remove all listeners\n    if (typeof ee.removeAllListeners === 'function') {\n      ee.removeAllListeners = this._patchRemoveAllListeners(\n        ee,\n        ee.removeAllListeners\n      );\n    }\n    return ee;\n  }\n\n  /**\n   * Patch methods that remove a given listener so that we match the \"patched\"\n   * version of that listener (the one that propagate context).\n   * @param ee EventEmitter instance\n   * @param original reference to the patched method\n   */\n  private _patchRemoveListener(ee: EventEmitter, original: Function) {\n    const contextManager = this;\n    return function (this: never, event: string, listener: Func<void>) {\n      const events = contextManager._getPatchMap(ee)?.[event];\n      if (events === undefined) {\n        return original.call(this, event, listener);\n      }\n      const patchedListener = events.get(listener);\n      return original.call(this, event, patchedListener || listener);\n    };\n  }\n\n  /**\n   * Patch methods that remove all listeners so we remove our\n   * internal references for a given event.\n   * @param ee EventEmitter instance\n   * @param original reference to the patched method\n   */\n  private _patchRemoveAllListeners(ee: EventEmitter, original: Function) {\n    const contextManager = this;\n    return function (this: never, event: string) {\n      const map = contextManager._getPatchMap(ee);\n      if (map !== undefined) {\n        if (arguments.length === 0) {\n          contextManager._createPatchMap(ee);\n        } else if (map[event] !== undefined) {\n          delete map[event];\n        }\n      }\n      return original.apply(this, arguments);\n    };\n  }\n\n  /**\n   * Patch methods on an event emitter instance that can add listeners so we\n   * can force them to propagate a given context.\n   * @param ee EventEmitter instance\n   * @param original reference to the patched method\n   * @param [context] context to propagate when calling listeners\n   */\n  private _patchAddListener(\n    ee: EventEmitter,\n    original: Function,\n    context: Context\n  ) {\n    const contextManager = this;\n    return function (this: never, event: string, listener: Func<void>) {\n      /**\n       * This check is required to prevent double-wrapping the listener.\n       * The implementation for ee.once wraps the listener and calls ee.on.\n       * Without this check, we would wrap that wrapped listener.\n       * This causes an issue because ee.removeListener depends on the onceWrapper\n       * to properly remove the listener. If we wrap their wrapper, we break\n       * that detection.\n       */\n      if (contextManager._wrapped) {\n        return original.call(this, event, listener);\n      }\n      let map = contextManager._getPatchMap(ee);\n      if (map === undefined) {\n        map = contextManager._createPatchMap(ee);\n      }\n      let listeners = map[event];\n      if (listeners === undefined) {\n        listeners = new WeakMap();\n        map[event] = listeners;\n      }\n      const patchedListener = contextManager.bind(context, listener);\n      // store a weak reference of the user listener to ours\n      listeners.set(listener, patchedListener);\n\n      /**\n       * See comment at the start of this function for the explanation of this property.\n       */\n      contextManager._wrapped = true;\n      try {\n        return original.call(this, event, patchedListener);\n      } finally {\n        contextManager._wrapped = false;\n      }\n    };\n  }\n\n  private _createPatchMap(ee: EventEmitter): PatchMap {\n    const map = Object.create(null);\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    (ee as any)[this._kOtListeners] = map;\n    return map;\n  }\n  private _getPatchMap(ee: EventEmitter): PatchMap | undefined {\n    return (ee as never)[this._kOtListeners];\n  }\n\n  private readonly _kOtListeners = Symbol('OtListeners');\n  private _wrapped = false;\n}\n"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG;;;;AAGH,MAAA,6BAAsC;AAatC,MAAM,oBAAoB,GAAG;IAC3B,aAAsB;IACtB,IAAa;IACb,MAAe;IACf,iBAA0B;IAC1B,qBAA8B;CAC/B,CAAC;AAEF,MAAsB,gCAAgC;IAAtD,aAAA;QA4LmB,IAAA,CAAA,aAAa,GAAG,MAAM,CAAC,aAAa,CAAC,CAAC;QAC/C,IAAA,CAAA,QAAQ,GAAG,KAAK,CAAC;IAC3B,CAAC;IA9KC;;;;;OAKG,CACH,IAAI,CAAI,OAAgB,EAAE,MAAS,EAAA;QACjC,IAAI,MAAM,YAAY,SAAA,YAAY,EAAE;YAClC,OAAO,IAAI,CAAC,iBAAiB,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;SAChD;QAED,IAAI,OAAO,MAAM,KAAK,UAAU,EAAE;YAChC,OAAO,IAAI,CAAC,aAAa,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;SAC5C;QACD,OAAO,MAAM,CAAC;IAChB,CAAC;IAEO,aAAa,CAAqB,OAAgB,EAAE,MAAS,EAAA;QACnE,MAAM,OAAO,GAAG,IAAI,CAAC;QACrB,MAAM,cAAc,GAAG,SAAuB,GAAG,IAAe;YAC9D,OAAO,OAAO,CAAC,IAAI,CAAC,OAAO,EAAE,GAAG,CAAG,CAAD,KAAO,CAAC,KAAK,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC;QAC/D,CAAC,CAAC;QACF,MAAM,CAAC,cAAc,CAAC,cAAc,EAAE,QAAQ,EAAE;YAC9C,UAAU,EAAE,KAAK;YACjB,YAAY,EAAE,IAAI;YAClB,QAAQ,EAAE,KAAK;YACf,KAAK,EAAE,MAAM,CAAC,MAAM;SACrB,CAAC,CAAC;QACH;;;WAGG,CACH,8DAA8D;QAC9D,OAAO,cAAqB,CAAC;IAC/B,CAAC;IAED;;;;;;OAMG,CACK,iBAAiB,CACvB,OAAgB,EAChB,EAAK,EAAA;QAEL,MAAM,GAAG,GAAG,IAAI,CAAC,YAAY,CAAC,EAAE,CAAC,CAAC;QAClC,IAAI,GAAG,KAAK,SAAS,EAAE,OAAO,EAAE,CAAC;QACjC,IAAI,CAAC,eAAe,CAAC,EAAE,CAAC,CAAC;QAEzB,yDAAyD;QACzD,oBAAoB,CAAC,OAAO,EAAC,UAAU,CAAC,EAAE;YACxC,IAAI,EAAE,CAAC,UAAU,CAAC,KAAK,SAAS,EAAE,OAAO;YACzC,EAAE,CAAC,UAAU,CAAC,GAAG,IAAI,CAAC,iBAAiB,CAAC,EAAE,EAAE,EAAE,CAAC,UAAU,CAAC,EAAE,OAAO,CAAC,CAAC;QACvE,CAAC,CAAC,CAAC;QACH,uCAAuC;QACvC,IAAI,OAAO,EAAE,CAAC,cAAc,KAAK,UAAU,EAAE;YAC3C,EAAE,CAAC,cAAc,GAAG,IAAI,CAAC,oBAAoB,CAAC,EAAE,EAAE,EAAE,CAAC,cAAc,CAAC,CAAC;SACtE;QACD,IAAI,OAAO,EAAE,CAAC,GAAG,KAAK,UAAU,EAAE;YAChC,EAAE,CAAC,GAAG,GAAG,IAAI,CAAC,oBAAoB,CAAC,EAAE,EAAE,EAAE,CAAC,GAAG,CAAC,CAAC;SAChD;QACD,yCAAyC;QACzC,IAAI,OAAO,EAAE,CAAC,kBAAkB,KAAK,UAAU,EAAE;YAC/C,EAAE,CAAC,kBAAkB,GAAG,IAAI,CAAC,wBAAwB,CACnD,EAAE,EACF,EAAE,CAAC,kBAAkB,CACtB,CAAC;SACH;QACD,OAAO,EAAE,CAAC;IACZ,CAAC;IAED;;;;;OAKG,CACK,oBAAoB,CAAC,EAAgB,EAAE,QAAkB,EAAA;QAC/D,MAAM,cAAc,GAAG,IAAI,CAAC;QAC5B,OAAO,SAAuB,KAAa,EAAE,QAAoB;;YAC/D,MAAM,MAAM,GAAG,CAAA,KAAA,cAAc,CAAC,YAAY,CAAC,EAAE,CAAC,MAAA,QAAA,OAAA,KAAA,IAAA,KAAA,IAAA,EAAA,CAAG,KAAK,CAAC,CAAC;YACxD,IAAI,MAAM,KAAK,SAAS,EAAE;gBACxB,OAAO,QAAQ,CAAC,IAAI,CAAC,IAAI,EAAE,KAAK,EAAE,QAAQ,CAAC,CAAC;aAC7C;YACD,MAAM,eAAe,GAAG,MAAM,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;YAC7C,OAAO,QAAQ,CAAC,IAAI,CAAC,IAAI,EAAE,KAAK,EAAE,eAAe,IAAI,QAAQ,CAAC,CAAC;QACjE,CAAC,CAAC;IACJ,CAAC;IAED;;;;;OAKG,CACK,wBAAwB,CAAC,EAAgB,EAAE,QAAkB,EAAA;QACnE,MAAM,cAAc,GAAG,IAAI,CAAC;QAC5B,OAAO,SAAuB,KAAa;YACzC,MAAM,GAAG,GAAG,cAAc,CAAC,YAAY,CAAC,EAAE,CAAC,CAAC;YAC5C,IAAI,GAAG,KAAK,SAAS,EAAE;gBACrB,IAAI,SAAS,CAAC,MAAM,KAAK,CAAC,EAAE;oBAC1B,cAAc,CAAC,eAAe,CAAC,EAAE,CAAC,CAAC;iBACpC,MAAM,IAAI,GAAG,CAAC,KAAK,CAAC,KAAK,SAAS,EAAE;oBACnC,OAAO,GAAG,CAAC,KAAK,CAAC,CAAC;iBACnB;aACF;YACD,OAAO,QAAQ,CAAC,KAAK,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;QACzC,CAAC,CAAC;IACJ,CAAC;IAED;;;;;;OAMG,CACK,iBAAiB,CACvB,EAAgB,EAChB,QAAkB,EAClB,OAAgB,EAAA;QAEhB,MAAM,cAAc,GAAG,IAAI,CAAC;QAC5B,OAAO,SAAuB,KAAa,EAAE,QAAoB;YAC/D;;;;;;;eAOG,CACH,IAAI,cAAc,CAAC,QAAQ,EAAE;gBAC3B,OAAO,QAAQ,CAAC,IAAI,CAAC,IAAI,EAAE,KAAK,EAAE,QAAQ,CAAC,CAAC;aAC7C;YACD,IAAI,GAAG,GAAG,cAAc,CAAC,YAAY,CAAC,EAAE,CAAC,CAAC;YAC1C,IAAI,GAAG,KAAK,SAAS,EAAE;gBACrB,GAAG,GAAG,cAAc,CAAC,eAAe,CAAC,EAAE,CAAC,CAAC;aAC1C;YACD,IAAI,SAAS,GAAG,GAAG,CAAC,KAAK,CAAC,CAAC;YAC3B,IAAI,SAAS,KAAK,SAAS,EAAE;gBAC3B,SAAS,GAAG,IAAI,OAAO,EAAE,CAAC;gBAC1B,GAAG,CAAC,KAAK,CAAC,GAAG,SAAS,CAAC;aACxB;YACD,MAAM,eAAe,GAAG,cAAc,CAAC,IAAI,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAC;YAC/D,sDAAsD;YACtD,SAAS,CAAC,GAAG,CAAC,QAAQ,EAAE,eAAe,CAAC,CAAC;YAEzC;;eAEG,CACH,cAAc,CAAC,QAAQ,GAAG,IAAI,CAAC;YAC/B,IAAI;gBACF,OAAO,QAAQ,CAAC,IAAI,CAAC,IAAI,EAAE,KAAK,EAAE,eAAe,CAAC,CAAC;aACpD,QAAS;gBACR,cAAc,CAAC,QAAQ,GAAG,KAAK,CAAC;aACjC;QACH,CAAC,CAAC;IACJ,CAAC;IAEO,eAAe,CAAC,EAAgB,EAAA;QACtC,MAAM,GAAG,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;QAChC,8DAA8D;QAC7D,EAAU,CAAC,IAAI,CAAC,aAAa,CAAC,GAAG,GAAG,CAAC;QACtC,OAAO,GAAG,CAAC;IACb,CAAC;IACO,YAAY,CAAC,EAAgB,EAAA;QACnC,OAAQ,EAAY,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;IAC3C,CAAC;CAIF;AA9LD,QAAA,gCAAA,GAAA,iCA8LC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2234, "column": 0}, "map": {"version": 3, "file": "AsyncHooksContextManager.js", "sourceRoot": "", "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/node_modules/%40opentelemetry/sdk-trace-node/node_modules/%40opentelemetry/context-async-hooks/src/AsyncHooksContextManager.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { Context, ROOT_CONTEXT } from '@opentelemetry/api';\nimport * as asyncHooks from 'async_hooks';\nimport { AbstractAsyncHooksContextManager } from './AbstractAsyncHooksContextManager';\n\nexport class AsyncHooksContextManager extends AbstractAsyncHooksContextManager {\n  private _asyncHook: asyncHooks.AsyncHook;\n  private _contexts: Map<number, Context> = new Map();\n  private _stack: Array<Context | undefined> = [];\n\n  constructor() {\n    super();\n    this._asyncHook = asyncHooks.createHook({\n      init: this._init.bind(this),\n      before: this._before.bind(this),\n      after: this._after.bind(this),\n      destroy: this._destroy.bind(this),\n      promiseResolve: this._destroy.bind(this),\n    });\n  }\n\n  active(): Context {\n    return this._stack[this._stack.length - 1] ?? ROOT_CONTEXT;\n  }\n\n  with<A extends unknown[], F extends (...args: A) => ReturnType<F>>(\n    context: Context,\n    fn: F,\n    thisArg?: ThisParameterType<F>,\n    ...args: A\n  ): ReturnType<F> {\n    this._enterContext(context);\n    try {\n      return fn.call(thisArg!, ...args);\n    } finally {\n      this._exitContext();\n    }\n  }\n\n  enable(): this {\n    this._asyncHook.enable();\n    return this;\n  }\n\n  disable(): this {\n    this._asyncHook.disable();\n    this._contexts.clear();\n    this._stack = [];\n    return this;\n  }\n\n  /**\n   * Init hook will be called when userland create a async context, setting the\n   * context as the current one if it exist.\n   * @param uid id of the async context\n   * @param type the resource type\n   */\n  private _init(uid: number, type: string) {\n    // ignore TIMERWRAP as they combine timers with same timeout which can lead to\n    // false context propagation. TIMERWRAP has been removed in node 11\n    // every timer has it's own `Timeout` resource anyway which is used to propagate\n    // context.\n    if (type === 'TIMERWRAP') return;\n\n    const context = this._stack[this._stack.length - 1];\n    if (context !== undefined) {\n      this._contexts.set(uid, context);\n    }\n  }\n\n  /**\n   * Destroy hook will be called when a given context is no longer used so we can\n   * remove its attached context.\n   * @param uid uid of the async context\n   */\n  private _destroy(uid: number) {\n    this._contexts.delete(uid);\n  }\n\n  /**\n   * Before hook is called just before executing a async context.\n   * @param uid uid of the async context\n   */\n  private _before(uid: number) {\n    const context = this._contexts.get(uid);\n    if (context !== undefined) {\n      this._enterContext(context);\n    }\n  }\n\n  /**\n   * After hook is called just after completing the execution of a async context.\n   */\n  private _after() {\n    this._exitContext();\n  }\n\n  /**\n   * Set the given context as active\n   */\n  private _enterContext(context: Context) {\n    this._stack.push(context);\n  }\n\n  /**\n   * Remove the context at the root of the stack\n   */\n  private _exitContext() {\n    this._stack.pop();\n  }\n}\n"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG;;;;AAEH,MAAA,sCAA2D;AAC3D,MAAA,oCAA0C;;;;;AAC1C,MAAA,mFAAsF;AAEtF,MAAa,wBAAyB,SAAQ,mCAAA,gCAAgC;IAK5E,aAAA;QACE,KAAK,EAAE,CAAC;QAJF,IAAA,CAAA,SAAS,GAAyB,IAAI,GAAG,EAAE,CAAC;QAC5C,IAAA,CAAA,MAAM,GAA+B,EAAE,CAAC;QAI9C,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC,UAAU,CAAC;YACtC,IAAI,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC;YAC3B,MAAM,EAAE,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC;YAC/B,KAAK,EAAE,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC;YAC7B,OAAO,EAAE,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC;YACjC,cAAc,EAAE,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC;SACzC,CAAC,CAAC;IACL,CAAC;IAED,MAAM,GAAA;;QACJ,OAAO,CAAA,KAAA,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,MAAA,QAAA,OAAA,KAAA,IAAA,KAAI,MAAA,YAAY,CAAC;IAC7D,CAAC;IAED,IAAI,CACF,OAAgB,EAChB,EAAK,EACL,OAA8B,EAC9B,GAAG,IAAO,EAAA;QAEV,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC;QAC5B,IAAI;YACF,OAAO,EAAE,CAAC,IAAI,CAAC,OAAQ,EAAE,GAAG,IAAI,CAAC,CAAC;SACnC,QAAS;YACR,IAAI,CAAC,YAAY,EAAE,CAAC;SACrB;IACH,CAAC;IAED,MAAM,GAAA;QACJ,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE,CAAC;QACzB,OAAO,IAAI,CAAC;IACd,CAAC;IAED,OAAO,GAAA;QACL,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE,CAAC;QAC1B,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE,CAAC;QACvB,IAAI,CAAC,MAAM,GAAG,EAAE,CAAC;QACjB,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;;;;OAKG,CACK,KAAK,CAAC,GAAW,EAAE,IAAY,EAAA;QACrC,8EAA8E;QAC9E,mEAAmE;QACnE,gFAAgF;QAChF,WAAW;QACX,IAAI,IAAI,KAAK,WAAW,EAAE,OAAO;QAEjC,MAAM,OAAO,GAAG,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;QACpD,IAAI,OAAO,KAAK,SAAS,EAAE;YACzB,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,GAAG,EAAE,OAAO,CAAC,CAAC;SAClC;IACH,CAAC;IAED;;;;OAIG,CACK,QAAQ,CAAC,GAAW,EAAA;QAC1B,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;IAC7B,CAAC;IAED;;;OAGG,CACK,OAAO,CAAC,GAAW,EAAA;QACzB,MAAM,OAAO,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;QACxC,IAAI,OAAO,KAAK,SAAS,EAAE;YACzB,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC;SAC7B;IACH,CAAC;IAED;;OAEG,CACK,MAAM,GAAA;QACZ,IAAI,CAAC,YAAY,EAAE,CAAC;IACtB,CAAC;IAED;;OAEG,CACK,aAAa,CAAC,OAAgB,EAAA;QACpC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;IAC5B,CAAC;IAED;;OAEG,CACK,YAAY,GAAA;QAClB,IAAI,CAAC,MAAM,CAAC,GAAG,EAAE,CAAC;IACpB,CAAC;CACF;AAzGD,QAAA,wBAAA,GAAA,yBAyGC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2349, "column": 0}, "map": {"version": 3, "file": "AsyncLocalStorageContextManager.js", "sourceRoot": "", "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/node_modules/%40opentelemetry/sdk-trace-node/node_modules/%40opentelemetry/context-async-hooks/src/AsyncLocalStorageContextManager.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { Context, ROOT_CONTEXT } from '@opentelemetry/api';\nimport { AsyncLocalStorage } from 'async_hooks';\nimport { AbstractAsyncHooksContextManager } from './AbstractAsyncHooksContextManager';\n\nexport class AsyncLocalStorageContextManager extends AbstractAsyncHooksContextManager {\n  private _asyncLocalStorage: AsyncLocalStorage<Context>;\n\n  constructor() {\n    super();\n    this._asyncLocalStorage = new AsyncLocalStorage();\n  }\n\n  active(): Context {\n    return this._asyncLocalStorage.getStore() ?? ROOT_CONTEXT;\n  }\n\n  with<A extends unknown[], F extends (...args: A) => ReturnType<F>>(\n    context: Context,\n    fn: F,\n    thisArg?: ThisParameterType<F>,\n    ...args: A\n  ): ReturnType<F> {\n    const cb = thisArg == null ? fn : fn.bind(thisArg);\n    return this._asyncLocalStorage.run(context, cb as never, ...args);\n  }\n\n  enable(): this {\n    return this;\n  }\n\n  disable(): this {\n    this._asyncLocalStorage.disable();\n    return this;\n  }\n}\n"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG;;;;AAEH,MAAA,sCAA2D;AAC3D,MAAA,uCAAgD;;;;;AAChD,MAAA,mFAAsF;AAEtF,MAAa,+BAAgC,SAAQ,mCAAA,gCAAgC;IAGnF,aAAA;QACE,KAAK,EAAE,CAAC;QACR,IAAI,CAAC,kBAAkB,GAAG,IAAI,cAAA,iBAAiB,EAAE,CAAC;IACpD,CAAC;IAED,MAAM,GAAA;;QACJ,OAAO,CAAA,KAAA,IAAI,CAAC,kBAAkB,CAAC,QAAQ,EAAE,MAAA,QAAA,OAAA,KAAA,IAAA,KAAI,MAAA,YAAY,CAAC;IAC5D,CAAC;IAED,IAAI,CACF,OAAgB,EAChB,EAAK,EACL,OAA8B,EAC9B,GAAG,IAAO,EAAA;QAEV,MAAM,EAAE,GAAG,OAAO,IAAI,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QACnD,OAAO,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,OAAO,EAAE,EAAW,EAAE,GAAG,IAAI,CAAC,CAAC;IACpE,CAAC;IAED,MAAM,GAAA;QACJ,OAAO,IAAI,CAAC;IACd,CAAC;IAED,OAAO,GAAA;QACL,IAAI,CAAC,kBAAkB,CAAC,OAAO,EAAE,CAAC;QAClC,OAAO,IAAI,CAAC;IACd,CAAC;CACF;AA9BD,QAAA,+BAAA,GAAA,gCA8BC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2402, "column": 0}, "map": {"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/node_modules/%40opentelemetry/sdk-trace-node/node_modules/%40opentelemetry/context-async-hooks/src/index.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nexport { AsyncHooksContextManager } from './AsyncHooksContextManager';\nexport { AsyncLocalStorageContextManager } from './AsyncLocalStorageContextManager';\n"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG;;;;AAEH,IAAA,mEAAsE;AAA7D,OAAA,cAAA,CAAA,SAAA,4BAAA;IAAA,YAAA;IAAA,KAAA;QAAA,OAAA,2BAAA,wBAAwB;IAAA;AAAA,GAAA;AACjC,IAAA,iFAAoF;AAA3E,OAAA,cAAA,CAAA,SAAA,mCAAA;IAAA,YAAA;IAAA,KAAA;QAAA,OAAA,kCAAA,+BAA+B;IAAA;AAAA,GAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2441, "column": 0}, "map": {"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/node_modules/%40opentelemetry/propagator-b3/src/index.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nexport * from './B3Propagator';\nexport * from './constants';\nexport * from './types';\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2474, "column": 0}, "map": {"version": 3, "file": "common.js", "sourceRoot": "", "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/node_modules/%40opentelemetry/propagator-b3/src/common.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { createContextKey } from '@opentelemetry/api';\n\n/** shared context for storing an extracted b3 debug flag */\nexport const B3_DEBUG_FLAG_KEY = createContextKey(\n  'OpenTelemetry Context Key B3 Debug Flag'\n);\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;AAEH,OAAO,EAAE,gBAAgB,EAAE,MAAM,oBAAoB,CAAC;;AAG/C,IAAM,iBAAiB,uLAAG,mBAAA,AAAgB,EAC/C,yCAAyC,CAC1C,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2500, "column": 0}, "map": {"version": 3, "file": "constants.js", "sourceRoot": "", "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/node_modules/%40opentelemetry/propagator-b3/src/constants.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n/** B3 single-header key */\nexport const B3_CONTEXT_HEADER = 'b3';\n\n/* b3 multi-header keys */\nexport const X_B3_TRACE_ID = 'x-b3-traceid';\nexport const X_B3_SPAN_ID = 'x-b3-spanid';\nexport const X_B3_SAMPLED = 'x-b3-sampled';\nexport const X_B3_PARENT_SPAN_ID = 'x-b3-parentspanid';\nexport const X_B3_FLAGS = 'x-b3-flags';\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG,CAEH,yBAAA,EAA2B;;;;;;;;AACpB,IAAM,iBAAiB,GAAG,IAAI,CAAC;AAG/B,IAAM,aAAa,GAAG,cAAc,CAAC;AACrC,IAAM,YAAY,GAAG,aAAa,CAAC;AACnC,IAAM,YAAY,GAAG,cAAc,CAAC;AACpC,IAAM,mBAAmB,GAAG,mBAAmB,CAAC;AAChD,IAAM,UAAU,GAAG,YAAY,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2534, "column": 0}, "map": {"version": 3, "file": "B3MultiPropagator.js", "sourceRoot": "", "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/node_modules/%40opentelemetry/propagator-b3/src/B3MultiPropagator.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  Context,\n  isSpanContextValid,\n  isValidSpanId,\n  isValidTraceId,\n  trace,\n  TextMapGetter,\n  TextMapPropagator,\n  TextMapSetter,\n  TraceFlags,\n} from '@opentelemetry/api';\nimport { isTracingSuppressed } from '@opentelemetry/core';\nimport { B3_DEBUG_FLAG_KEY } from './common';\nimport {\n  X_B3_FLAGS,\n  X_B3_PARENT_SPAN_ID,\n  X_B3_SAMPLED,\n  X_B3_SPAN_ID,\n  X_B3_TRACE_ID,\n} from './constants';\n\nconst VALID_SAMPLED_VALUES = new Set([true, 'true', 'True', '1', 1]);\nconst VALID_UNSAMPLED_VALUES = new Set([false, 'false', 'False', '0', 0]);\n\nfunction isValidSampledValue(sampled: TraceFlags | undefined): boolean {\n  return sampled === TraceFlags.SAMPLED || sampled === TraceFlags.NONE;\n}\n\nfunction parseHeader(header: unknown) {\n  return Array.isArray(header) ? header[0] : header;\n}\n\nfunction getHeaderValue(carrier: unknown, getter: TextMapGetter, key: string) {\n  const header = getter.get(carrier, key);\n  return parseHeader(header);\n}\n\nfunction getTraceId(carrier: unknown, getter: TextMapGetter): string {\n  const traceId = getHeaderValue(carrier, getter, X_B3_TRACE_ID);\n  if (typeof traceId === 'string') {\n    return traceId.padStart(32, '0');\n  }\n  return '';\n}\n\nfunction getSpanId(carrier: unknown, getter: TextMapGetter): string {\n  const spanId = getHeaderValue(carrier, getter, X_B3_SPAN_ID);\n  if (typeof spanId === 'string') {\n    return spanId;\n  }\n  return '';\n}\n\nfunction getDebug(carrier: unknown, getter: TextMapGetter): string | undefined {\n  const debug = getHeaderValue(carrier, getter, X_B3_FLAGS);\n  return debug === '1' ? '1' : undefined;\n}\n\nfunction getTraceFlags(\n  carrier: unknown,\n  getter: TextMapGetter\n): TraceFlags | undefined {\n  const traceFlags = getHeaderValue(carrier, getter, X_B3_SAMPLED);\n  const debug = getDebug(carrier, getter);\n  if (debug === '1' || VALID_SAMPLED_VALUES.has(traceFlags)) {\n    return TraceFlags.SAMPLED;\n  }\n  if (traceFlags === undefined || VALID_UNSAMPLED_VALUES.has(traceFlags)) {\n    return TraceFlags.NONE;\n  }\n  // This indicates to isValidSampledValue that this is not valid\n  return;\n}\n\n/**\n * Propagator for the B3 multiple-header HTTP format.\n * Based on: https://github.com/openzipkin/b3-propagation\n */\nexport class B3MultiPropagator implements TextMapPropagator {\n  inject(context: Context, carrier: unknown, setter: TextMapSetter): void {\n    const spanContext = trace.getSpanContext(context);\n    if (\n      !spanContext ||\n      !isSpanContextValid(spanContext) ||\n      isTracingSuppressed(context)\n    )\n      return;\n\n    const debug = context.getValue(B3_DEBUG_FLAG_KEY);\n    setter.set(carrier, X_B3_TRACE_ID, spanContext.traceId);\n    setter.set(carrier, X_B3_SPAN_ID, spanContext.spanId);\n    // According to the B3 spec, if the debug flag is set,\n    // the sampled flag shouldn't be propagated as well.\n    if (debug === '1') {\n      setter.set(carrier, X_B3_FLAGS, debug);\n    } else if (spanContext.traceFlags !== undefined) {\n      // We set the header only if there is an existing sampling decision.\n      // Otherwise we will omit it => Absent.\n      setter.set(\n        carrier,\n        X_B3_SAMPLED,\n        (TraceFlags.SAMPLED & spanContext.traceFlags) === TraceFlags.SAMPLED\n          ? '1'\n          : '0'\n      );\n    }\n  }\n\n  extract(context: Context, carrier: unknown, getter: TextMapGetter): Context {\n    const traceId = getTraceId(carrier, getter);\n    const spanId = getSpanId(carrier, getter);\n    const traceFlags = getTraceFlags(carrier, getter) as TraceFlags;\n    const debug = getDebug(carrier, getter);\n\n    if (\n      isValidTraceId(traceId) &&\n      isValidSpanId(spanId) &&\n      isValidSampledValue(traceFlags)\n    ) {\n      context = context.setValue(B3_DEBUG_FLAG_KEY, debug);\n      return trace.setSpanContext(context, {\n        traceId,\n        spanId,\n        isRemote: true,\n        traceFlags,\n      });\n    }\n    return context;\n  }\n\n  fields(): string[] {\n    return [\n      X_B3_TRACE_ID,\n      X_B3_SPAN_ID,\n      X_B3_FLAGS,\n      X_B3_SAMPLED,\n      X_B3_PARENT_SPAN_ID,\n    ];\n  }\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;AAEH,OAAO,EAEL,kBAAkB,EAClB,aAAa,EACb,cAAc,EACd,KAAK,EAIL,UAAU,GACX,MAAM,oBAAoB,CAAC;;;AAC5B,OAAO,EAAE,mBAAmB,EAAE,MAAM,qBAAqB,CAAC;AAC1D,OAAO,EAAE,iBAAiB,EAAE,MAAM,UAAU,CAAC;AAC7C,OAAO,EACL,UAAU,EACV,mBAAmB,EACnB,YAAY,EACZ,YAAY,EACZ,aAAa,GACd,MAAM,aAAa,CAAC;;;;;AAErB,IAAM,oBAAoB,GAAG,IAAI,GAAG,CAAC;IAAC,IAAI;IAAE,MAAM;IAAE,MAAM;IAAE,GAAG;IAAE,CAAC;CAAC,CAAC,CAAC;AACrE,IAAM,sBAAsB,GAAG,IAAI,GAAG,CAAC;IAAC,KAAK;IAAE,OAAO;IAAE,OAAO;IAAE,GAAG;IAAE,CAAC;CAAC,CAAC,CAAC;AAE1E,SAAS,mBAAmB,CAAC,OAA+B;IAC1D,OAAO,OAAO,uLAAK,aAAU,CAAC,OAAO,IAAI,OAAO,uLAAK,aAAU,CAAC,IAAI,CAAC;AACvE,CAAC;AAED,SAAS,WAAW,CAAC,MAAe;IAClC,OAAO,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC;AACpD,CAAC;AAED,SAAS,cAAc,CAAC,OAAgB,EAAE,MAAqB,EAAE,GAAW;IAC1E,IAAM,MAAM,GAAG,MAAM,CAAC,GAAG,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC;IACxC,OAAO,WAAW,CAAC,MAAM,CAAC,CAAC;AAC7B,CAAC;AAED,SAAS,UAAU,CAAC,OAAgB,EAAE,MAAqB;IACzD,IAAM,OAAO,GAAG,cAAc,CAAC,OAAO,EAAE,MAAM,sLAAE,gBAAa,CAAC,CAAC;IAC/D,IAAI,OAAO,OAAO,KAAK,QAAQ,EAAE;QAC/B,OAAO,OAAO,CAAC,QAAQ,CAAC,EAAE,EAAE,GAAG,CAAC,CAAC;KAClC;IACD,OAAO,EAAE,CAAC;AACZ,CAAC;AAED,SAAS,SAAS,CAAC,OAAgB,EAAE,MAAqB;IACxD,IAAM,MAAM,GAAG,cAAc,CAAC,OAAO,EAAE,MAAM,sLAAE,eAAY,CAAC,CAAC;IAC7D,IAAI,OAAO,MAAM,KAAK,QAAQ,EAAE;QAC9B,OAAO,MAAM,CAAC;KACf;IACD,OAAO,EAAE,CAAC;AACZ,CAAC;AAED,SAAS,QAAQ,CAAC,OAAgB,EAAE,MAAqB;IACvD,IAAM,KAAK,GAAG,cAAc,CAAC,OAAO,EAAE,MAAM,sLAAE,aAAU,CAAC,CAAC;IAC1D,OAAO,KAAK,KAAK,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,SAAS,CAAC;AACzC,CAAC;AAED,SAAS,aAAa,CACpB,OAAgB,EAChB,MAAqB;IAErB,IAAM,UAAU,GAAG,cAAc,CAAC,OAAO,EAAE,MAAM,sLAAE,eAAY,CAAC,CAAC;IACjE,IAAM,KAAK,GAAG,QAAQ,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;IACxC,IAAI,KAAK,KAAK,GAAG,IAAI,oBAAoB,CAAC,GAAG,CAAC,UAAU,CAAC,EAAE;QACzD,yLAAO,aAAU,CAAC,OAAO,CAAC;KAC3B;IACD,IAAI,UAAU,KAAK,SAAS,IAAI,sBAAsB,CAAC,GAAG,CAAC,UAAU,CAAC,EAAE;QACtE,OAAO,+LAAU,CAAC,IAAI,CAAC;KACxB;IACD,+DAA+D;IAC/D,OAAO;AACT,CAAC;AAED;;;GAGG,CACH,IAAA,oBAAA;IAAA,SAAA,qBA6DA,CAAC;IA5DC,kBAAA,SAAA,CAAA,MAAM,GAAN,SAAO,OAAgB,EAAE,OAAgB,EAAE,MAAqB;QAC9D,IAAM,WAAW,6KAAG,QAAK,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC;QAClD,IACE,CAAC,WAAW,IACZ,gMAAC,qBAAA,AAAkB,EAAC,WAAW,CAAC,0PAChC,sBAAA,AAAmB,EAAC,OAAO,CAAC,EAE5B,OAAO;QAET,IAAM,KAAK,GAAG,OAAO,CAAC,QAAQ,kLAAC,oBAAiB,CAAC,CAAC;QAClD,MAAM,CAAC,GAAG,CAAC,OAAO,sLAAE,gBAAa,EAAE,WAAW,CAAC,OAAO,CAAC,CAAC;QACxD,MAAM,CAAC,GAAG,CAAC,OAAO,sLAAE,eAAY,EAAE,WAAW,CAAC,MAAM,CAAC,CAAC;QACtD,sDAAsD;QACtD,oDAAoD;QACpD,IAAI,KAAK,KAAK,GAAG,EAAE;YACjB,MAAM,CAAC,GAAG,CAAC,OAAO,sLAAE,aAAU,EAAE,KAAK,CAAC,CAAC;SACxC,MAAM,IAAI,WAAW,CAAC,UAAU,KAAK,SAAS,EAAE;YAC/C,oEAAoE;YACpE,uCAAuC;YACvC,MAAM,CAAC,GAAG,CACR,OAAO,sLACP,eAAY,EACZ,mLAAC,aAAU,CAAC,OAAO,GAAG,WAAW,CAAC,UAAU,CAAC,uLAAK,aAAU,CAAC,OAAO,GAChE,GAAG,GACH,GAAG,CACR,CAAC;SACH;IACH,CAAC;IAED,kBAAA,SAAA,CAAA,OAAO,GAAP,SAAQ,OAAgB,EAAE,OAAgB,EAAE,MAAqB;QAC/D,IAAM,OAAO,GAAG,UAAU,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;QAC5C,IAAM,MAAM,GAAG,SAAS,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;QAC1C,IAAM,UAAU,GAAG,aAAa,CAAC,OAAO,EAAE,MAAM,CAAe,CAAC;QAChE,IAAM,KAAK,GAAG,QAAQ,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;QAExC,mMACE,iBAAA,AAAc,EAAC,OAAO,CAAC,mMACvB,gBAAA,AAAa,EAAC,MAAM,CAAC,IACrB,mBAAmB,CAAC,UAAU,CAAC,EAC/B;YACA,OAAO,GAAG,OAAO,CAAC,QAAQ,kLAAC,oBAAiB,EAAE,KAAK,CAAC,CAAC;YACrD,iLAAO,QAAK,CAAC,cAAc,CAAC,OAAO,EAAE;gBACnC,OAAO,EAAA,OAAA;gBACP,MAAM,EAAA,MAAA;gBACN,QAAQ,EAAE,IAAI;gBACd,UAAU,EAAA,UAAA;aACX,CAAC,CAAC;SACJ;QACD,OAAO,OAAO,CAAC;IACjB,CAAC;IAED,kBAAA,SAAA,CAAA,MAAM,GAAN;QACE,OAAO;gMACL,gBAAa;gMACb,eAAY;gMACZ,aAAU;gMACV,eAAY;gMACZ,sBAAmB;SACpB,CAAC;IACJ,CAAC;IACH,OAAA,iBAAC;AAAD,CAAC,AA7DD,IA6DC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2671, "column": 0}, "map": {"version": 3, "file": "B3SinglePropagator.js", "sourceRoot": "", "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/node_modules/%40opentelemetry/propagator-b3/src/B3SinglePropagator.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  Context,\n  isSpanContextValid,\n  isValidSpanId,\n  isValidTraceId,\n  TextMapGetter,\n  TextMapPropagator,\n  TextMapSetter,\n  trace,\n  TraceFlags,\n} from '@opentelemetry/api';\nimport { isTracingSuppressed } from '@opentelemetry/core';\nimport { B3_DEBUG_FLAG_KEY } from './common';\nimport { B3_CONTEXT_HEADER } from './constants';\n\nconst B3_CONTEXT_REGEX =\n  /((?:[0-9a-f]{16}){1,2})-([0-9a-f]{16})(?:-([01d](?![0-9a-f])))?(?:-([0-9a-f]{16}))?/;\nconst PADDING = '0'.repeat(16);\nconst SAMPLED_VALUES = new Set(['d', '1']);\nconst DEBUG_STATE = 'd';\n\nfunction convertToTraceId128(traceId: string): string {\n  return traceId.length === 32 ? traceId : `${PADDING}${traceId}`;\n}\n\nfunction convertToTraceFlags(samplingState: string | undefined): TraceFlags {\n  if (samplingState && SAMPLED_VALUES.has(samplingState)) {\n    return TraceFlags.SAMPLED;\n  }\n  return TraceFlags.NONE;\n}\n\n/**\n * Propagator for the B3 single-header HTTP format.\n * Based on: https://github.com/openzipkin/b3-propagation\n */\nexport class B3SinglePropagator implements TextMapPropagator {\n  inject(context: Context, carrier: unknown, setter: TextMapSetter): void {\n    const spanContext = trace.getSpanContext(context);\n    if (\n      !spanContext ||\n      !isSpanContextValid(spanContext) ||\n      isTracingSuppressed(context)\n    )\n      return;\n\n    const samplingState =\n      context.getValue(B3_DEBUG_FLAG_KEY) || spanContext.traceFlags & 0x1;\n    const value = `${spanContext.traceId}-${spanContext.spanId}-${samplingState}`;\n    setter.set(carrier, B3_CONTEXT_HEADER, value);\n  }\n\n  extract(context: Context, carrier: unknown, getter: TextMapGetter): Context {\n    const header = getter.get(carrier, B3_CONTEXT_HEADER);\n    const b3Context = Array.isArray(header) ? header[0] : header;\n    if (typeof b3Context !== 'string') return context;\n\n    const match = b3Context.match(B3_CONTEXT_REGEX);\n    if (!match) return context;\n\n    const [, extractedTraceId, spanId, samplingState] = match;\n    const traceId = convertToTraceId128(extractedTraceId);\n\n    if (!isValidTraceId(traceId) || !isValidSpanId(spanId)) return context;\n\n    const traceFlags = convertToTraceFlags(samplingState);\n\n    if (samplingState === DEBUG_STATE) {\n      context = context.setValue(B3_DEBUG_FLAG_KEY, samplingState);\n    }\n\n    return trace.setSpanContext(context, {\n      traceId,\n      spanId,\n      isRemote: true,\n      traceFlags,\n    });\n  }\n\n  fields(): string[] {\n    return [B3_CONTEXT_HEADER];\n  }\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;;;AAEH,OAAO,EAEL,kBAAkB,EAClB,aAAa,EACb,cAAc,EAId,KAAK,EACL,UAAU,GACX,MAAM,oBAAoB,CAAC;AAC5B,OAAO,EAAE,mBAAmB,EAAE,MAAM,qBAAqB,CAAC;AAC1D,OAAO,EAAE,iBAAiB,EAAE,MAAM,UAAU,CAAC;AAC7C,OAAO,EAAE,iBAAiB,EAAE,MAAM,aAAa,CAAC;;;;;;;;;;;;;;;;;;;;;;;;AAEhD,IAAM,gBAAgB,GACpB,qFAAqF,CAAC;AACxF,IAAM,OAAO,GAAG,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;AAC/B,IAAM,cAAc,GAAG,IAAI,GAAG,CAAC;IAAC,GAAG;IAAE,GAAG;CAAC,CAAC,CAAC;AAC3C,IAAM,WAAW,GAAG,GAAG,CAAC;AAExB,SAAS,mBAAmB,CAAC,OAAe;IAC1C,OAAO,OAAO,CAAC,MAAM,KAAK,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,KAAG,OAAO,GAAG,OAAS,CAAC;AAClE,CAAC;AAED,SAAS,mBAAmB,CAAC,aAAiC;IAC5D,IAAI,aAAa,IAAI,cAAc,CAAC,GAAG,CAAC,aAAa,CAAC,EAAE;QACtD,yLAAO,aAAU,CAAC,OAAO,CAAC;KAC3B;IACD,yLAAO,aAAU,CAAC,IAAI,CAAC;AACzB,CAAC;AAED;;;GAGG,CACH,IAAA,qBAAA;IAAA,SAAA,sBA8CA,CAAC;IA7CC,mBAAA,SAAA,CAAA,MAAM,GAAN,SAAO,OAAgB,EAAE,OAAgB,EAAE,MAAqB;QAC9D,IAAM,WAAW,6KAAG,QAAK,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC;QAClD,IACE,CAAC,WAAW,IACZ,gMAAC,qBAAA,AAAkB,EAAC,WAAW,CAAC,0PAChC,sBAAA,AAAmB,EAAC,OAAO,CAAC,EAE5B,OAAO;QAET,IAAM,aAAa,GACjB,OAAO,CAAC,QAAQ,kLAAC,oBAAiB,CAAC,IAAI,WAAW,CAAC,UAAU,GAAG,GAAG,CAAC;QACtE,IAAM,KAAK,GAAM,WAAW,CAAC,OAAO,GAAA,MAAI,WAAW,CAAC,MAAM,GAAA,MAAI,aAAe,CAAC;QAC9E,MAAM,CAAC,GAAG,CAAC,OAAO,sLAAE,oBAAiB,EAAE,KAAK,CAAC,CAAC;IAChD,CAAC;IAED,mBAAA,SAAA,CAAA,OAAO,GAAP,SAAQ,OAAgB,EAAE,OAAgB,EAAE,MAAqB;QAC/D,IAAM,MAAM,GAAG,MAAM,CAAC,GAAG,CAAC,OAAO,sLAAE,oBAAiB,CAAC,CAAC;QACtD,IAAM,SAAS,GAAG,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC;QAC7D,IAAI,OAAO,SAAS,KAAK,QAAQ,EAAE,OAAO,OAAO,CAAC;QAElD,IAAM,KAAK,GAAG,SAAS,CAAC,KAAK,CAAC,gBAAgB,CAAC,CAAC;QAChD,IAAI,CAAC,KAAK,EAAE,OAAO,OAAO,CAAC;QAErB,IAAA,KAAA,OAA8C,KAAK,EAAA,EAAA,EAAhD,gBAAgB,GAAA,EAAA,CAAA,EAAA,EAAE,MAAM,GAAA,EAAA,CAAA,EAAA,EAAE,aAAa,GAAA,EAAA,CAAA,EAAS,CAAC;QAC1D,IAAM,OAAO,GAAG,mBAAmB,CAAC,gBAAgB,CAAC,CAAC;QAEtD,IAAI,gMAAC,iBAAA,AAAc,EAAC,OAAO,CAAC,IAAI,CAAC,+MAAA,AAAa,EAAC,MAAM,CAAC,EAAE,OAAO,OAAO,CAAC;QAEvE,IAAM,UAAU,GAAG,mBAAmB,CAAC,aAAa,CAAC,CAAC;QAEtD,IAAI,aAAa,KAAK,WAAW,EAAE;YACjC,OAAO,GAAG,OAAO,CAAC,QAAQ,iLAAC,qBAAiB,EAAE,aAAa,CAAC,CAAC;SAC9D;QAED,iLAAO,QAAK,CAAC,cAAc,CAAC,OAAO,EAAE;YACnC,OAAO,EAAA,OAAA;YACP,MAAM,EAAA,MAAA;YACN,QAAQ,EAAE,IAAI;YACd,UAAU,EAAA,UAAA;SACX,CAAC,CAAC;IACL,CAAC;IAED,mBAAA,SAAA,CAAA,MAAM,GAAN;QACE,OAAO;gMAAC,oBAAiB;SAAC,CAAC;IAC7B,CAAC;IACH,OAAA,kBAAC;AAAD,CAAC,AA9CD,IA8CC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2780, "column": 0}, "map": {"version": 3, "file": "types.js", "sourceRoot": "", "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/node_modules/%40opentelemetry/propagator-b3/src/types.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n/** Enumeration of B3 inject encodings */\nexport enum B3InjectEncoding {\n  SINGLE_HEADER,\n  MULTI_HEADER,\n}\n\n/** Configuration for the B3Propagator */\nexport interface B3PropagatorConfig {\n  injectEncoding?: B3InjectEncoding;\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG,CAEH,uCAAA,EAAyC;;;AACzC,IAAY,gBAGX;AAHD,CAAA,SAAY,gBAAgB;IAC1B,gBAAA,CAAA,gBAAA,CAAA,gBAAA,GAAA,EAAA,GAAA,eAAa,CAAA;IACb,gBAAA,CAAA,gBAAA,CAAA,eAAA,GAAA,EAAA,GAAA,cAAY,CAAA;AACd,CAAC,EAHW,gBAAgB,IAAA,CAAhB,gBAAgB,GAAA,CAAA,CAAA,GAG3B", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2808, "column": 0}, "map": {"version": 3, "file": "B3Propagator.js", "sourceRoot": "", "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/node_modules/%40opentelemetry/propagator-b3/src/B3Propagator.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  Context,\n  TextMapGetter,\n  TextMapPropagator,\n  TextMapSetter,\n} from '@opentelemetry/api';\nimport { isTracingSuppressed } from '@opentelemetry/core';\nimport { B3MultiPropagator } from './B3MultiPropagator';\nimport { B3SinglePropagator } from './B3SinglePropagator';\nimport { B3_CONTEXT_HEADER } from './constants';\nimport { B3InjectEncoding, B3PropagatorConfig } from './types';\n\n/**\n * Propagator that extracts B3 context in both single and multi-header variants,\n * with configurable injection format defaulting to B3 single-header. Due to\n * the asymmetry in injection and extraction formats this is not suitable to\n * be implemented as a composite propagator.\n * Based on: https://github.com/openzipkin/b3-propagation\n */\nexport class B3Propagator implements TextMapPropagator {\n  private readonly _b3MultiPropagator: B3MultiPropagator =\n    new B3MultiPropagator();\n  private readonly _b3SinglePropagator: B3SinglePropagator =\n    new B3SinglePropagator();\n  private readonly _inject: (\n    context: Context,\n    carrier: unknown,\n    setter: TextMapSetter\n  ) => void;\n  public readonly _fields: string[];\n\n  constructor(config: B3PropagatorConfig = {}) {\n    if (config.injectEncoding === B3InjectEncoding.MULTI_HEADER) {\n      this._inject = this._b3MultiPropagator.inject;\n      this._fields = this._b3MultiPropagator.fields();\n    } else {\n      this._inject = this._b3SinglePropagator.inject;\n      this._fields = this._b3SinglePropagator.fields();\n    }\n  }\n\n  inject(context: Context, carrier: unknown, setter: TextMapSetter): void {\n    if (isTracingSuppressed(context)) {\n      return;\n    }\n    this._inject(context, carrier, setter);\n  }\n\n  extract(context: Context, carrier: unknown, getter: TextMapGetter): Context {\n    const header = getter.get(carrier, B3_CONTEXT_HEADER);\n    const b3Context = Array.isArray(header) ? header[0] : header;\n\n    if (b3Context) {\n      return this._b3SinglePropagator.extract(context, carrier, getter);\n    } else {\n      return this._b3MultiPropagator.extract(context, carrier, getter);\n    }\n  }\n\n  fields(): string[] {\n    return this._fields;\n  }\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;AAQH,OAAO,EAAE,mBAAmB,EAAE,MAAM,qBAAqB,CAAC;AAC1D,OAAO,EAAE,iBAAiB,EAAE,MAAM,qBAAqB,CAAC;AACxD,OAAO,EAAE,kBAAkB,EAAE,MAAM,sBAAsB,CAAC;AAC1D,OAAO,EAAE,iBAAiB,EAAE,MAAM,aAAa,CAAC;AAChD,OAAO,EAAE,gBAAgB,EAAsB,MAAM,SAAS,CAAC;;;;;;AAE/D;;;;;;GAMG,CACH,IAAA,eAAA;IAYE,SAAA,aAAY,MAA+B;QAA/B,IAAA,WAAA,KAAA,GAAA;YAAA,SAAA,CAAA,CAA+B;QAAA;QAX1B,IAAA,CAAA,kBAAkB,GACjC,gMAAI,oBAAiB,EAAE,CAAC;QACT,IAAA,CAAA,mBAAmB,GAClC,iMAAI,qBAAkB,EAAE,CAAC;QASzB,IAAI,MAAM,CAAC,cAAc,qLAAK,mBAAgB,CAAC,YAAY,EAAE;YAC3D,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,kBAAkB,CAAC,MAAM,CAAC;YAC9C,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,kBAAkB,CAAC,MAAM,EAAE,CAAC;SACjD,MAAM;YACL,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,mBAAmB,CAAC,MAAM,CAAC;YAC/C,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,mBAAmB,CAAC,MAAM,EAAE,CAAC;SAClD;IACH,CAAC;IAED,aAAA,SAAA,CAAA,MAAM,GAAN,SAAO,OAAgB,EAAE,OAAgB,EAAE,MAAqB;QAC9D,IAAI,4QAAA,AAAmB,EAAC,OAAO,CAAC,EAAE;YAChC,OAAO;SACR;QACD,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,OAAO,EAAE,MAAM,CAAC,CAAC;IACzC,CAAC;IAED,aAAA,SAAA,CAAA,OAAO,GAAP,SAAQ,OAAgB,EAAE,OAAgB,EAAE,MAAqB;QAC/D,IAAM,MAAM,GAAG,MAAM,CAAC,GAAG,CAAC,OAAO,sLAAE,oBAAiB,CAAC,CAAC;QACtD,IAAM,SAAS,GAAG,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC;QAE7D,IAAI,SAAS,EAAE;YACb,OAAO,IAAI,CAAC,mBAAmB,CAAC,OAAO,CAAC,OAAO,EAAE,OAAO,EAAE,MAAM,CAAC,CAAC;SACnE,MAAM;YACL,OAAO,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC,OAAO,EAAE,OAAO,EAAE,MAAM,CAAC,CAAC;SAClE;IACH,CAAC;IAED,aAAA,SAAA,CAAA,MAAM,GAAN;QACE,OAAO,IAAI,CAAC,OAAO,CAAC;IACtB,CAAC;IACH,OAAA,YAAC;AAAD,CAAC,AA3CD,IA2CC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2922, "column": 0}, "map": {"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/node_modules/%40opentelemetry/propagator-jaeger/src/index.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nexport * from './JaegerPropagator';\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2953, "column": 0}, "map": {"version": 3, "file": "JaegerPropagator.js", "sourceRoot": "", "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/node_modules/%40opentelemetry/propagator-jaeger/src/JaegerPropagator.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  Context,\n  SpanContext,\n  TextMapGetter,\n  TextMapPropagator,\n  TextMapSetter,\n  propagation,\n  trace,\n  TraceFlags,\n} from '@opentelemetry/api';\nimport { isTracingSuppressed } from '@opentelemetry/core';\nimport { JaegerPropagatorConfig } from './types';\n\nexport const UBER_TRACE_ID_HEADER = 'uber-trace-id';\nexport const UBER_BAGGAGE_HEADER_PREFIX = 'uberctx';\n\n/**\n * Propagates {@link SpanContext} through Trace Context format propagation.\n * {trace-id}:{span-id}:{parent-span-id}:{flags}\n * {trace-id}\n * 64-bit or 128-bit random number in base16 format.\n * Can be variable length, shorter values are 0-padded on the left.\n * Value of 0 is invalid.\n * {span-id}\n * 64-bit random number in base16 format.\n * {parent-span-id}\n * Set to 0 because this field is deprecated.\n * {flags}\n * One byte bitmap, as two hex digits.\n * Inspired by jaeger-client-node project.\n */\nexport class JaegerPropagator implements TextMapPropagator {\n  private readonly _jaegerTraceHeader: string;\n  private readonly _jaegerBaggageHeaderPrefix: string;\n\n  constructor(customTraceHeader?: string);\n  constructor(config?: JaegerPropagatorConfig);\n  constructor(config?: JaegerPropagatorConfig | string) {\n    if (typeof config === 'string') {\n      this._jaegerTraceHeader = config;\n      this._jaegerBaggageHeaderPrefix = UBER_BAGGAGE_HEADER_PREFIX;\n    } else {\n      this._jaegerTraceHeader =\n        config?.customTraceHeader || UBER_TRACE_ID_HEADER;\n      this._jaegerBaggageHeaderPrefix =\n        config?.customBaggageHeaderPrefix || UBER_BAGGAGE_HEADER_PREFIX;\n    }\n  }\n\n  inject(context: Context, carrier: unknown, setter: TextMapSetter): void {\n    const spanContext = trace.getSpanContext(context);\n    const baggage = propagation.getBaggage(context);\n    if (spanContext && isTracingSuppressed(context) === false) {\n      const traceFlags = `0${(\n        spanContext.traceFlags || TraceFlags.NONE\n      ).toString(16)}`;\n\n      setter.set(\n        carrier,\n        this._jaegerTraceHeader,\n        `${spanContext.traceId}:${spanContext.spanId}:0:${traceFlags}`\n      );\n    }\n\n    if (baggage) {\n      for (const [key, entry] of baggage.getAllEntries()) {\n        setter.set(\n          carrier,\n          `${this._jaegerBaggageHeaderPrefix}-${key}`,\n          encodeURIComponent(entry.value)\n        );\n      }\n    }\n  }\n\n  extract(context: Context, carrier: unknown, getter: TextMapGetter): Context {\n    const uberTraceIdHeader = getter.get(carrier, this._jaegerTraceHeader);\n    const uberTraceId = Array.isArray(uberTraceIdHeader)\n      ? uberTraceIdHeader[0]\n      : uberTraceIdHeader;\n    const baggageValues = getter\n      .keys(carrier)\n      .filter(key => key.startsWith(`${this._jaegerBaggageHeaderPrefix}-`))\n      .map(key => {\n        const value = getter.get(carrier, key);\n        return {\n          key: key.substring(this._jaegerBaggageHeaderPrefix.length + 1),\n          value: Array.isArray(value) ? value[0] : value,\n        };\n      });\n\n    let newContext = context;\n    // if the trace id header is present and valid, inject it into the context\n    if (typeof uberTraceId === 'string') {\n      const spanContext = deserializeSpanContext(uberTraceId);\n      if (spanContext) {\n        newContext = trace.setSpanContext(newContext, spanContext);\n      }\n    }\n    if (baggageValues.length === 0) return newContext;\n\n    // if baggage values are present, inject it into the current baggage\n    let currentBaggage =\n      propagation.getBaggage(context) ?? propagation.createBaggage();\n    for (const baggageEntry of baggageValues) {\n      if (baggageEntry.value === undefined) continue;\n      currentBaggage = currentBaggage.setEntry(baggageEntry.key, {\n        value: decodeURIComponent(baggageEntry.value),\n      });\n    }\n    newContext = propagation.setBaggage(newContext, currentBaggage);\n\n    return newContext;\n  }\n\n  fields(): string[] {\n    return [this._jaegerTraceHeader];\n  }\n}\n\nconst VALID_HEX_RE = /^[0-9a-f]{1,2}$/i;\n\n/**\n * @param {string} serializedString - a serialized span context.\n * @return {SpanContext} - returns a span context represented by the serializedString.\n **/\nfunction deserializeSpanContext(serializedString: string): SpanContext | null {\n  const headers = decodeURIComponent(serializedString).split(':');\n  if (headers.length !== 4) {\n    return null;\n  }\n\n  const [_traceId, _spanId, , flags] = headers;\n\n  const traceId = _traceId.padStart(32, '0');\n  const spanId = _spanId.padStart(16, '0');\n  const traceFlags = VALID_HEX_RE.test(flags) ? parseInt(flags, 16) & 1 : 1;\n\n  return { traceId, spanId, isRemote: true, traceFlags };\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;;;;;AAEH,OAAO,EAML,WAAW,EACX,KAAK,EACL,UAAU,GACX,MAAM,oBAAoB,CAAC;AAC5B,OAAO,EAAE,mBAAmB,EAAE,MAAM,qBAAqB,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAGnD,IAAM,oBAAoB,GAAG,eAAe,CAAC;AAC7C,IAAM,0BAA0B,GAAG,SAAS,CAAC;AAEpD;;;;;;;;;;;;;;GAcG,CACH,IAAA,mBAAA;IAME,SAAA,iBAAY,MAAwC;QAClD,IAAI,OAAO,MAAM,KAAK,QAAQ,EAAE;YAC9B,IAAI,CAAC,kBAAkB,GAAG,MAAM,CAAC;YACjC,IAAI,CAAC,0BAA0B,GAAG,0BAA0B,CAAC;SAC9D,MAAM;YACL,IAAI,CAAC,kBAAkB,GACrB,CAAA,MAAM,KAAA,QAAN,MAAM,KAAA,KAAA,IAAA,KAAA,IAAN,MAAM,CAAE,iBAAiB,KAAI,oBAAoB,CAAC;YACpD,IAAI,CAAC,0BAA0B,GAC7B,CAAA,MAAM,KAAA,QAAN,MAAM,KAAA,KAAA,IAAA,KAAA,IAAN,MAAM,CAAE,yBAAyB,KAAI,0BAA0B,CAAC;SACnE;IACH,CAAC;IAED,iBAAA,SAAA,CAAA,MAAM,GAAN,SAAO,OAAgB,EAAE,OAAgB,EAAE,MAAqB;;QAC9D,IAAM,WAAW,6KAAG,QAAK,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC;QAClD,IAAM,OAAO,mLAAG,cAAW,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC;QAChD,IAAI,WAAW,8PAAI,sBAAA,AAAmB,EAAC,OAAO,CAAC,KAAK,KAAK,EAAE;YACzD,IAAM,UAAU,GAAG,MAAI,CACrB,WAAW,CAAC,UAAU,sLAAI,aAAU,CAAC,IAAI,CAC1C,CAAC,QAAQ,CAAC,EAAE,CAAG,CAAC;YAEjB,MAAM,CAAC,GAAG,CACR,OAAO,EACP,IAAI,CAAC,kBAAkB,EACpB,WAAW,CAAC,OAAO,GAAA,MAAI,WAAW,CAAC,MAAM,GAAA,QAAM,UAAY,CAC/D,CAAC;SACH;QAED,IAAI,OAAO,EAAE;;gBACX,IAA2B,IAAA,KAAA,SAAA,OAAO,CAAC,aAAa,EAAE,CAAA,EAAA,KAAA,GAAA,IAAA,EAAA,EAAA,CAAA,GAAA,IAAA,EAAA,KAAA,GAAA,IAAA,GAAE;oBAAzC,IAAA,KAAA,OAAA,GAAA,KAAA,EAAA,EAAY,EAAX,GAAG,GAAA,EAAA,CAAA,EAAA,EAAE,KAAK,GAAA,EAAA,CAAA,EAAA;oBACpB,MAAM,CAAC,GAAG,CACR,OAAO,EACJ,IAAI,CAAC,0BAA0B,GAAA,MAAI,GAAK,EAC3C,kBAAkB,CAAC,KAAK,CAAC,KAAK,CAAC,CAChC,CAAC;iBACH;;;;;;;;;;;;SACF;IACH,CAAC;IAED,iBAAA,SAAA,CAAA,OAAO,GAAP,SAAQ,OAAgB,EAAE,OAAgB,EAAE,MAAqB;;QAAjE,IAAA,QAAA,IAAA,CAsCC;;QArCC,IAAM,iBAAiB,GAAG,MAAM,CAAC,GAAG,CAAC,OAAO,EAAE,IAAI,CAAC,kBAAkB,CAAC,CAAC;QACvE,IAAM,WAAW,GAAG,KAAK,CAAC,OAAO,CAAC,iBAAiB,CAAC,GAChD,iBAAiB,CAAC,CAAC,CAAC,GACpB,iBAAiB,CAAC;QACtB,IAAM,aAAa,GAAG,MAAM,CACzB,IAAI,CAAC,OAAO,CAAC,CACb,MAAM,CAAC,SAAA,GAAG;YAAI,OAAA,GAAG,CAAC,UAAU,CAAI,KAAI,CAAC,0BAA0B,GAAA,GAAG,CAAC;QAArD,CAAqD,CAAC,CACpE,GAAG,CAAC,SAAA,GAAG;YACN,IAAM,KAAK,GAAG,MAAM,CAAC,GAAG,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC;YACvC,OAAO;gBACL,GAAG,EAAE,GAAG,CAAC,SAAS,CAAC,KAAI,CAAC,0BAA0B,CAAC,MAAM,GAAG,CAAC,CAAC;gBAC9D,KAAK,EAAE,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK;aAC/C,CAAC;QACJ,CAAC,CAAC,CAAC;QAEL,IAAI,UAAU,GAAG,OAAO,CAAC;QACzB,0EAA0E;QAC1E,IAAI,OAAO,WAAW,KAAK,QAAQ,EAAE;YACnC,IAAM,WAAW,GAAG,sBAAsB,CAAC,WAAW,CAAC,CAAC;YACxD,IAAI,WAAW,EAAE;gBACf,UAAU,GAAG,kLAAK,CAAC,cAAc,CAAC,UAAU,EAAE,WAAW,CAAC,CAAC;aAC5D;SACF;QACD,IAAI,aAAa,CAAC,MAAM,KAAK,CAAC,EAAE,OAAO,UAAU,CAAC;QAElD,oEAAoE;QACpE,IAAI,cAAc,GAChB,CAAA,KAAA,8LAAW,CAAC,UAAU,CAAC,OAAO,CAAC,MAAA,QAAA,OAAA,KAAA,IAAA,qLAAI,cAAW,CAAC,aAAa,EAAE,CAAC;;YACjE,IAA2B,IAAA,kBAAA,SAAA,aAAa,CAAA,EAAA,oBAAA,gBAAA,IAAA,EAAA,EAAA,CAAA,kBAAA,IAAA,EAAA,oBAAA,gBAAA,IAAA,GAAE;gBAArC,IAAM,YAAY,GAAA,kBAAA,KAAA;gBACrB,IAAI,YAAY,CAAC,KAAK,KAAK,SAAS,EAAE,SAAS;gBAC/C,cAAc,GAAG,cAAc,CAAC,QAAQ,CAAC,YAAY,CAAC,GAAG,EAAE;oBACzD,KAAK,EAAE,kBAAkB,CAAC,YAAY,CAAC,KAAK,CAAC;iBAC9C,CAAC,CAAC;aACJ;;;;;;;;;;;;QACD,UAAU,mLAAG,cAAW,CAAC,UAAU,CAAC,UAAU,EAAE,cAAc,CAAC,CAAC;QAEhE,OAAO,UAAU,CAAC;IACpB,CAAC;IAED,iBAAA,SAAA,CAAA,MAAM,GAAN;QACE,OAAO;YAAC,IAAI,CAAC,kBAAkB;SAAC,CAAC;IACnC,CAAC;IACH,OAAA,gBAAC;AAAD,CAAC,AAvFD,IAuFC;;AAED,IAAM,YAAY,GAAG,kBAAkB,CAAC;AAExC;;;IAGI,CACJ,SAAS,sBAAsB,CAAC,gBAAwB;IACtD,IAAM,OAAO,GAAG,kBAAkB,CAAC,gBAAgB,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;IAChE,IAAI,OAAO,CAAC,MAAM,KAAK,CAAC,EAAE;QACxB,OAAO,IAAI,CAAC;KACb;IAEK,IAAA,KAAA,OAA+B,OAAO,EAAA,EAAA,EAArC,QAAQ,GAAA,EAAA,CAAA,EAAA,EAAE,OAAO,GAAA,EAAA,CAAA,EAAA,EAAI,KAAK,GAAA,EAAA,CAAA,EAAW,CAAC;IAE7C,IAAM,OAAO,GAAG,QAAQ,CAAC,QAAQ,CAAC,EAAE,EAAE,GAAG,CAAC,CAAC;IAC3C,IAAM,MAAM,GAAG,OAAO,CAAC,QAAQ,CAAC,EAAE,EAAE,GAAG,CAAC,CAAC;IACzC,IAAM,UAAU,GAAG,YAAY,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,KAAK,EAAE,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAE1E,OAAO;QAAE,OAAO,EAAA,OAAA;QAAE,MAAM,EAAA,MAAA;QAAE,QAAQ,EAAE,IAAI;QAAE,UAAU,EAAA,UAAA;IAAA,CAAE,CAAC;AACzD,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3171, "column": 0}, "map": {"version": 3, "file": "NodeTracerProvider.js", "sourceRoot": "", "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/node_modules/%40opentelemetry/sdk-trace-node/src/NodeTracerProvider.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nimport {\n  AsyncHooksContextManager,\n  AsyncLocalStorageContextManager,\n} from '@opentelemetry/context-async-hooks';\nimport { B3Propagator, B3InjectEncoding } from '@opentelemetry/propagator-b3';\nimport {\n  BasicTracerProvider,\n  PROPAGATOR_FACTORY,\n  SDKRegistrationConfig,\n} from '@opentelemetry/sdk-trace-base';\nimport * as semver from 'semver';\nimport { NodeTracerConfig } from './config';\nimport { JaegerPropagator } from '@opentelemetry/propagator-jaeger';\n\n/**\n * Register this TracerProvider for use with the OpenTelemetry API.\n * Undefined values may be replaced with defaults, and\n * null values will be skipped.\n *\n * @param config Configuration object for SDK registration\n */\nexport class NodeTracerProvider extends BasicTracerProvider {\n  protected static override readonly _registeredPropagators = new Map<\n    string,\n    PROPAGATOR_FACTORY\n  >([\n    ...BasicTracerProvider._registeredPropagators,\n    [\n      'b3',\n      () =>\n        new B3Propagator({ injectEncoding: B3InjectEncoding.SINGLE_HEADER }),\n    ],\n    [\n      'b3multi',\n      () => new B3Propagator({ injectEncoding: B3InjectEncoding.MULTI_HEADER }),\n    ],\n    ['jaeger', () => new JaegerPropagator()],\n  ]);\n\n  constructor(config: NodeTracerConfig = {}) {\n    super(config);\n  }\n\n  override register(config: SDKRegistrationConfig = {}): void {\n    if (config.contextManager === undefined) {\n      const ContextManager = semver.gte(process.version, '14.8.0')\n        ? AsyncLocalStorageContextManager\n        : AsyncHooksContextManager;\n      config.contextManager = new ContextManager();\n      config.contextManager.enable();\n    }\n\n    super.register(config);\n  }\n}\n"], "names": [], "mappings": "AA4DwC;;;;;;AA5DxC;;;;;;;;;;;;;;GAcG,CACH,MAAA,sEAG4C;AAC5C,MAAA,0DAA8E;AAC9E,MAAA,4DAIuC;AACvC,MAAA,2BAAiC;AAEjC,MAAA,kEAAoE;AAEpE;;;;;;GAMG,CACH,MAAa,kBAAmB,SAAQ,iBAAA,mBAAmB;IAkBzD,YAAY,SAA2B,CAAA,CAAE,CAAA;QACvC,KAAK,CAAC,MAAM,CAAC,CAAC;IAChB,CAAC;IAEQ,QAAQ,CAAC,SAAgC,CAAA,CAAE,EAAA;QAClD,IAAI,MAAM,CAAC,cAAc,KAAK,SAAS,EAAE;YACvC,MAAM,cAAc,GAAG,MAAM,CAAC,GAAG,4KAAQ,CAAC,OAAO,EAAE,QAAQ,CAAC,GACxD,sBAAA,+BAA+B,GAC/B,sBAAA,wBAAwB,CAAC;YAC7B,MAAM,CAAC,cAAc,GAAG,IAAI,cAAc,EAAE,CAAC;YAC7C,MAAM,CAAC,cAAc,CAAC,MAAM,EAAE,CAAC;SAChC;QAED,KAAK,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;IACzB,CAAC;;AAhCH,QAAA,kBAAA,GAAA,mBAiCC;AAhCoC,mBAAA,sBAAsB,GAAG,IAAI,GAAG,CAGjE;OACG,iBAAA,mBAAmB,CAAC,sBAAsB;IAC7C;QACE,IAAI;QACJ,GAAG,CACD,CADG,GACC,gBAAA,YAAY,CAAC;gBAAE,cAAc,EAAE,gBAAA,gBAAgB,CAAC,aAAa;YAAA,CAAE,CAAC;KACvE;IACD;QACE,SAAS;QACT,GAAG,CAAG,CAAD,GAAK,gBAAA,YAAY,CAAC;gBAAE,cAAc,EAAE,gBAAA,gBAAgB,CAAC,YAAY;YAAA,CAAE,CAAC;KAC1E;IACD;QAAC,QAAQ;QAAE,GAAG,CAAG,CAAD,GAAK,oBAAA,gBAAgB,EAAE;KAAC;CACzC,CAAC,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3240, "column": 0}, "map": {"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/node_modules/%40opentelemetry/sdk-trace-node/src/index.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nexport { NodeTracerConfig } from './config';\nexport * from './NodeTracerProvider';\nexport * from '@opentelemetry/sdk-trace-base';\n"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG;;;;;;;;;;;;;;;;;;AAGH,wJAAA,SAAqC;AACrC,sLAAA,SAA8C", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3281, "column": 0}, "map": {"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/node_modules/%40opentelemetry/instrumentation/src/index.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nexport { registerInstrumentations } from './autoLoader';\nexport { InstrumentationBase } from './platform/index';\nexport { InstrumentationNodeModuleDefinition } from './instrumentationNodeModuleDefinition';\nexport { InstrumentationNodeModuleFile } from './instrumentationNodeModuleFile';\nexport * from './types';\nexport * from './types_internal';\nexport * from './utils';\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3318, "column": 0}, "map": {"version": 3, "file": "autoLoaderUtils.js", "sourceRoot": "", "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/node_modules/%40opentelemetry/instrumentation/src/autoLoaderUtils.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { TracerProvider, MeterProvider } from '@opentelemetry/api';\nimport { Instrumentation } from './types';\nimport { LoggerProvider } from '@opentelemetry/api-logs';\n\n/**\n * Enable instrumentations\n * @param instrumentations\n * @param tracerProvider\n * @param meterProvider\n */\nexport function enableInstrumentations(\n  instrumentations: Instrumentation[],\n  tracerProvider?: TracerProvider,\n  meterProvider?: MeterProvider,\n  loggerProvider?: LoggerProvider\n): void {\n  for (let i = 0, j = instrumentations.length; i < j; i++) {\n    const instrumentation = instrumentations[i];\n    if (tracerProvider) {\n      instrumentation.setTracerProvider(tracerProvider);\n    }\n    if (meterProvider) {\n      instrumentation.setMeterProvider(meterProvider);\n    }\n    if (loggerProvider && instrumentation.setLoggerProvider) {\n      instrumentation.setLoggerProvider(loggerProvider);\n    }\n    // instrumentations have been already enabled during creation\n    // so enable only if user prevented that by setting enabled to false\n    // this is to prevent double enabling but when calling register all\n    // instrumentations should be now enabled\n    if (!instrumentation.getConfig().enabled) {\n      instrumentation.enable();\n    }\n  }\n}\n\n/**\n * Disable instrumentations\n * @param instrumentations\n */\nexport function disableInstrumentations(\n  instrumentations: Instrumentation[]\n): void {\n  instrumentations.forEach(instrumentation => instrumentation.disable());\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG,CAMH;;;;;GAKG;;;;AACG,SAAU,sBAAsB,CACpC,gBAAmC,EACnC,cAA+B,EAC/B,aAA6B,EAC7B,cAA+B;IAE/B,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,gBAAgB,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAE;QACvD,IAAM,eAAe,GAAG,gBAAgB,CAAC,CAAC,CAAC,CAAC;QAC5C,IAAI,cAAc,EAAE;YAClB,eAAe,CAAC,iBAAiB,CAAC,cAAc,CAAC,CAAC;SACnD;QACD,IAAI,aAAa,EAAE;YACjB,eAAe,CAAC,gBAAgB,CAAC,aAAa,CAAC,CAAC;SACjD;QACD,IAAI,cAAc,IAAI,eAAe,CAAC,iBAAiB,EAAE;YACvD,eAAe,CAAC,iBAAiB,CAAC,cAAc,CAAC,CAAC;SACnD;QACD,6DAA6D;QAC7D,oEAAoE;QACpE,mEAAmE;QACnE,yCAAyC;QACzC,IAAI,CAAC,eAAe,CAAC,SAAS,EAAE,CAAC,OAAO,EAAE;YACxC,eAAe,CAAC,MAAM,EAAE,CAAC;SAC1B;KACF;AACH,CAAC;AAMK,SAAU,uBAAuB,CACrC,gBAAmC;IAEnC,gBAAgB,CAAC,OAAO,CAAC,SAAA,eAAe;QAAI,OAAA,eAAe,CAAC,OAAO,EAAE;IAAzB,CAAyB,CAAC,CAAC;AACzE,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3373, "column": 0}, "map": {"version": 3, "file": "autoLoader.js", "sourceRoot": "", "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/node_modules/%40opentelemetry/instrumentation/src/autoLoader.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { trace, metrics } from '@opentelemetry/api';\nimport { logs } from '@opentelemetry/api-logs';\nimport {\n  disableInstrumentations,\n  enableInstrumentations,\n} from './autoLoaderUtils';\nimport { AutoLoaderOptions } from './types_internal';\n\n/**\n * It will register instrumentations and plugins\n * @param options\n * @return returns function to unload instrumentation and plugins that were\n *   registered\n */\nexport function registerInstrumentations(\n  options: AutoLoaderOptions\n): () => void {\n  const tracerProvider = options.tracerProvider || trace.getTracerProvider();\n  const meterProvider = options.meterProvider || metrics.getMeterProvider();\n  const loggerProvider = options.loggerProvider || logs.getLoggerProvider();\n  const instrumentations = options.instrumentations?.flat() ?? [];\n\n  enableInstrumentations(\n    instrumentations,\n    tracerProvider,\n    meterProvider,\n    loggerProvider\n  );\n\n  return () => {\n    disableInstrumentations(instrumentations);\n  };\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;;AAEH,OAAO,EAAE,KAAK,EAAE,OAAO,EAAE,MAAM,oBAAoB,CAAC;AACpD,OAAO,EAAE,IAAI,EAAE,MAAM,yBAAyB,CAAC;AAC/C,OAAO,EACL,uBAAuB,EACvB,sBAAsB,GACvB,MAAM,mBAAmB,CAAC;;;;AASrB,SAAU,wBAAwB,CACtC,OAA0B;;IAE1B,IAAM,cAAc,GAAG,OAAO,CAAC,cAAc,8KAAI,QAAK,CAAC,iBAAiB,EAAE,CAAC;IAC3E,IAAM,aAAa,GAAG,OAAO,CAAC,aAAa,gLAAI,UAAO,CAAC,gBAAgB,EAAE,CAAC;IAC1E,IAAM,cAAc,GAAG,OAAO,CAAC,cAAc,+LAAI,OAAI,CAAC,iBAAiB,EAAE,CAAC;IAC1E,IAAM,gBAAgB,GAAG,CAAA,KAAA,CAAA,KAAA,OAAO,CAAC,gBAAgB,MAAA,QAAA,OAAA,KAAA,IAAA,KAAA,IAAA,GAAE,IAAI,EAAE,MAAA,QAAA,OAAA,KAAA,IAAA,KAAI,EAAE,CAAC;iMAEhE,yBAAA,AAAsB,EACpB,gBAAgB,EAChB,cAAc,EACd,aAAa,EACb,cAAc,CACf,CAAC;IAEF,OAAO;qMACL,0BAAA,AAAuB,EAAC,gBAAgB,CAAC,CAAC;IAC5C,CAAC,CAAC;AACJ,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3414, "column": 0}, "map": {"version": 3, "file": "instrumentation.js", "sourceRoot": "", "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/node_modules/%40opentelemetry/instrumentation/src/instrumentation.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  diag,\n  DiagLogger,\n  metrics,\n  Meter,\n  MeterProvider,\n  trace,\n  Tracer,\n  TracerProvider,\n  Span,\n} from '@opentelemetry/api';\nimport { Logger, LoggerProvider, logs } from '@opentelemetry/api-logs';\nimport * as shimmer from 'shimmer';\nimport {\n  InstrumentationModuleDefinition,\n  Instrumentation,\n  InstrumentationConfig,\n  SpanCustomizationHook,\n} from './types';\n\n/**\n * Base abstract internal class for instrumenting node and web plugins\n */\nexport abstract class InstrumentationAbstract<\n  ConfigType extends InstrumentationConfig = InstrumentationConfig,\n> implements Instrumentation<ConfigType>\n{\n  protected _config: ConfigType;\n\n  private _tracer: Tracer;\n  private _meter: Meter;\n  private _logger: Logger;\n  protected _diag: DiagLogger;\n\n  constructor(\n    public readonly instrumentationName: string,\n    public readonly instrumentationVersion: string,\n    config: ConfigType\n  ) {\n    // copy config first level properties to ensure they are immutable.\n    // nested properties are not copied, thus are mutable from the outside.\n    this._config = {\n      enabled: true,\n      ...config,\n    };\n\n    this._diag = diag.createComponentLogger({\n      namespace: instrumentationName,\n    });\n\n    this._tracer = trace.getTracer(instrumentationName, instrumentationVersion);\n    this._meter = metrics.getMeter(instrumentationName, instrumentationVersion);\n    this._logger = logs.getLogger(instrumentationName, instrumentationVersion);\n    this._updateMetricInstruments();\n  }\n\n  /* Api to wrap instrumented method */\n  protected _wrap = shimmer.wrap;\n  /* Api to unwrap instrumented methods */\n  protected _unwrap = shimmer.unwrap;\n  /* Api to mass wrap instrumented method */\n  protected _massWrap = shimmer.massWrap;\n  /* Api to mass unwrap instrumented methods */\n  protected _massUnwrap = shimmer.massUnwrap;\n\n  /* Returns meter */\n  protected get meter(): Meter {\n    return this._meter;\n  }\n\n  /**\n   * Sets MeterProvider to this plugin\n   * @param meterProvider\n   */\n  public setMeterProvider(meterProvider: MeterProvider): void {\n    this._meter = meterProvider.getMeter(\n      this.instrumentationName,\n      this.instrumentationVersion\n    );\n\n    this._updateMetricInstruments();\n  }\n\n  /* Returns logger */\n  protected get logger(): Logger {\n    return this._logger;\n  }\n\n  /**\n   * Sets LoggerProvider to this plugin\n   * @param loggerProvider\n   */\n  public setLoggerProvider(loggerProvider: LoggerProvider): void {\n    this._logger = loggerProvider.getLogger(\n      this.instrumentationName,\n      this.instrumentationVersion\n    );\n  }\n\n  /**\n   * @experimental\n   *\n   * Get module definitions defined by {@link init}.\n   * This can be used for experimental compile-time instrumentation.\n   *\n   * @returns an array of {@link InstrumentationModuleDefinition}\n   */\n  public getModuleDefinitions(): InstrumentationModuleDefinition[] {\n    const initResult = this.init() ?? [];\n    if (!Array.isArray(initResult)) {\n      return [initResult];\n    }\n\n    return initResult;\n  }\n\n  /**\n   * Sets the new metric instruments with the current Meter.\n   */\n  protected _updateMetricInstruments(): void {\n    return;\n  }\n\n  /* Returns InstrumentationConfig */\n  public getConfig(): ConfigType {\n    return this._config;\n  }\n\n  /**\n   * Sets InstrumentationConfig to this plugin\n   * @param InstrumentationConfig\n   */\n  public setConfig(config: ConfigType): void {\n    // copy config first level properties to ensure they are immutable.\n    // nested properties are not copied, thus are mutable from the outside.\n    this._config = { ...config };\n  }\n\n  /**\n   * Sets TraceProvider to this plugin\n   * @param tracerProvider\n   */\n  public setTracerProvider(tracerProvider: TracerProvider): void {\n    this._tracer = tracerProvider.getTracer(\n      this.instrumentationName,\n      this.instrumentationVersion\n    );\n  }\n\n  /* Returns tracer */\n  protected get tracer(): Tracer {\n    return this._tracer;\n  }\n\n  /* Disable plugin */\n  public abstract enable(): void;\n\n  /* Enable plugin */\n  public abstract disable(): void;\n\n  /**\n   * Init method in which plugin should define _modules and patches for\n   * methods.\n   */\n  protected abstract init():\n    | InstrumentationModuleDefinition\n    | InstrumentationModuleDefinition[]\n    | void;\n\n  /**\n   * Execute span customization hook, if configured, and log any errors.\n   * Any semantics of the trigger and info are defined by the specific instrumentation.\n   * @param hookHandler The optional hook handler which the user has configured via instrumentation config\n   * @param triggerName The name of the trigger for executing the hook for logging purposes\n   * @param span The span to which the hook should be applied\n   * @param info The info object to be passed to the hook, with useful data the hook may use\n   */\n  protected _runSpanCustomizationHook<SpanCustomizationInfoType>(\n    hookHandler: SpanCustomizationHook<SpanCustomizationInfoType> | undefined,\n    triggerName: string,\n    span: Span,\n    info: SpanCustomizationInfoType\n  ) {\n    if (!hookHandler) {\n      return;\n    }\n\n    try {\n      hookHandler(span, info);\n    } catch (e) {\n      this._diag.error(\n        `Error running span customization hook due to exception in handler`,\n        { triggerName },\n        e\n      );\n    }\n  }\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;AAEH,OAAO,EACL,IAAI,EAEJ,OAAO,EAGP,KAAK,GAIN,MAAM,oBAAoB,CAAC;;;AAC5B,OAAO,EAA0B,IAAI,EAAE,MAAM,yBAAyB,CAAC;AACvE,OAAO,KAAK,OAAO,MAAM,SAAS,CAAC;;;;;;;;;;;;;;AAQnC;;GAEG,CACH,IAAA,0BAAA;IAWE,SAAA,wBACkB,mBAA2B,EAC3B,sBAA8B,EAC9C,MAAkB;QAFF,IAAA,CAAA,mBAAmB,GAAnB,mBAAmB,CAAQ;QAC3B,IAAA,CAAA,sBAAsB,GAAtB,sBAAsB,CAAQ;QAoBhD,mCAAA,EAAqC,CAC3B,IAAA,CAAA,KAAK,uIAAG,OAAO,AAAK,CAAJ,AAAK;QAC/B,sCAAA,EAAwC,CAC9B,IAAA,CAAA,OAAO,uIAAG,OAAO,CAAC,CAAM,CAAC;QACnC,wCAAA,EAA0C,CAChC,IAAA,CAAA,SAAS,uIAAG,OAAO,CAAC,GAAQ,CAAC;QACvC,2CAAA,EAA6C,CACnC,IAAA,CAAA,WAAW,uIAAG,OAAO,CAAC,KAAU,CAAC;QAxBzC,mEAAmE;QACnE,uEAAuE;QACvE,IAAI,CAAC,OAAO,GAAA,SAAA;YACV,OAAO,EAAE,IAAI;QAAA,GACV,MAAM,CACV,CAAC;QAEF,IAAI,CAAC,KAAK,GAAG,gLAAI,CAAC,qBAAqB,CAAC;YACtC,SAAS,EAAE,mBAAmB;SAC/B,CAAC,CAAC;QAEH,IAAI,CAAC,OAAO,GAAG,kLAAK,CAAC,SAAS,CAAC,mBAAmB,EAAE,sBAAsB,CAAC,CAAC;QAC5E,IAAI,CAAC,MAAM,+KAAG,UAAO,CAAC,QAAQ,CAAC,mBAAmB,EAAE,sBAAsB,CAAC,CAAC;QAC5E,IAAI,CAAC,OAAO,8LAAG,OAAI,CAAC,SAAS,CAAC,mBAAmB,EAAE,sBAAsB,CAAC,CAAC;QAC3E,IAAI,CAAC,wBAAwB,EAAE,CAAC;IAClC,CAAC;IAYD,OAAA,cAAA,CAAc,wBAAA,SAAA,EAAA,OAAK,EAAA;QADnB,iBAAA,EAAmB,MACnB;YACE,OAAO,IAAI,CAAC,MAAM,CAAC;QACrB,CAAC;;;OAAA;IAED;;;OAGG,CACI,wBAAA,SAAA,CAAA,gBAAgB,GAAvB,SAAwB,aAA4B;QAClD,IAAI,CAAC,MAAM,GAAG,aAAa,CAAC,QAAQ,CAClC,IAAI,CAAC,mBAAmB,EACxB,IAAI,CAAC,sBAAsB,CAC5B,CAAC;QAEF,IAAI,CAAC,wBAAwB,EAAE,CAAC;IAClC,CAAC;IAGD,OAAA,cAAA,CAAc,wBAAA,SAAA,EAAA,QAAM,EAAA;QADpB,kBAAA,EAAoB,MACpB;YACE,OAAO,IAAI,CAAC,OAAO,CAAC;QACtB,CAAC;;;OAAA;IAED;;;OAGG,CACI,wBAAA,SAAA,CAAA,iBAAiB,GAAxB,SAAyB,cAA8B;QACrD,IAAI,CAAC,OAAO,GAAG,cAAc,CAAC,SAAS,CACrC,IAAI,CAAC,mBAAmB,EACxB,IAAI,CAAC,sBAAsB,CAC5B,CAAC;IACJ,CAAC;IAED;;;;;;;OAOG,CACI,wBAAA,SAAA,CAAA,oBAAoB,GAA3B;;QACE,IAAM,UAAU,GAAG,CAAA,KAAA,IAAI,CAAC,IAAI,EAAE,MAAA,QAAA,OAAA,KAAA,IAAA,KAAI,EAAE,CAAC;QACrC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,UAAU,CAAC,EAAE;YAC9B,OAAO;gBAAC,UAAU;aAAC,CAAC;SACrB;QAED,OAAO,UAAU,CAAC;IACpB,CAAC;IAED;;OAEG,CACO,wBAAA,SAAA,CAAA,wBAAwB,GAAlC;QACE,OAAO;IACT,CAAC;IAED,iCAAA,EAAmC,CAC5B,wBAAA,SAAA,CAAA,SAAS,GAAhB;QACE,OAAO,IAAI,CAAC,OAAO,CAAC;IACtB,CAAC;IAED;;;OAGG,CACI,wBAAA,SAAA,CAAA,SAAS,GAAhB,SAAiB,MAAkB;QACjC,mEAAmE;QACnE,uEAAuE;QACvE,IAAI,CAAC,OAAO,GAAA,SAAA,CAAA,GAAQ,MAAM,CAAE,CAAC;IAC/B,CAAC;IAED;;;OAGG,CACI,wBAAA,SAAA,CAAA,iBAAiB,GAAxB,SAAyB,cAA8B;QACrD,IAAI,CAAC,OAAO,GAAG,cAAc,CAAC,SAAS,CACrC,IAAI,CAAC,mBAAmB,EACxB,IAAI,CAAC,sBAAsB,CAC5B,CAAC;IACJ,CAAC;IAGD,OAAA,cAAA,CAAc,wBAAA,SAAA,EAAA,QAAM,EAAA;QADpB,kBAAA,EAAoB,MACpB;YACE,OAAO,IAAI,CAAC,OAAO,CAAC;QACtB,CAAC;;;OAAA;IAiBD;;;;;;;OAOG,CACO,wBAAA,SAAA,CAAA,yBAAyB,GAAnC,SACE,WAAyE,EACzE,WAAmB,EACnB,IAAU,EACV,IAA+B;QAE/B,IAAI,CAAC,WAAW,EAAE;YAChB,OAAO;SACR;QAED,IAAI;YACF,WAAW,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;SACzB,CAAC,OAAO,CAAC,EAAE;YACV,IAAI,CAAC,KAAK,CAAC,KAAK,CACd,mEAAmE,EACnE;gBAAE,WAAW,EAAA,WAAA;YAAA,CAAE,EACf,CAAC,CACF,CAAC;SACH;IACH,CAAC;IACH,OAAA,uBAAC;AAAD,CAAC,AA9KD,IA8KC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3574, "column": 0}, "map": {"version": 3, "file": "instrumentation.js", "sourceRoot": "", "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/node_modules/%40opentelemetry/instrumentation/src/platform/browser/instrumentation.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { InstrumentationAbstract } from '../../instrumentation';\nimport * as types from '../../types';\nimport { InstrumentationConfig } from '../../types';\n\n/**\n * Base abstract class for instrumenting web plugins\n */\nexport abstract class InstrumentationBase<\n    ConfigType extends InstrumentationConfig = InstrumentationConfig,\n  >\n  extends InstrumentationAbstract<ConfigType>\n  implements types.Instrumentation<ConfigType>\n{\n  constructor(\n    instrumentationName: string,\n    instrumentationVersion: string,\n    config: ConfigType\n  ) {\n    super(instrumentationName, instrumentationVersion, config);\n\n    if (this._config.enabled) {\n      this.enable();\n    }\n  }\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;AAEH,OAAO,EAAE,uBAAuB,EAAE,MAAM,uBAAuB,CAAC;;;;;;;;;;;;;;;;;;;;;;AAIhE;;GAEG,CACH,IAAA,sBAAA,SAAA,MAAA;IAGU,UAAA,qBAAA,QAAmC;IAG3C,SAAA,oBACE,mBAA2B,EAC3B,sBAA8B,EAC9B,MAAkB;QAHpB,IAAA,QAKE,OAAA,IAAA,CAAA,IAAA,EAAM,mBAAmB,EAAE,sBAAsB,EAAE,MAAM,CAAC,IAAA,IAAA,CAK3D;QAHC,IAAI,KAAI,CAAC,OAAO,CAAC,OAAO,EAAE;YACxB,KAAI,CAAC,MAAM,EAAE,CAAC;SACf;;IACH,CAAC;IACH,OAAA,mBAAC;AAAD,CAAC,AAjBD,0LAGU,0BAAuB,GAchC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3634, "column": 0}, "map": {"version": 3, "file": "instrumentationNodeModuleDefinition.js", "sourceRoot": "", "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/node_modules/%40opentelemetry/instrumentation/src/instrumentationNodeModuleDefinition.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  InstrumentationModuleDefinition,\n  InstrumentationModuleFile,\n} from './types';\n\nexport class InstrumentationNodeModuleDefinition\n  implements InstrumentationModuleDefinition\n{\n  files: InstrumentationModuleFile[];\n  constructor(\n    public name: string,\n    public supportedVersions: string[],\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    public patch?: (exports: any, moduleVersion?: string) => any,\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    public unpatch?: (exports: any, moduleVersion?: string) => void,\n    files?: InstrumentationModuleFile[]\n  ) {\n    this.files = files || [];\n  }\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;AAOH,IAAA,sCAAA;IAIE,SAAA,oCACS,IAAY,EACZ,iBAA2B,EAClC,8DAA8D;IACvD,KAAqD,EAC5D,8DAA8D;IACvD,OAAwD,EAC/D,KAAmC;QAN5B,IAAA,CAAA,IAAI,GAAJ,IAAI,CAAQ;QACZ,IAAA,CAAA,iBAAiB,GAAjB,iBAAiB,CAAU;QAE3B,IAAA,CAAA,KAAK,GAAL,KAAK,CAAgD;QAErD,IAAA,CAAA,OAAO,GAAP,OAAO,CAAiD;QAG/D,IAAI,CAAC,KAAK,GAAG,KAAK,IAAI,EAAE,CAAC;IAC3B,CAAC;IACH,OAAA,mCAAC;AAAD,CAAC,AAfD,IAeC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3671, "column": 0}, "map": {"version": 3, "file": "noop-normalize.js", "sourceRoot": "", "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/node_modules/%40opentelemetry/instrumentation/src/platform/browser/noop-normalize.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { diag } from '@opentelemetry/api';\n\n/**\n * Placeholder normalize function to replace the node variant in browser runtimes,\n * this should never be called and will perform a no-op and warn if it is called regardless.\n *\n * This is a workaround to fix https://github.com/open-telemetry/opentelemetry-js/issues/4373 until the instrumentation\n * package can be made node-only.\n *\n * @param path input path\n * @return unmodified path\n * @internal\n */\nexport function normalize(path: string): string {\n  diag.warn(\n    'Path normalization is not implemented for this platform. To silence this warning, ensure no node-specific instrumentations are loaded, and node-specific types (e.g. InstrumentationNodeModuleFile), are not used in a browser context)'\n  );\n  return path;\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;AAEH,OAAO,EAAE,IAAI,EAAE,MAAM,oBAAoB,CAAC;;AAapC,SAAU,SAAS,CAAC,IAAY;6KACpC,OAAI,CAAC,IAAI,CACP,yOAAyO,CAC1O,CAAC;IACF,OAAO,IAAI,CAAC;AACd,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3700, "column": 0}, "map": {"version": 3, "file": "instrumentationNodeModuleFile.js", "sourceRoot": "", "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/node_modules/%40opentelemetry/instrumentation/src/instrumentationNodeModuleFile.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { InstrumentationModuleFile } from './types';\nimport { normalize } from './platform/index';\n\nexport class InstrumentationNodeModuleFile\n  implements InstrumentationModuleFile\n{\n  public name: string;\n  constructor(\n    name: string,\n    public supportedVersions: string[],\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    public patch: (moduleExports: any, moduleVersion?: string) => any,\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    public unpatch: (moduleExports?: any, moduleVersion?: string) => void\n  ) {\n    this.name = normalize(name);\n  }\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;AAGH,OAAO,EAAE,SAAS,EAAE,MAAM,kBAAkB,CAAC;;AAE7C,IAAA,gCAAA;IAIE,SAAA,8BACE,IAAY,EACL,iBAA2B,EAClC,8DAA8D;IACvD,KAA0D,EACjE,8DAA8D;IACvD,OAA8D;QAJ9D,IAAA,CAAA,iBAAiB,GAAjB,iBAAiB,CAAU;QAE3B,IAAA,CAAA,KAAK,GAAL,KAAK,CAAqD;QAE1D,IAAA,CAAA,OAAO,GAAP,OAAO,CAAuD;QAErE,IAAI,CAAC,IAAI,yNAAG,YAAA,AAAS,EAAC,IAAI,CAAC,CAAC;IAC9B,CAAC;IACH,OAAA,6BAAC;AAAD,CAAC,AAdD,IAcC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3738, "column": 0}, "map": {"version": 3, "file": "types.js", "sourceRoot": "", "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/node_modules/%40opentelemetry/instrumentation/src/types.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { TracerProvider, MeterProvider, Span } from '@opentelemetry/api';\nimport { LoggerProvider } from '@opentelemetry/api-logs';\n\n/** Interface Instrumentation to apply patch. */\nexport interface Instrumentation<\n  ConfigType extends InstrumentationConfig = InstrumentationConfig,\n> {\n  /** Instrumentation Name  */\n  instrumentationName: string;\n\n  /** Instrumentation Version  */\n  instrumentationVersion: string;\n\n  /**\n   * Instrumentation Description - please describe all useful information\n   * as Instrumentation might patch different version of different modules,\n   * or support different browsers etc.\n   */\n  instrumentationDescription?: string;\n\n  /** Method to disable the instrumentation  */\n  disable(): void;\n\n  /** Method to enable the instrumentation  */\n  enable(): void;\n\n  /** Method to set tracer provider  */\n  setTracerProvider(tracerProvider: TracerProvider): void;\n\n  /** Method to set meter provider  */\n  setMeterProvider(meterProvider: MeterProvider): void;\n\n  /** Method to set logger provider  */\n  setLoggerProvider?(loggerProvider: LoggerProvider): void;\n\n  /** Method to set instrumentation config  */\n  setConfig(config: ConfigType): void;\n\n  /** Method to get instrumentation config  */\n  getConfig(): ConfigType;\n}\n\n/**\n * Base interface for configuration options common to all instrumentations.\n * This interface can be extended by individual instrumentations to include\n * additional configuration options specific to that instrumentation.\n * All configuration options must be optional.\n */\nexport interface InstrumentationConfig {\n  /**\n   * Whether to enable the plugin.\n   * @default true\n   */\n  enabled?: boolean;\n}\n\n/**\n * This interface defines the params that are be added to the wrapped function\n * using the \"shimmer.wrap\"\n */\nexport interface ShimWrapped extends Function {\n  __wrapped: boolean;\n  // eslint-disable-next-line @typescript-eslint/ban-types\n  __unwrap: Function;\n  // eslint-disable-next-line @typescript-eslint/ban-types\n  __original: Function;\n}\n\nexport interface InstrumentationModuleFile {\n  /** Name of file to be patched with relative path */\n  name: string;\n\n  moduleExports?: unknown;\n\n  /** Supported versions for the file.\n   *\n   * A module version is supported if one of the supportedVersions in the array satisfies the module version.\n   * The syntax of the version is checked with the `satisfies` function of \"The semantic versioner for npm\", see\n   * [`semver` package](https://www.npmjs.com/package/semver)\n   * If the version is not supported, we won't apply instrumentation patch.\n   * If omitted, all versions of the module will be patched.\n   *\n   * It is recommended to always specify a range that is bound to a major version, to avoid breaking changes.\n   * New major versions should be reviewed and tested before being added to the supportedVersions array.\n   *\n   * Example: ['>=1.2.3 <3']\n   */\n  supportedVersions: string[];\n\n  /** Method to patch the instrumentation  */\n  patch(moduleExports: unknown, moduleVersion?: string): unknown;\n\n  /** Method to unpatch the instrumentation  */\n  unpatch(moduleExports?: unknown, moduleVersion?: string): void;\n}\n\nexport interface InstrumentationModuleDefinition {\n  /** Module name or path  */\n  name: string;\n\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  moduleExports?: any;\n\n  /** Instrumented module version */\n  moduleVersion?: string;\n\n  /** Supported version of module.\n   *\n   * A module version is supported if one of the supportedVersions in the array satisfies the module version.\n   * The syntax of the version is checked with the `satisfies` function of \"The semantic versioner for npm\", see\n   * [`semver` package](https://www.npmjs.com/package/semver)\n   * If the version is not supported, we won't apply instrumentation patch (see `enable` method).\n   * If omitted, all versions of the module will be patched.\n   *\n   * It is recommended to always specify a range that is bound to a major version, to avoid breaking changes.\n   * New major versions should be reviewed and tested before being added to the supportedVersions array.\n   *\n   * Example: ['>=1.2.3 <3']\n   */\n  supportedVersions: string[];\n\n  /** Module internal files to be patched  */\n  files: InstrumentationModuleFile[];\n\n  /** If set to true, the includePrerelease check will be included when calling semver.satisfies */\n  includePrerelease?: boolean;\n\n  /** Method to patch the instrumentation  */\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  patch?: (moduleExports: any, moduleVersion?: string) => any;\n\n  /** Method to unpatch the instrumentation  */\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  unpatch?: (moduleExports: any, moduleVersion?: string) => void;\n}\n\n/**\n * SpanCustomizationHook is a common way for instrumentations to expose extension points\n * where users can add custom behavior to a span based on info object passed to the hook at different times of the span lifecycle.\n * This is an advanced feature, commonly used to add additional or non-spec-compliant attributes to the span,\n * capture payloads, modify the span in some way, or carry some other side effect.\n *\n * The hook is registered with the instrumentation specific config by implementing an handler function with this signature,\n * and if the hook is present, it will be called with the span and the event information\n * when the event is emitted.\n *\n * When and under what conditions the hook is called and what data is passed\n * in the info argument, is specific to each instrumentation and life-cycle event\n * and should be documented where it is used.\n *\n * Instrumentation may define multiple hooks, for different spans, or different span life-cycle events.\n */\nexport type SpanCustomizationHook<SpanCustomizationInfoType> = (\n  span: Span,\n  info: SpanCustomizationInfoType\n) => void;\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3761, "column": 0}, "map": {"version": 3, "file": "types_internal.js", "sourceRoot": "", "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/node_modules/%40opentelemetry/instrumentation/src/types_internal.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { TracerProvider, MeterProvider } from '@opentelemetry/api';\nimport { Instrumentation } from './types';\nimport { LoggerProvider } from '@opentelemetry/api-logs';\n\nexport interface AutoLoaderResult {\n  instrumentations: Instrumentation[];\n}\n\nexport interface AutoLoaderOptions {\n  instrumentations?: (Instrumentation | Instrumentation[])[];\n  tracerProvider?: TracerProvider;\n  meterProvider?: MeterProvider;\n  loggerProvider?: LoggerProvider;\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3784, "column": 0}, "map": {"version": 3, "file": "utils.js", "sourceRoot": "", "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/node_modules/%40opentelemetry/instrumentation/src/utils.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { ShimWrapped } from './types';\n\n/**\n * function to execute patched function and being able to catch errors\n * @param execute - function to be executed\n * @param onFinish - callback to run when execute finishes\n */\nexport function safeExecuteInTheMiddle<T>(\n  execute: () => T,\n  onFinish: (e: Error | undefined, result: T | undefined) => void,\n  preventThrowingError?: boolean\n): T {\n  let error: Error | undefined;\n  let result: T | undefined;\n  try {\n    result = execute();\n  } catch (e) {\n    error = e;\n  } finally {\n    onFinish(error, result);\n    if (error && !preventThrowingError) {\n      // eslint-disable-next-line no-unsafe-finally\n      throw error;\n    }\n    // eslint-disable-next-line no-unsafe-finally\n    return result as T;\n  }\n}\n\n/**\n * Async function to execute patched function and being able to catch errors\n * @param execute - function to be executed\n * @param onFinish - callback to run when execute finishes\n */\nexport async function safeExecuteInTheMiddleAsync<T>(\n  execute: () => T,\n  onFinish: (e: Error | undefined, result: T | undefined) => void,\n  preventThrowingError?: boolean\n): Promise<T> {\n  let error: Error | undefined;\n  let result: T | undefined;\n  try {\n    result = await execute();\n  } catch (e) {\n    error = e;\n  } finally {\n    onFinish(error, result);\n    if (error && !preventThrowingError) {\n      // eslint-disable-next-line no-unsafe-finally\n      throw error;\n    }\n    // eslint-disable-next-line no-unsafe-finally\n    return result as T;\n  }\n}\n/**\n * Checks if certain function has been already wrapped\n * @param func\n */\nexport function isWrapped(func: unknown): func is ShimWrapped {\n  return (\n    typeof func === 'function' &&\n    typeof (func as ShimWrapped).__original === 'function' &&\n    typeof (func as ShimWrapped).__unwrap === 'function' &&\n    (func as ShimWrapped).__wrapped === true\n  );\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASG,SAAU,sBAAsB,CACpC,OAAgB,EAChB,QAA+D,EAC/D,oBAA8B;IAE9B,IAAI,KAAwB,CAAC;IAC7B,IAAI,MAAqB,CAAC;IAC1B,IAAI;QACF,MAAM,GAAG,OAAO,EAAE,CAAC;KACpB,CAAC,OAAO,CAAC,EAAE;QACV,KAAK,GAAG,CAAC,CAAC;KACX,QAAS;QACR,QAAQ,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;QACxB,IAAI,KAAK,IAAI,CAAC,oBAAoB,EAAE;YAClC,6CAA6C;YAC7C,MAAM,KAAK,CAAC;SACb;QACD,6CAA6C;QAC7C,OAAO,MAAW,CAAC;KACpB;AACH,CAAC;AAOK,SAAgB,2BAA2B,CAC/C,OAAgB,EAChB,QAA+D,EAC/D,oBAA8B;;;;;;;;;;;;oBAKnB,OAAA;wBAAA,EAAA,OAAA;wBAAM,OAAO,EAAE;qBAAA,CAAA;;oBAAxB,MAAM,GAAG,GAAA,IAAA,EAAe,CAAC;;;;;;;oBAEzB,KAAK,GAAG,GAAC,CAAC;;;;;;oBAEV,QAAQ,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;oBACxB,IAAI,KAAK,IAAI,CAAC,oBAAoB,EAAE;wBAClC,6CAA6C;wBAC7C,MAAM,KAAK,CAAC;qBACb;oBACD,6CAA6C;oBAC7C,OAAA;wBAAA,EAAA,QAAA;wBAAO,MAAW;qBAAA,CAAC;;;;;;;;CAEtB;AAKK,SAAU,SAAS,CAAC,IAAa;IACrC,OAAO,AACL,OAAO,IAAI,KAAK,UAAU,IAC1B,OAAQ,IAAoB,CAAC,UAAU,KAAK,UAAU,IACtD,OAAQ,IAAoB,CAAC,QAAQ,KAAK,UAAU,IACnD,IAAoB,CAAC,SAAS,KAAK,IAAI,CACzC,CAAC;AACJ,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4040, "column": 0}, "map": {"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/node_modules/%40opentelemetry/exporter-trace-otlp-proto/src/index.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nexport { OTLPTraceExporter } from './platform';\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4071, "column": 0}, "map": {"version": 3, "file": "OTLPTraceExporter.js", "sourceRoot": "", "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/node_modules/%40opentelemetry/exporter-trace-otlp-proto/src/platform/browser/OTLPTraceExporter.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { ReadableSpan, SpanExporter } from '@opentelemetry/sdk-trace-base';\nimport { getEnv, baggageUtils } from '@opentelemetry/core';\nimport {\n  OTLPExporterConfigBase,\n  appendResourcePathToUrl,\n  appendRootPathToUrlIfNeeded,\n  OTLPExporterBrowserBase,\n} from '@opentelemetry/otlp-exporter-base';\nimport {\n  IExportTraceServiceResponse,\n  ProtobufTraceSerializer,\n} from '@opentelemetry/otlp-transformer';\n\nconst DEFAULT_COLLECTOR_RESOURCE_PATH = 'v1/traces';\nconst DEFAULT_COLLECTOR_URL = `http://localhost:4318/${DEFAULT_COLLECTOR_RESOURCE_PATH}`;\n\n/**\n * Collector Trace Exporter for Web\n */\nexport class OTLPTraceExporter\n  extends OTLPExporterBrowserBase<ReadableSpan, IExportTraceServiceResponse>\n  implements SpanExporter\n{\n  constructor(config: OTLPExporterConfigBase = {}) {\n    super(config, ProtobufTraceSerializer, 'application/x-protobuf');\n    this._headers = Object.assign(\n      this._headers,\n      baggageUtils.parseKeyPairsIntoRecord(\n        getEnv().OTEL_EXPORTER_OTLP_TRACES_HEADERS\n      )\n    );\n  }\n\n  getDefaultUrl(config: OTLPExporterConfigBase): string {\n    return typeof config.url === 'string'\n      ? config.url\n      : getEnv().OTEL_EXPORTER_OTLP_TRACES_ENDPOINT.length > 0\n      ? appendRootPathToUrlIfNeeded(getEnv().OTEL_EXPORTER_OTLP_TRACES_ENDPOINT)\n      : getEnv().OTEL_EXPORTER_OTLP_ENDPOINT.length > 0\n      ? appendResourcePathToUrl(\n          getEnv().OTEL_EXPORTER_OTLP_ENDPOINT,\n          DEFAULT_COLLECTOR_RESOURCE_PATH\n        )\n      : DEFAULT_COLLECTOR_URL;\n  }\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;;AAGH,OAAO,EAAE,MAAM,EAAE,YAAY,EAAE,MAAM,qBAAqB,CAAC;;AAC3D,OAAO,EAEL,uBAAuB,EACvB,2BAA2B,EAC3B,uBAAuB,GACxB,MAAM,mCAAmC,CAAC;AAC3C,OAAO,EAEL,uBAAuB,GACxB,MAAM,iCAAiC,CAAC;;;;;;;;;;;;;;;;;;;;;;;;AAEzC,IAAM,+BAA+B,GAAG,WAAW,CAAC;AACpD,IAAM,qBAAqB,GAAG,2BAAyB,+BAAiC,CAAC;AAEzF;;GAEG,CACH,IAAA,oBAAA,SAAA,MAAA;IACU,UAAA,mBAAA,QAAkE;IAG1E,SAAA,kBAAY,MAAmC;QAAnC,IAAA,WAAA,KAAA,GAAA;YAAA,SAAA,CAAA,CAAmC;QAAA;QAA/C,IAAA,QACE,OAAA,IAAA,CAAA,IAAA,EAAM,MAAM,uMAAE,0BAAuB,EAAE,wBAAwB,CAAC,IAAA,IAAA,CAOjE;QANC,KAAI,CAAC,QAAQ,GAAG,MAAM,CAAC,MAAM,CAC3B,KAAI,CAAC,QAAQ,kSACb,eAAY,CAAC,uBAAuB,+QAClC,SAAA,AAAM,EAAE,EAAC,iCAAiC,CAC3C,CACF,CAAC;;IACJ,CAAC;IAED,kBAAA,SAAA,CAAA,aAAa,GAAb,SAAc,MAA8B;QAC1C,OAAO,OAAO,MAAM,CAAC,GAAG,KAAK,QAAQ,GACjC,MAAM,CAAC,GAAG,iRACV,SAAA,AAAM,EAAE,EAAC,kCAAkC,CAAC,MAAM,GAAG,CAAC,8LACtD,8BAAA,AAA2B,gRAAC,SAAA,AAAM,EAAE,EAAC,kCAAkC,CAAC,iRACxE,SAAA,AAAM,EAAE,EAAC,2BAA2B,CAAC,MAAM,GAAG,CAAC,8LAC/C,0BAAA,AAAuB,gRACrB,SAAA,AAAM,EAAE,EAAC,2BAA2B,EACpC,+BAA+B,CAChC,GACD,qBAAqB,CAAC;IAC5B,CAAC;IACH,OAAA,iBAAC;AAAD,CAAC,AA1BD,kOACU,0BAAuB,GAyBhC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4165, "column": 0}, "map": {"version": 3, "file": "util.js", "sourceRoot": "", "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/node_modules/%40opentelemetry/otlp-exporter-base/src/util.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { diag } from '@opentelemetry/api';\nimport { getEnv } from '@opentelemetry/core';\n\nconst DEFAULT_TRACE_TIMEOUT = 10000;\nexport const DEFAULT_EXPORT_MAX_ATTEMPTS = 5;\nexport const DEFAULT_EXPORT_INITIAL_BACKOFF = 1000;\nexport const DEFAULT_EXPORT_MAX_BACKOFF = 5000;\nexport const DEFAULT_EXPORT_BACKOFF_MULTIPLIER = 1.5;\n\n/**\n * Parses headers from config leaving only those that have defined values\n * @param partialHeaders\n */\nexport function parseHeaders(\n  partialHeaders: Partial<Record<string, unknown>> = {}\n): Record<string, string> {\n  const headers: Record<string, string> = {};\n  Object.entries(partialHeaders).forEach(([key, value]) => {\n    if (typeof value !== 'undefined') {\n      headers[key] = String(value);\n    } else {\n      diag.warn(\n        `Header \"${key}\" has invalid value (${value}) and will be ignored`\n      );\n    }\n  });\n  return headers;\n}\n\n/**\n * Adds path (version + signal) to a no per-signal endpoint\n * @param url\n * @param path\n * @returns url + path\n */\nexport function appendResourcePathToUrl(url: string, path: string): string {\n  if (!url.endsWith('/')) {\n    url = url + '/';\n  }\n  return url + path;\n}\n\n/**\n * Adds root path to signal specific endpoint when endpoint contains no path part and no root path\n * @param url\n * @returns url\n */\nexport function appendRootPathToUrlIfNeeded(url: string): string {\n  try {\n    const parsedUrl = new URL(url);\n    if (parsedUrl.pathname === '') {\n      parsedUrl.pathname = parsedUrl.pathname + '/';\n    }\n    return parsedUrl.toString();\n  } catch {\n    diag.warn(`Could not parse export URL: '${url}'`);\n    return url;\n  }\n}\n\n/**\n * Configure exporter trace timeout value from passed in value or environment variables\n * @param timeoutMillis\n * @returns timeout value in milliseconds\n */\nexport function configureExporterTimeout(\n  timeoutMillis: number | undefined\n): number {\n  if (typeof timeoutMillis === 'number') {\n    if (timeoutMillis <= 0) {\n      // OTLP exporter configured timeout - using default value of 10000ms\n      return invalidTimeout(timeoutMillis, DEFAULT_TRACE_TIMEOUT);\n    }\n    return timeoutMillis;\n  } else {\n    return getExporterTimeoutFromEnv();\n  }\n}\n\nfunction getExporterTimeoutFromEnv(): number {\n  const definedTimeout = Number(\n    getEnv().OTEL_EXPORTER_OTLP_TRACES_TIMEOUT ??\n      getEnv().OTEL_EXPORTER_OTLP_TIMEOUT\n  );\n\n  if (definedTimeout <= 0) {\n    // OTLP exporter configured timeout - using default value of 10000ms\n    return invalidTimeout(definedTimeout, DEFAULT_TRACE_TIMEOUT);\n  } else {\n    return definedTimeout;\n  }\n}\n\n// OTLP exporter configured timeout - using default value of 10000ms\nexport function invalidTimeout(\n  timeout: number,\n  defaultTimeout: number\n): number {\n  diag.warn('Timeout must be greater than 0', timeout);\n\n  return defaultTimeout;\n}\n\nexport function isExportRetryable(statusCode: number): boolean {\n  const retryCodes = [429, 502, 503, 504];\n\n  return retryCodes.includes(statusCode);\n}\n\nexport function parseRetryAfterToMills(retryAfter?: string | null): number {\n  if (retryAfter == null) {\n    return -1;\n  }\n  const seconds = Number.parseInt(retryAfter, 10);\n  if (Number.isInteger(seconds)) {\n    return seconds > 0 ? seconds * 1000 : -1;\n  }\n  // https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/Retry-After#directives\n  const delay = new Date(retryAfter).getTime() - Date.now();\n\n  if (delay >= 0) {\n    return delay;\n  }\n  return 0;\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;;;;;;;;;;;AAEH,OAAO,EAAE,IAAI,EAAE,MAAM,oBAAoB,CAAC;AAC1C,OAAO,EAAE,MAAM,EAAE,MAAM,qBAAqB,CAAC;;;;;;;;;;;;;;;;;;;;;;AAE7C,IAAM,qBAAqB,GAAG,KAAK,CAAC;AAC7B,IAAM,2BAA2B,GAAG,CAAC,CAAC;AACtC,IAAM,8BAA8B,GAAG,IAAI,CAAC;AAC5C,IAAM,0BAA0B,GAAG,IAAI,CAAC;AACxC,IAAM,iCAAiC,GAAG,GAAG,CAAC;AAM/C,SAAU,YAAY,CAC1B,cAAqD;IAArD,IAAA,mBAAA,KAAA,GAAA;QAAA,iBAAA,CAAA,CAAqD;IAAA;IAErD,IAAM,OAAO,GAA2B,CAAA,CAAE,CAAC;IAC3C,MAAM,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC,OAAO,CAAC,SAAC,EAAY;YAAZ,KAAA,OAAA,IAAA,EAAY,EAAX,GAAG,GAAA,EAAA,CAAA,EAAA,EAAE,KAAK,GAAA,EAAA,CAAA,EAAA;QACjD,IAAI,OAAO,KAAK,KAAK,WAAW,EAAE;YAChC,OAAO,CAAC,GAAG,CAAC,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC;SAC9B,MAAM;qLACL,OAAI,CAAC,IAAI,CACP,cAAW,GAAG,GAAA,2BAAwB,KAAK,GAAA,uBAAuB,CACnE,CAAC;SACH;IACH,CAAC,CAAC,CAAC;IACH,OAAO,OAAO,CAAC;AACjB,CAAC;AAQK,SAAU,uBAAuB,CAAC,GAAW,EAAE,IAAY;IAC/D,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE;QACtB,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC;KACjB;IACD,OAAO,GAAG,GAAG,IAAI,CAAC;AACpB,CAAC;AAOK,SAAU,2BAA2B,CAAC,GAAW;IACrD,IAAI;QACF,IAAM,SAAS,GAAG,IAAI,GAAG,CAAC,GAAG,CAAC,CAAC;QAC/B,IAAI,SAAS,CAAC,QAAQ,KAAK,EAAE,EAAE;YAC7B,SAAS,CAAC,QAAQ,GAAG,SAAS,CAAC,QAAQ,GAAG,GAAG,CAAC;SAC/C;QACD,OAAO,SAAS,CAAC,QAAQ,EAAE,CAAC;KAC7B,CAAC,OAAA,IAAM;iLACN,OAAI,CAAC,IAAI,CAAC,kCAAgC,GAAG,GAAA,GAAG,CAAC,CAAC;QAClD,OAAO,GAAG,CAAC;KACZ;AACH,CAAC;AAOK,SAAU,wBAAwB,CACtC,aAAiC;IAEjC,IAAI,OAAO,aAAa,KAAK,QAAQ,EAAE;QACrC,IAAI,aAAa,IAAI,CAAC,EAAE;YACtB,oEAAoE;YACpE,OAAO,cAAc,CAAC,aAAa,EAAE,qBAAqB,CAAC,CAAC;SAC7D;QACD,OAAO,aAAa,CAAC;KACtB,MAAM;QACL,OAAO,yBAAyB,EAAE,CAAC;KACpC;AACH,CAAC;AAED,SAAS,yBAAyB;;IAChC,IAAM,cAAc,GAAG,MAAM,CAC3B,CAAA,yQAAA,SAAA,AAAM,EAAE,EAAC,iCAAiC,MAAA,QAAA,OAAA,KAAA,IAAA,KACxC,6QAAA,AAAM,EAAE,EAAC,0BAA0B,CACtC,CAAC;IAEF,IAAI,cAAc,IAAI,CAAC,EAAE;QACvB,oEAAoE;QACpE,OAAO,cAAc,CAAC,cAAc,EAAE,qBAAqB,CAAC,CAAC;KAC9D,MAAM;QACL,OAAO,cAAc,CAAC;KACvB;AACH,CAAC;AAGK,SAAU,cAAc,CAC5B,OAAe,EACf,cAAsB;6KAEtB,OAAI,CAAC,IAAI,CAAC,gCAAgC,EAAE,OAAO,CAAC,CAAC;IAErD,OAAO,cAAc,CAAC;AACxB,CAAC;AAEK,SAAU,iBAAiB,CAAC,UAAkB;IAClD,IAAM,UAAU,GAAG;QAAC,GAAG;QAAE,GAAG;QAAE,GAAG;QAAE,GAAG;KAAC,CAAC;IAExC,OAAO,UAAU,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;AACzC,CAAC;AAEK,SAAU,sBAAsB,CAAC,UAA0B;IAC/D,IAAI,UAAU,IAAI,IAAI,EAAE;QACtB,OAAO,CAAC,CAAC,CAAC;KACX;IACD,IAAM,OAAO,GAAG,MAAM,CAAC,QAAQ,CAAC,UAAU,EAAE,EAAE,CAAC,CAAC;IAChD,IAAI,MAAM,CAAC,SAAS,CAAC,OAAO,CAAC,EAAE;QAC7B,OAAO,OAAO,GAAG,CAAC,CAAC,CAAC,CAAC,OAAO,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;KAC1C;IACD,mFAAmF;IACnF,IAAM,KAAK,GAAG,IAAI,IAAI,CAAC,UAAU,CAAC,CAAC,OAAO,EAAE,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;IAE1D,IAAI,KAAK,IAAI,CAAC,EAAE;QACd,OAAO,KAAK,CAAC;KACd;IACD,OAAO,CAAC,CAAC;AACX,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4308, "column": 0}, "map": {"version": 3, "file": "OTLPExporterBase.js", "sourceRoot": "", "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/node_modules/%40opentelemetry/otlp-exporter-base/src/OTLPExporterBase.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { diag } from '@opentelemetry/api';\nimport {\n  ExportResult,\n  ExportResultCode,\n  BindOnceFuture,\n} from '@opentelemetry/core';\nimport {\n  OTLPExporterError,\n  OTLPExporterConfigBase,\n  ExportServiceError,\n} from './types';\nimport { configureExporterTimeout } from './util';\n\n/**\n * Collector Exporter abstract base class\n */\nexport abstract class OTLPExporterBase<\n  T extends OTLPExporterConfigBase,\n  ExportItem,\n> {\n  public readonly url: string;\n  public readonly hostname: string | undefined;\n  public readonly timeoutMillis: number;\n  protected _concurrencyLimit: number;\n  protected _sendingPromises: Promise<unknown>[] = [];\n  protected _shutdownOnce: BindOnceFuture<void>;\n\n  /**\n   * @param config\n   */\n  constructor(config: T = {} as T) {\n    this.url = this.getDefaultUrl(config);\n    if (typeof config.hostname === 'string') {\n      this.hostname = config.hostname;\n    }\n\n    this.shutdown = this.shutdown.bind(this);\n    this._shutdownOnce = new BindOnceFuture(this._shutdown, this);\n\n    this._concurrencyLimit =\n      typeof config.concurrencyLimit === 'number'\n        ? config.concurrencyLimit\n        : 30;\n\n    this.timeoutMillis = configureExporterTimeout(config.timeoutMillis);\n\n    // platform dependent\n    this.onInit(config);\n  }\n\n  /**\n   * Export items.\n   * @param items\n   * @param resultCallback\n   */\n  export(\n    items: ExportItem[],\n    resultCallback: (result: ExportResult) => void\n  ): void {\n    if (this._shutdownOnce.isCalled) {\n      resultCallback({\n        code: ExportResultCode.FAILED,\n        error: new Error('Exporter has been shutdown'),\n      });\n      return;\n    }\n\n    if (this._sendingPromises.length >= this._concurrencyLimit) {\n      resultCallback({\n        code: ExportResultCode.FAILED,\n        error: new Error('Concurrent export limit reached'),\n      });\n      return;\n    }\n\n    this._export(items)\n      .then(() => {\n        resultCallback({ code: ExportResultCode.SUCCESS });\n      })\n      .catch((error: ExportServiceError) => {\n        resultCallback({ code: ExportResultCode.FAILED, error });\n      });\n  }\n\n  private _export(items: ExportItem[]): Promise<unknown> {\n    return new Promise<void>((resolve, reject) => {\n      try {\n        diag.debug('items to be sent', items);\n        this.send(items, resolve, reject);\n      } catch (e) {\n        reject(e);\n      }\n    });\n  }\n\n  /**\n   * Shutdown the exporter.\n   */\n  shutdown(): Promise<void> {\n    return this._shutdownOnce.call();\n  }\n\n  /**\n   * Exports any pending spans in the exporter\n   */\n  forceFlush(): Promise<void> {\n    return Promise.all(this._sendingPromises).then(() => {\n      /** ignore resolved values */\n    });\n  }\n\n  /**\n   * Called by _shutdownOnce with BindOnceFuture\n   */\n  private _shutdown(): Promise<void> {\n    diag.debug('shutdown started');\n    this.onShutdown();\n    return this.forceFlush();\n  }\n\n  abstract onShutdown(): void;\n  abstract onInit(config: T): void;\n  abstract send(\n    items: ExportItem[],\n    onSuccess: () => void,\n    onError: (error: OTLPExporterError) => void\n  ): void;\n  abstract getDefaultUrl(config: T): string;\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;AAEH,OAAO,EAAE,IAAI,EAAE,MAAM,oBAAoB,CAAC;AAC1C,OAAO,EAEL,gBAAgB,EAChB,cAAc,GACf,MAAM,qBAAqB,CAAC;;AAM7B,OAAO,EAAE,wBAAwB,EAAE,MAAM,QAAQ,CAAC;;;;AAElD;;GAEG,CACH,IAAA,mBAAA;IAWE;;OAEG,CACH,SAAA,iBAAY,MAAmB;QAAnB,IAAA,WAAA,KAAA,GAAA;YAAA,SAAY,CAAA,CAAO;QAAA;QANrB,IAAA,CAAA,gBAAgB,GAAuB,EAAE,CAAC;QAOlD,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC;QACtC,IAAI,OAAO,MAAM,CAAC,QAAQ,KAAK,QAAQ,EAAE;YACvC,IAAI,CAAC,QAAQ,GAAG,MAAM,CAAC,QAAQ,CAAC;SACjC;QAED,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACzC,IAAI,CAAC,aAAa,GAAG,mPAAI,iBAAc,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,CAAC;QAE9D,IAAI,CAAC,iBAAiB,GACpB,OAAO,MAAM,CAAC,gBAAgB,KAAK,QAAQ,GACvC,MAAM,CAAC,gBAAgB,GACvB,EAAE,CAAC;QAET,IAAI,CAAC,aAAa,GAAG,sNAAA,AAAwB,EAAC,MAAM,CAAC,aAAa,CAAC,CAAC;QAEpE,qBAAqB;QACrB,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;IACtB,CAAC;IAED;;;;OAIG,CACH,iBAAA,SAAA,CAAA,MAAM,GAAN,SACE,KAAmB,EACnB,cAA8C;QAE9C,IAAI,IAAI,CAAC,aAAa,CAAC,QAAQ,EAAE;YAC/B,cAAc,CAAC;gBACb,IAAI,4OAAE,mBAAgB,CAAC,MAAM;gBAC7B,KAAK,EAAE,IAAI,KAAK,CAAC,4BAA4B,CAAC;aAC/C,CAAC,CAAC;YACH,OAAO;SACR;QAED,IAAI,IAAI,CAAC,gBAAgB,CAAC,MAAM,IAAI,IAAI,CAAC,iBAAiB,EAAE;YAC1D,cAAc,CAAC;gBACb,IAAI,4OAAE,mBAAgB,CAAC,MAAM;gBAC7B,KAAK,EAAE,IAAI,KAAK,CAAC,iCAAiC,CAAC;aACpD,CAAC,CAAC;YACH,OAAO;SACR;QAED,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAChB,IAAI,CAAC;YACJ,cAAc,CAAC;gBAAE,IAAI,4OAAE,mBAAgB,CAAC,OAAO;YAAA,CAAE,CAAC,CAAC;QACrD,CAAC,CAAC,CACD,KAAK,CAAC,SAAC,KAAyB;YAC/B,cAAc,CAAC;gBAAE,IAAI,EAAE,6PAAgB,CAAC,MAAM;gBAAE,KAAK,EAAA,KAAA;YAAA,CAAE,CAAC,CAAC;QAC3D,CAAC,CAAC,CAAC;IACP,CAAC;IAEO,iBAAA,SAAA,CAAA,OAAO,GAAf,SAAgB,KAAmB;QAAnC,IAAA,QAAA,IAAA,CASC;QARC,OAAO,IAAI,OAAO,CAAO,SAAC,OAAO,EAAE,MAAM;YACvC,IAAI;yLACF,OAAI,CAAC,KAAK,CAAC,kBAAkB,EAAE,KAAK,CAAC,CAAC;gBACtC,KAAI,CAAC,IAAI,CAAC,KAAK,EAAE,OAAO,EAAE,MAAM,CAAC,CAAC;aACnC,CAAC,OAAO,CAAC,EAAE;gBACV,MAAM,CAAC,CAAC,CAAC,CAAC;aACX;QACH,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;OAEG,CACH,iBAAA,SAAA,CAAA,QAAQ,GAAR;QACE,OAAO,IAAI,CAAC,aAAa,CAAC,IAAI,EAAE,CAAC;IACnC,CAAC;IAED;;OAEG,CACH,iBAAA,SAAA,CAAA,UAAU,GAAV;QACE,OAAO,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC,IAAI,CAAC;QAC7C,2BAAA,EAA6B,CAC/B,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;OAEG,CACK,iBAAA,SAAA,CAAA,SAAS,GAAjB;iLACE,OAAI,CAAC,KAAK,CAAC,kBAAkB,CAAC,CAAC;QAC/B,IAAI,CAAC,UAAU,EAAE,CAAC;QAClB,OAAO,IAAI,CAAC,UAAU,EAAE,CAAC;IAC3B,CAAC;IAUH,OAAA,gBAAC;AAAD,CAAC,AAhHD,IAgHC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4422, "column": 0}, "map": {"version": 3, "file": "types.js", "sourceRoot": "", "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/node_modules/%40opentelemetry/otlp-exporter-base/src/types.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n/**\n * Interface for handling error\n */\nexport class OTLPExporterError extends Error {\n  readonly code?: number;\n  override readonly name: string = 'OTLPExporterError';\n  readonly data?: string;\n\n  constructor(message?: string, code?: number, data?: string) {\n    super(message);\n    this.data = data;\n    this.code = code;\n  }\n}\n\n/**\n * Interface for handling export service errors\n */\nexport interface ExportServiceError {\n  name: string;\n  code: number;\n  details: string;\n  metadata: { [key: string]: unknown };\n  message: string;\n  stack: string;\n}\n\n/**\n * Collector Exporter base config\n */\nexport interface OTLPExporterConfigBase {\n  headers?: Partial<Record<string, unknown>>;\n  hostname?: string;\n  url?: string;\n  concurrencyLimit?: number;\n  /** Maximum time the OTLP exporter will wait for each batch export.\n   * The default value is 10000ms. */\n  timeoutMillis?: number;\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;;;;;;;;;;;;;;;;;;;;;AAEH;;GAEG,CACH,IAAA,oBAAA,SAAA,MAAA;IAAuC,UAAA,mBAAA,QAAK;IAK1C,SAAA,kBAAY,OAAgB,EAAE,IAAa,EAAE,IAAa;QAA1D,IAAA,QACE,OAAA,IAAA,CAAA,IAAA,EAAM,OAAO,CAAC,IAAA,IAAA,CAGf;QAPiB,MAAA,IAAI,GAAW,mBAAmB,CAAC;QAKnD,KAAI,CAAC,IAAI,GAAG,IAAI,CAAC;QACjB,KAAI,CAAC,IAAI,GAAG,IAAI,CAAC;;IACnB,CAAC;IACH,OAAA,iBAAC;AAAD,CAAC,AAVD,CAAuC,KAAK,GAU3C", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4480, "column": 0}, "map": {"version": 3, "file": "util.js", "sourceRoot": "", "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/node_modules/%40opentelemetry/otlp-exporter-base/src/platform/browser/util.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nimport { diag } from '@opentelemetry/api';\nimport { OTLPExporterError } from '../../types';\nimport {\n  DEFAULT_EXPORT_MAX_ATTEMPTS,\n  DEFAULT_EXPORT_INITIAL_BACKOFF,\n  DEFAULT_EXPORT_BACKOFF_MULTIPLIER,\n  DEFAULT_EXPORT_MAX_BACKOFF,\n  isExportRetryable,\n  parseRetryAfterToMills,\n} from '../../util';\n\n/**\n * Send metrics/spans using browser navigator.sendBeacon\n * @param body\n * @param url\n * @param blobPropertyBag\n * @param onSuccess\n * @param onError\n */\nexport function sendWithBeacon(\n  body: Uint8Array,\n  url: string,\n  blobPropertyBag: BlobPropertyBag,\n  onSuccess: () => void,\n  onError: (error: OTLPExporterError) => void\n): void {\n  if (navigator.sendBeacon(url, new Blob([body], blobPropertyBag))) {\n    diag.debug('sendBeacon - can send', body);\n    onSuccess();\n  } else {\n    const error = new OTLPExporterError(`sendBeacon - cannot send ${body}`);\n    onError(error);\n  }\n}\n\n/**\n * function to send metrics/spans using browser XMLHttpRequest\n *     used when navigator.sendBeacon is not available\n * @param body\n * @param url\n * @param headers\n * @param onSuccess\n * @param onError\n */\nexport function sendWithXhr(\n  body: Uint8Array,\n  url: string,\n  headers: Record<string, string>,\n  exporterTimeout: number,\n  onSuccess: () => void,\n  onError: (error: OTLPExporterError) => void\n): void {\n  let retryTimer: ReturnType<typeof setTimeout>;\n  let xhr: XMLHttpRequest;\n  let reqIsDestroyed = false;\n\n  const exporterTimer = setTimeout(() => {\n    clearTimeout(retryTimer);\n    reqIsDestroyed = true;\n\n    if (xhr.readyState === XMLHttpRequest.DONE) {\n      const err = new OTLPExporterError('Request Timeout');\n      onError(err);\n    } else {\n      xhr.abort();\n    }\n  }, exporterTimeout);\n\n  const sendWithRetry = (\n    retries = DEFAULT_EXPORT_MAX_ATTEMPTS,\n    minDelay = DEFAULT_EXPORT_INITIAL_BACKOFF\n  ) => {\n    xhr = new XMLHttpRequest();\n    xhr.open('POST', url);\n\n    const defaultHeaders = {\n      Accept: 'application/json',\n      'Content-Type': 'application/json',\n    };\n\n    Object.entries({\n      ...defaultHeaders,\n      ...headers,\n    }).forEach(([k, v]) => {\n      xhr.setRequestHeader(k, v);\n    });\n\n    xhr.send(body);\n\n    xhr.onreadystatechange = () => {\n      if (xhr.readyState === XMLHttpRequest.DONE && reqIsDestroyed === false) {\n        if (xhr.status >= 200 && xhr.status <= 299) {\n          diag.debug('xhr success', body);\n          onSuccess();\n          clearTimeout(exporterTimer);\n          clearTimeout(retryTimer);\n        } else if (xhr.status && isExportRetryable(xhr.status) && retries > 0) {\n          let retryTime: number;\n          minDelay = DEFAULT_EXPORT_BACKOFF_MULTIPLIER * minDelay;\n\n          // retry after interval specified in Retry-After header\n          if (xhr.getResponseHeader('Retry-After')) {\n            retryTime = parseRetryAfterToMills(\n              xhr.getResponseHeader('Retry-After')!\n            );\n          } else {\n            // exponential backoff with jitter\n            retryTime = Math.round(\n              Math.random() * (DEFAULT_EXPORT_MAX_BACKOFF - minDelay) + minDelay\n            );\n          }\n\n          retryTimer = setTimeout(() => {\n            sendWithRetry(retries - 1, minDelay);\n          }, retryTime);\n        } else {\n          const error = new OTLPExporterError(\n            `Failed to export with XHR (status: ${xhr.status})`,\n            xhr.status\n          );\n          onError(error);\n          clearTimeout(exporterTimer);\n          clearTimeout(retryTimer);\n        }\n      }\n    };\n\n    xhr.onabort = () => {\n      if (reqIsDestroyed) {\n        const err = new OTLPExporterError('Request Timeout');\n        onError(err);\n      }\n      clearTimeout(exporterTimer);\n      clearTimeout(retryTimer);\n    };\n\n    xhr.onerror = () => {\n      if (reqIsDestroyed) {\n        const err = new OTLPExporterError('Request Timeout');\n        onError(err);\n      }\n      clearTimeout(exporterTimer);\n      clearTimeout(retryTimer);\n    };\n  };\n\n  sendWithRetry();\n}\n"], "names": [], "mappings": ";;;;AAAA;;;;;;;;;;;;;;GAcG,CACH,OAAO,EAAE,IAAI,EAAE,MAAM,oBAAoB,CAAC;AAC1C,OAAO,EAAE,iBAAiB,EAAE,MAAM,aAAa,CAAC;AAChD,OAAO,EACL,2BAA2B,EAC3B,8BAA8B,EAC9B,iCAAiC,EACjC,0BAA0B,EAC1B,iBAAiB,EACjB,sBAAsB,GACvB,MAAM,YAAY,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUd,SAAU,cAAc,CAC5B,IAAgB,EAChB,GAAW,EACX,eAAgC,EAChC,SAAqB,EACrB,OAA2C;IAE3C,IAAI,SAAS,CAAC,UAAU,CAAC,GAAG,EAAE,IAAI,IAAI,CAAC;QAAC,IAAI;KAAC,EAAE,eAAe,CAAC,CAAC,EAAE;iLAChE,OAAI,CAAC,KAAK,CAAC,uBAAuB,EAAE,IAAI,CAAC,CAAC;QAC1C,SAAS,EAAE,CAAC;KACb,MAAM;QACL,IAAM,KAAK,GAAG,4LAAI,oBAAiB,CAAC,8BAA4B,IAAM,CAAC,CAAC;QACxE,OAAO,CAAC,KAAK,CAAC,CAAC;KAChB;AACH,CAAC;AAWK,SAAU,WAAW,CACzB,IAAgB,EAChB,GAAW,EACX,OAA+B,EAC/B,eAAuB,EACvB,SAAqB,EACrB,OAA2C;IAE3C,IAAI,UAAyC,CAAC;IAC9C,IAAI,GAAmB,CAAC;IACxB,IAAI,cAAc,GAAG,KAAK,CAAC;IAE3B,IAAM,aAAa,GAAG,UAAU,CAAC;QAC/B,YAAY,CAAC,UAAU,CAAC,CAAC;QACzB,cAAc,GAAG,IAAI,CAAC;QAEtB,IAAI,GAAG,CAAC,UAAU,KAAK,cAAc,CAAC,IAAI,EAAE;YAC1C,IAAM,GAAG,GAAG,2LAAI,qBAAiB,CAAC,iBAAiB,CAAC,CAAC;YACrD,OAAO,CAAC,GAAG,CAAC,CAAC;SACd,MAAM;YACL,GAAG,CAAC,KAAK,EAAE,CAAC;SACb;IACH,CAAC,EAAE,eAAe,CAAC,CAAC;IAEpB,IAAM,aAAa,GAAG,SACpB,OAAqC,EACrC,QAAyC;QADzC,IAAA,YAAA,KAAA,GAAA;YAAA,UAAA,sLAAA,CAAA,8BAAqC;QAAA;QACrC,IAAA,aAAA,KAAA,GAAA;YAAA,WAAA,sLAAA,CAAA,iCAAyC;QAAA;QAEzC,GAAG,GAAG,IAAI,cAAc,EAAE,CAAC;QAC3B,GAAG,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC;QAEtB,IAAM,cAAc,GAAG;YACrB,MAAM,EAAE,kBAAkB;YAC1B,cAAc,EAAE,kBAAkB;SACnC,CAAC;QAEF,MAAM,CAAC,OAAO,CAAA,SAAA,SAAA,CAAA,GACT,cAAc,GACd,OAAO,EACV,CAAC,OAAO,CAAC,SAAC,EAAM;gBAAN,KAAA,OAAA,IAAA,EAAM,EAAL,CAAC,GAAA,EAAA,CAAA,EAAA,EAAE,CAAC,GAAA,EAAA,CAAA,EAAA;YACf,GAAG,CAAC,gBAAgB,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QAC7B,CAAC,CAAC,CAAC;QAEH,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAEf,GAAG,CAAC,kBAAkB,GAAG;YACvB,IAAI,GAAG,CAAC,UAAU,KAAK,cAAc,CAAC,IAAI,IAAI,cAAc,KAAK,KAAK,EAAE;gBACtE,IAAI,GAAG,CAAC,MAAM,IAAI,GAAG,IAAI,GAAG,CAAC,MAAM,IAAI,GAAG,EAAE;6LAC1C,OAAI,CAAC,KAAK,CAAC,aAAa,EAAE,IAAI,CAAC,CAAC;oBAChC,SAAS,EAAE,CAAC;oBACZ,YAAY,CAAC,aAAa,CAAC,CAAC;oBAC5B,YAAY,CAAC,UAAU,CAAC,CAAC;iBAC1B,MAAM,IAAI,GAAG,CAAC,MAAM,+LAAI,oBAAA,AAAiB,EAAC,GAAG,CAAC,MAAM,CAAC,IAAI,OAAO,GAAG,CAAC,EAAE;oBACrE,IAAI,SAAS,GAAA,KAAA,CAAQ,CAAC;oBACtB,QAAQ,0LAAG,oCAAiC,GAAG,QAAQ,CAAC;oBAExD,uDAAuD;oBACvD,IAAI,GAAG,CAAC,iBAAiB,CAAC,aAAa,CAAC,EAAE;wBACxC,SAAS,8LAAG,yBAAA,AAAsB,EAChC,GAAG,CAAC,iBAAiB,CAAC,aAAa,CAAE,CACtC,CAAC;qBACH,MAAM;wBACL,kCAAkC;wBAClC,SAAS,GAAG,IAAI,CAAC,KAAK,CACpB,IAAI,CAAC,MAAM,EAAE,GAAG,wLAAC,6BAA0B,GAAG,QAAQ,CAAC,GAAG,QAAQ,CACnE,CAAC;qBACH;oBAED,UAAU,GAAG,UAAU,CAAC;wBACtB,aAAa,CAAC,OAAO,GAAG,CAAC,EAAE,QAAQ,CAAC,CAAC;oBACvC,CAAC,EAAE,SAAS,CAAC,CAAC;iBACf,MAAM;oBACL,IAAM,KAAK,GAAG,4LAAI,oBAAiB,CACjC,wCAAsC,GAAG,CAAC,MAAM,GAAA,GAAG,EACnD,GAAG,CAAC,MAAM,CACX,CAAC;oBACF,OAAO,CAAC,KAAK,CAAC,CAAC;oBACf,YAAY,CAAC,aAAa,CAAC,CAAC;oBAC5B,YAAY,CAAC,UAAU,CAAC,CAAC;iBAC1B;aACF;QACH,CAAC,CAAC;QAEF,GAAG,CAAC,OAAO,GAAG;YACZ,IAAI,cAAc,EAAE;gBAClB,IAAM,GAAG,GAAG,4LAAI,oBAAiB,CAAC,iBAAiB,CAAC,CAAC;gBACrD,OAAO,CAAC,GAAG,CAAC,CAAC;aACd;YACD,YAAY,CAAC,aAAa,CAAC,CAAC;YAC5B,YAAY,CAAC,UAAU,CAAC,CAAC;QAC3B,CAAC,CAAC;QAEF,GAAG,CAAC,OAAO,GAAG;YACZ,IAAI,cAAc,EAAE;gBAClB,IAAM,GAAG,GAAG,4LAAI,oBAAiB,CAAC,iBAAiB,CAAC,CAAC;gBACrD,OAAO,CAAC,GAAG,CAAC,CAAC;aACd;YACD,YAAY,CAAC,aAAa,CAAC,CAAC;YAC5B,YAAY,CAAC,UAAU,CAAC,CAAC;QAC3B,CAAC,CAAC;IACJ,CAAC,CAAC;IAEF,aAAa,EAAE,CAAC;AAClB,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4629, "column": 0}, "map": {"version": 3, "file": "OTLPExporterBrowserBase.js", "sourceRoot": "", "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/node_modules/%40opentelemetry/otlp-exporter-base/src/platform/browser/OTLPExporterBrowserBase.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { OTLPExporterBase } from '../../OTLPExporterBase';\nimport { OTLPExporterConfigBase } from '../../types';\nimport * as otlpTypes from '../../types';\nimport { parseHeaders } from '../../util';\nimport { sendWithBeacon, sendWithXhr } from './util';\nimport { diag } from '@opentelemetry/api';\nimport { getEnv, baggageUtils } from '@opentelemetry/core';\nimport { ISerializer } from '@opentelemetry/otlp-transformer';\n\n/**\n * Collector Metric Exporter abstract base class\n */\nexport abstract class OTLPExporterBrowserBase<\n  ExportItem,\n  ServiceResponse,\n> extends OTLPExporterBase<OTLPExporterConfigBase, ExportItem> {\n  protected _headers: Record<string, string>;\n  private _useXHR: boolean = false;\n  private _contentType: string;\n  private _serializer: ISerializer<ExportItem[], ServiceResponse>;\n\n  /**\n   * @param config\n   * @param serializer\n   * @param contentType\n   */\n  constructor(\n    config: OTLPExporterConfigBase = {},\n    serializer: ISerializer<ExportItem[], ServiceResponse>,\n    contentType: string\n  ) {\n    super(config);\n    this._serializer = serializer;\n    this._contentType = contentType;\n    this._useXHR =\n      !!config.headers || typeof navigator.sendBeacon !== 'function';\n    if (this._useXHR) {\n      this._headers = Object.assign(\n        {},\n        parseHeaders(config.headers),\n        baggageUtils.parseKeyPairsIntoRecord(\n          getEnv().OTEL_EXPORTER_OTLP_HEADERS\n        )\n      );\n    } else {\n      this._headers = {};\n    }\n  }\n\n  onInit(): void {}\n\n  onShutdown(): void {}\n\n  send(\n    items: ExportItem[],\n    onSuccess: () => void,\n    onError: (error: otlpTypes.OTLPExporterError) => void\n  ): void {\n    if (this._shutdownOnce.isCalled) {\n      diag.debug('Shutdown already started. Cannot send objects');\n      return;\n    }\n    const body = this._serializer.serializeRequest(items) ?? new Uint8Array();\n\n    const promise = new Promise<void>((resolve, reject) => {\n      if (this._useXHR) {\n        sendWithXhr(\n          body,\n          this.url,\n          {\n            ...this._headers,\n            'Content-Type': this._contentType,\n          },\n          this.timeoutMillis,\n          resolve,\n          reject\n        );\n      } else {\n        sendWithBeacon(\n          body,\n          this.url,\n          { type: this._contentType },\n          resolve,\n          reject\n        );\n      }\n    }).then(onSuccess, onError);\n\n    this._sendingPromises.push(promise);\n    const popPromise = () => {\n      const index = this._sendingPromises.indexOf(promise);\n      this._sendingPromises.splice(index, 1);\n    };\n    promise.then(popPromise, popPromise);\n  }\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;AAEH,OAAO,EAAE,gBAAgB,EAAE,MAAM,wBAAwB,CAAC;AAG1D,OAAO,EAAE,YAAY,EAAE,MAAM,YAAY,CAAC;AAC1C,OAAO,EAAE,cAAc,EAAE,WAAW,EAAE,MAAM,QAAQ,CAAC;AACrD,OAAO,EAAE,IAAI,EAAE,MAAM,oBAAoB,CAAC;;AAC1C,OAAO,EAAE,MAAM,EAAE,YAAY,EAAE,MAAM,qBAAqB,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAG3D;;GAEG,CACH,IAAA,0BAAA,SAAA,MAAA;IAGU,UAAA,yBAAA,QAAoD;IAM5D;;;;OAIG,CACH,SAAA,wBACE,MAAmC,EACnC,UAAsD,EACtD,WAAmB;QAFnB,IAAA,WAAA,KAAA,GAAA;YAAA,SAAA,CAAA,CAAmC;QAAA;QADrC,IAAA,QAKE,OAAA,IAAA,CAAA,IAAA,EAAM,MAAM,CAAC,IAAA,IAAA,CAgBd;QA9BO,MAAA,OAAO,GAAY,KAAK,CAAC;QAe/B,KAAI,CAAC,WAAW,GAAG,UAAU,CAAC;QAC9B,KAAI,CAAC,YAAY,GAAG,WAAW,CAAC;QAChC,KAAI,CAAC,OAAO,GACV,CAAC,CAAC,MAAM,CAAC,OAAO,IAAI,OAAO,SAAS,CAAC,UAAU,KAAK,UAAU,CAAC;QACjE,IAAI,KAAI,CAAC,OAAO,EAAE;YAChB,KAAI,CAAC,QAAQ,GAAG,MAAM,CAAC,MAAM,CAC3B,CAAA,CAAE,6LACF,eAAA,AAAY,EAAC,MAAM,CAAC,OAAO,CAAC,wRAC5B,eAAY,CAAC,uBAAuB,EAClC,4QAAA,AAAM,EAAE,EAAC,0BAA0B,CACpC,CACF,CAAC;SACH,MAAM;YACL,KAAI,CAAC,QAAQ,GAAG,CAAA,CAAE,CAAC;SACpB;;IACH,CAAC;IAED,wBAAA,SAAA,CAAA,MAAM,GAAN,YAAgB,CAAC;IAEjB,wBAAA,SAAA,CAAA,UAAU,GAAV,YAAoB,CAAC;IAErB,wBAAA,SAAA,CAAA,IAAI,GAAJ,SACE,KAAmB,EACnB,SAAqB,EACrB,OAAqD;QAHvD,IAAA,QAAA,IAAA,CAyCC;;QApCC,IAAI,IAAI,CAAC,aAAa,CAAC,QAAQ,EAAE;qLAC/B,OAAI,CAAC,KAAK,CAAC,+CAA+C,CAAC,CAAC;YAC5D,OAAO;SACR;QACD,IAAM,IAAI,GAAG,CAAA,KAAA,IAAI,CAAC,WAAW,CAAC,gBAAgB,CAAC,KAAK,CAAC,MAAA,QAAA,OAAA,KAAA,IAAA,KAAI,IAAI,UAAU,EAAE,CAAC;QAE1E,IAAM,OAAO,GAAG,IAAI,OAAO,CAAO,SAAC,OAAO,EAAE,MAAM;YAChD,IAAI,KAAI,CAAC,OAAO,EAAE;kOAChB,cAAA,AAAW,EACT,IAAI,EACJ,KAAI,CAAC,GAAG,EAAA,SAAA,SAAA,CAAA,GAEH,KAAI,CAAC,QAAQ,GAAA;oBAChB,cAAc,EAAE,KAAI,CAAC,YAAY;gBAAA,IAEnC,KAAI,CAAC,aAAa,EAClB,OAAO,EACP,MAAM,CACP,CAAC;aACH,MAAM;kOACL,iBAAA,AAAc,EACZ,IAAI,EACJ,KAAI,CAAC,GAAG,EACR;oBAAE,IAAI,EAAE,KAAI,CAAC,YAAY;gBAAA,CAAE,EAC3B,OAAO,EACP,MAAM,CACP,CAAC;aACH;QACH,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;QAE5B,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QACpC,IAAM,UAAU,GAAG;YACjB,IAAM,KAAK,GAAG,KAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;YACrD,KAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;QACzC,CAAC,CAAC;QACF,OAAO,CAAC,IAAI,CAAC,UAAU,EAAE,UAAU,CAAC,CAAC;IACvC,CAAC;IACH,OAAA,uBAAC;AAAD,CAAC,AAnFD,oMAGU,mBAAgB,GAgFzB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4749, "column": 0}, "map": {"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/node_modules/%40opentelemetry/otlp-exporter-base/src/index.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nexport * from './platform';\nexport { OTLPExporterBase } from './OTLPExporterBase';\nexport {\n  OTLPExporterError,\n  OTLPExporterConfigBase,\n  ExportServiceError,\n} from './types';\nexport {\n  parseHeaders,\n  appendResourcePathToUrl,\n  appendRootPathToUrlIfNeeded,\n  configureExporterTimeout,\n  invalidTimeout,\n} from './util';\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4783, "column": 0}, "map": {"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/node_modules/%40opentelemetry/otlp-exporter-base/src/platform/browser/index.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nexport { OTLPExporterBrowserBase } from './OTLPExporterBrowserBase';\nexport { sendWithXhr } from './util';\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4881, "column": 0}, "map": {"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/node_modules/%40opentelemetry/exporter-trace-otlp-http/src/index.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nexport * from './platform';\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4912, "column": 0}, "map": {"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/node_modules/%40opentelemetry/exporter-trace-otlp-http/src/platform/browser/index.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nexport * from './OTLPTraceExporter';\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4943, "column": 0}, "map": {"version": 3, "file": "OTLPTraceExporter.js", "sourceRoot": "", "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/node_modules/%40opentelemetry/exporter-trace-otlp-http/src/platform/browser/OTLPTraceExporter.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { ReadableSpan, SpanExporter } from '@opentelemetry/sdk-trace-base';\nimport { getEnv, baggageUtils } from '@opentelemetry/core';\nimport {\n  OTLPExporterConfigBase,\n  appendResourcePathToUrl,\n  appendRootPathToUrlIfNeeded,\n  OTLPExporterBrowserBase,\n} from '@opentelemetry/otlp-exporter-base';\nimport {\n  IExportTraceServiceResponse,\n  JsonTraceSerializer,\n} from '@opentelemetry/otlp-transformer';\n\nconst DEFAULT_COLLECTOR_RESOURCE_PATH = 'v1/traces';\nconst DEFAULT_COLLECTOR_URL = `http://localhost:4318/${DEFAULT_COLLECTOR_RESOURCE_PATH}`;\n\n/**\n * Collector Trace Exporter for Web\n */\nexport class OTLPTraceExporter\n  extends OTLPExporterBrowserBase<ReadableSpan, IExportTraceServiceResponse>\n  implements SpanExporter\n{\n  constructor(config: OTLPExporterConfigBase = {}) {\n    super(config, JsonTraceSerializer, 'application/json');\n    this._headers = Object.assign(\n      this._headers,\n      baggageUtils.parseKeyPairsIntoRecord(\n        getEnv().OTEL_EXPORTER_OTLP_TRACES_HEADERS\n      )\n    );\n  }\n\n  getDefaultUrl(config: OTLPExporterConfigBase): string {\n    return typeof config.url === 'string'\n      ? config.url\n      : getEnv().OTEL_EXPORTER_OTLP_TRACES_ENDPOINT.length > 0\n      ? appendRootPathToUrlIfNeeded(getEnv().OTEL_EXPORTER_OTLP_TRACES_ENDPOINT)\n      : getEnv().OTEL_EXPORTER_OTLP_ENDPOINT.length > 0\n      ? appendResourcePathToUrl(\n          getEnv().OTEL_EXPORTER_OTLP_ENDPOINT,\n          DEFAULT_COLLECTOR_RESOURCE_PATH\n        )\n      : DEFAULT_COLLECTOR_URL;\n  }\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;;AAGH,OAAO,EAAE,MAAM,EAAE,YAAY,EAAE,MAAM,qBAAqB,CAAC;;AAC3D,OAAO,EAEL,uBAAuB,EACvB,2BAA2B,EAC3B,uBAAuB,GACxB,MAAM,mCAAmC,CAAC;AAC3C,OAAO,EAEL,mBAAmB,GACpB,MAAM,iCAAiC,CAAC;;;;;;;;;;;;;;;;;;;;;;;;AAEzC,IAAM,+BAA+B,GAAG,WAAW,CAAC;AACpD,IAAM,qBAAqB,GAAG,2BAAyB,+BAAiC,CAAC;AAEzF;;GAEG,CACH,IAAA,oBAAA,SAAA,MAAA;IACU,UAAA,mBAAA,QAAkE;IAG1E,SAAA,kBAAY,MAAmC;QAAnC,IAAA,WAAA,KAAA,GAAA;YAAA,SAAA,CAAA,CAAmC;QAAA;QAA/C,IAAA,QACE,OAAA,IAAA,CAAA,IAAA,EAAM,MAAM,mMAAE,sBAAmB,EAAE,kBAAkB,CAAC,IAAA,IAAA,CAOvD;QANC,KAAI,CAAC,QAAQ,GAAG,MAAM,CAAC,MAAM,CAC3B,KAAI,CAAC,QAAQ,iSACb,eAAY,CAAC,uBAAuB,8QAClC,SAAA,AAAM,EAAE,EAAC,iCAAiC,CAC3C,CACF,CAAC;;IACJ,CAAC;IAED,kBAAA,SAAA,CAAA,aAAa,GAAb,SAAc,MAA8B;QAC1C,OAAO,OAAO,MAAM,CAAC,GAAG,KAAK,QAAQ,GACjC,MAAM,CAAC,GAAG,gRACV,SAAA,AAAM,EAAE,EAAC,kCAAkC,CAAC,MAAM,GAAG,CAAC,8LACtD,8BAAA,AAA2B,+QAAC,SAAA,AAAM,EAAE,EAAC,kCAAkC,CAAC,gRACxE,SAAA,AAAM,EAAE,EAAC,2BAA2B,CAAC,MAAM,GAAG,CAAC,8LAC/C,0BAAA,AAAuB,+QACrB,SAAA,AAAM,EAAE,EAAC,2BAA2B,EACpC,+BAA+B,CAChC,GACD,qBAAqB,CAAC;IAC5B,CAAC;IACH,OAAA,iBAAC;AAAD,CAAC,AA1BD,kOACU,0BAAuB,GAyBhC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5058, "column": 0}, "map": {"version": 3, "file": "create-service-client-constructor.js", "sourceRoot": "", "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/node_modules/%40opentelemetry/otlp-grpc-exporter-base/src/create-service-client-constructor.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport * as grpc from '@grpc/grpc-js';\n\n/**\n * Creates a unary service client constructor that, when instantiated, does not serialize/deserialize anything.\n * Allows for passing in {@link Buffer} directly, serialization can be handled via protobufjs or custom implementations.\n *\n * @param path service path\n * @param name service name\n */\nexport function createServiceClientConstructor(\n  path: string,\n  name: string\n): grpc.ServiceClientConstructor {\n  const serviceDefinition = {\n    export: {\n      path: path,\n      requestStream: false,\n      responseStream: false,\n      requestSerialize: (arg: Buffer) => {\n        return arg;\n      },\n      requestDeserialize: (arg: Buffer) => {\n        return arg;\n      },\n      responseSerialize: (arg: Buffer) => {\n        return arg;\n      },\n      responseDeserialize: (arg: Buffer) => {\n        return arg;\n      },\n    },\n  };\n\n  return grpc.makeGenericClientConstructor(serviceDefinition, name);\n}\n"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG;;;;AAEH,MAAA,gCAAsC;AAEtC;;;;;;GAMG,CACH,SAAgB,8BAA8B,CAC5C,IAAY,EACZ,IAAY;IAEZ,MAAM,iBAAiB,GAAG;QACxB,MAAM,EAAE;YACN,IAAI,EAAE,IAAI;YACV,aAAa,EAAE,KAAK;YACpB,cAAc,EAAE,KAAK;YACrB,gBAAgB,EAAE,CAAC,GAAW,EAAE,EAAE;gBAChC,OAAO,GAAG,CAAC;YACb,CAAC;YACD,kBAAkB,EAAE,CAAC,GAAW,EAAE,EAAE;gBAClC,OAAO,GAAG,CAAC;YACb,CAAC;YACD,iBAAiB,EAAE,CAAC,GAAW,EAAE,EAAE;gBACjC,OAAO,GAAG,CAAC;YACb,CAAC;YACD,mBAAmB,EAAE,CAAC,GAAW,EAAE,EAAE;gBACnC,OAAO,GAAG,CAAC;YACb,CAAC;SACF;KACF,CAAC;IAEF,OAAO,IAAI,CAAC,4BAA4B,CAAC,iBAAiB,EAAE,IAAI,CAAC,CAAC;AACpE,CAAC;AAzBD,QAAA,8BAAA,GAAA,+BAyBC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5112, "column": 0}, "map": {"version": 3, "file": "grpc-exporter-transport.js", "sourceRoot": "", "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/node_modules/%40opentelemetry/otlp-grpc-exporter-base/src/grpc-exporter-transport.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n// NOTE: do not change these type imports to actual imports. Doing so WILL break `@opentelemetry/instrumentation-http`,\n// as they'd be imported before the http/https modules can be wrapped.\nimport type {\n  Metadata,\n  ServiceError,\n  ChannelCredentials,\n  Client,\n} from '@grpc/grpc-js';\nimport { ExportResponse } from './export-response';\nimport { IExporterTransport } from './exporter-transport';\n\n// values taken from '@grpc/grpc-js` so that we don't need to require/import it.\nconst GRPC_COMPRESSION_NONE = 0;\nconst GRPC_COMPRESSION_GZIP = 2;\n\nfunction toGrpcCompression(compression: 'gzip' | 'none'): number {\n  return compression === 'gzip' ? GRPC_COMPRESSION_GZIP : GRPC_COMPRESSION_NONE;\n}\n\nexport function createInsecureCredentials(): ChannelCredentials {\n  // Lazy-load so that we don't need to require/import '@grpc/grpc-js' before it can be wrapped by instrumentation.\n  const {\n    credentials,\n    // eslint-disable-next-line @typescript-eslint/no-var-requires\n  } = require('@grpc/grpc-js');\n  return credentials.createInsecure();\n}\n\nexport function createSslCredentials(\n  rootCert?: Buffer,\n  privateKey?: Buffer,\n  certChain?: Buffer\n): ChannelCredentials {\n  // Lazy-load so that we don't need to require/import '@grpc/grpc-js' before it can be wrapped by instrumentation.\n  const {\n    credentials,\n    // eslint-disable-next-line @typescript-eslint/no-var-requires\n  } = require('@grpc/grpc-js');\n  return credentials.createSsl(rootCert, privateKey, certChain);\n}\n\nexport function createEmptyMetadata(): Metadata {\n  // Lazy-load so that we don't need to require/import '@grpc/grpc-js' before it can be wrapped by instrumentation.\n  const {\n    Metadata,\n    // eslint-disable-next-line @typescript-eslint/no-var-requires\n  } = require('@grpc/grpc-js');\n  return new Metadata();\n}\n\nexport interface GrpcExporterTransportParameters {\n  grpcPath: string;\n  grpcName: string;\n  address: string;\n  /**\n   * NOTE: Ensure that you're only importing/requiring gRPC inside the function providing the channel credentials,\n   * otherwise, gRPC and http/https instrumentations may break.\n   *\n   * For common cases, you can avoid to import/require gRPC your function by using\n   *   - {@link createSslCredentials}\n   *   - {@link createInsecureCredentials}\n   */\n  credentials: () => ChannelCredentials;\n  /**\n   * NOTE: Ensure that you're only importing/requiring gRPC inside the function providing the metadata,\n   * otherwise, gRPC and http/https instrumentations may break.\n   *\n   * To avoid having to import/require gRPC from your function to create a new Metadata object,\n   * use {@link createEmptyMetadata}\n   */\n  metadata: () => Metadata;\n  compression: 'gzip' | 'none';\n  timeoutMillis: number;\n}\n\nexport class GrpcExporterTransport implements IExporterTransport {\n  private _client?: Client;\n  private _metadata?: Metadata;\n\n  constructor(private _parameters: GrpcExporterTransportParameters) {}\n\n  shutdown() {\n    this._client?.close();\n  }\n\n  send(data: Uint8Array): Promise<ExportResponse> {\n    // We need to make a for gRPC\n    const buffer = Buffer.from(data);\n\n    if (this._client == null) {\n      // Lazy require to ensure that grpc is not loaded before instrumentations can wrap it\n      const {\n        createServiceClientConstructor,\n        // eslint-disable-next-line @typescript-eslint/no-var-requires\n      } = require('./create-service-client-constructor');\n\n      try {\n        this._metadata = this._parameters.metadata();\n      } catch (error) {\n        return Promise.resolve({\n          status: 'failure',\n          error: error,\n        });\n      }\n\n      const clientConstructor = createServiceClientConstructor(\n        this._parameters.grpcPath,\n        this._parameters.grpcName\n      );\n\n      try {\n        this._client = new clientConstructor(\n          this._parameters.address,\n          this._parameters.credentials(),\n          {\n            'grpc.default_compression_algorithm': toGrpcCompression(\n              this._parameters.compression\n            ),\n          }\n        );\n      } catch (error) {\n        return Promise.resolve({\n          status: 'failure',\n          error: error,\n        });\n      }\n    }\n\n    return new Promise<ExportResponse>(resolve => {\n      // this will always be defined\n      // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n      const deadline = Date.now() + this._parameters.timeoutMillis;\n\n      // this should never happen\n      if (this._metadata == null) {\n        return resolve({\n          error: new Error('metadata was null'),\n          status: 'failure',\n        });\n      }\n\n      // eslint-disable-next-line @typescript-eslint/ban-ts-comment\n      // @ts-ignore The gRPC client constructor is created on runtime, so we don't have any types for the resulting client.\n      this._client.export(\n        buffer,\n        this._metadata,\n        { deadline: deadline },\n        (err: ServiceError, response: Buffer) => {\n          if (err) {\n            resolve({\n              status: 'failure',\n              error: err,\n            });\n          } else {\n            resolve({\n              data: response,\n              status: 'success',\n            });\n          }\n        }\n      );\n    });\n  }\n}\n"], "names": [], "mappings": "AAuGmB;;AAvGnB;;;;;;;;;;;;;;GAcG;;;;AAaH,gFAAgF;AAChF,MAAM,qBAAqB,GAAG,CAAC,CAAC;AAChC,MAAM,qBAAqB,GAAG,CAAC,CAAC;AAEhC,SAAS,iBAAiB,CAAC,WAA4B;IACrD,OAAO,WAAW,KAAK,MAAM,CAAC,CAAC,CAAC,qBAAqB,CAAC,CAAC,CAAC,qBAAqB,CAAC;AAChF,CAAC;AAED,SAAgB,yBAAyB;IACvC,iHAAiH;IACjH,MAAM,EACJ,WAAW,EAEZ,GAAG,OAAO,CAAC,eAAe,CAAC,CAAC;IAC7B,OAAO,WAAW,CAAC,cAAc,EAAE,CAAC;AACtC,CAAC;AAPD,QAAA,yBAAA,GAAA,0BAOC;AAED,SAAgB,oBAAoB,CAClC,QAAiB,EACjB,UAAmB,EACnB,SAAkB;IAElB,iHAAiH;IACjH,MAAM,EACJ,WAAW,EAEZ,GAAG,OAAO,CAAC,eAAe,CAAC,CAAC;IAC7B,OAAO,WAAW,CAAC,SAAS,CAAC,QAAQ,EAAE,UAAU,EAAE,SAAS,CAAC,CAAC;AAChE,CAAC;AAXD,QAAA,oBAAA,GAAA,qBAWC;AAED,SAAgB,mBAAmB;IACjC,iHAAiH;IACjH,MAAM,EACJ,QAAQ,EAET,GAAG,OAAO,CAAC,eAAe,CAAC,CAAC;IAC7B,OAAO,IAAI,QAAQ,EAAE,CAAC;AACxB,CAAC;AAPD,QAAA,mBAAA,GAAA,oBAOC;AA2BD,MAAa,qBAAqB;IAIhC,YAAoB,WAA4C,CAAA;QAA5C,IAAA,CAAA,WAAW,GAAX,WAAW,CAAiC;IAAG,CAAC;IAEpE,QAAQ,GAAA;;QACN,CAAA,KAAA,IAAI,CAAC,OAAO,MAAA,QAAA,OAAA,KAAA,IAAA,KAAA,IAAA,GAAE,KAAK,EAAE,CAAC;IACxB,CAAC;IAED,IAAI,CAAC,IAAgB,EAAA;QACnB,6BAA6B;QAC7B,MAAM,MAAM,2KAAS,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAEjC,IAAI,IAAI,CAAC,OAAO,IAAI,IAAI,EAAE;YACxB,qFAAqF;YACrF,MAAM,EACJ,8BAA8B,EAE/B,GAAG,OAAO,CAAC,qCAAqC,CAAC,CAAC;YAEnD,IAAI;gBACF,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,WAAW,CAAC,QAAQ,EAAE,CAAC;aAC9C,CAAC,OAAO,KAAK,EAAE;gBACd,OAAO,OAAO,CAAC,OAAO,CAAC;oBACrB,MAAM,EAAE,SAAS;oBACjB,KAAK,EAAE,KAAK;iBACb,CAAC,CAAC;aACJ;YAED,MAAM,iBAAiB,GAAG,8BAA8B,CACtD,IAAI,CAAC,WAAW,CAAC,QAAQ,EACzB,IAAI,CAAC,WAAW,CAAC,QAAQ,CAC1B,CAAC;YAEF,IAAI;gBACF,IAAI,CAAC,OAAO,GAAG,IAAI,iBAAiB,CAClC,IAAI,CAAC,WAAW,CAAC,OAAO,EACxB,IAAI,CAAC,WAAW,CAAC,WAAW,EAAE,EAC9B;oBACE,oCAAoC,EAAE,iBAAiB,CACrD,IAAI,CAAC,WAAW,CAAC,WAAW,CAC7B;iBACF,CACF,CAAC;aACH,CAAC,OAAO,KAAK,EAAE;gBACd,OAAO,OAAO,CAAC,OAAO,CAAC;oBACrB,MAAM,EAAE,SAAS;oBACjB,KAAK,EAAE,KAAK;iBACb,CAAC,CAAC;aACJ;SACF;QAED,OAAO,IAAI,OAAO,EAAiB,OAAO,CAAC,EAAE;YAC3C,8BAA8B;YAC9B,oEAAoE;YACpE,MAAM,QAAQ,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC;YAE7D,2BAA2B;YAC3B,IAAI,IAAI,CAAC,SAAS,IAAI,IAAI,EAAE;gBAC1B,OAAO,OAAO,CAAC;oBACb,KAAK,EAAE,IAAI,KAAK,CAAC,mBAAmB,CAAC;oBACrC,MAAM,EAAE,SAAS;iBAClB,CAAC,CAAC;aACJ;YAED,6DAA6D;YAC7D,qHAAqH;YACrH,IAAI,CAAC,OAAO,CAAC,MAAM,CACjB,MAAM,EACN,IAAI,CAAC,SAAS,EACd;gBAAE,QAAQ,EAAE,QAAQ;YAAA,CAAE,EACtB,CAAC,GAAiB,EAAE,QAAgB,EAAE,EAAE;gBACtC,IAAI,GAAG,EAAE;oBACP,OAAO,CAAC;wBACN,MAAM,EAAE,SAAS;wBACjB,KAAK,EAAE,GAAG;qBACX,CAAC,CAAC;iBACJ,MAAM;oBACL,OAAO,CAAC;wBACN,IAAI,EAAE,QAAQ;wBACd,MAAM,EAAE,SAAS;qBAClB,CAAC,CAAC;iBACJ;YACH,CAAC,CACF,CAAC;QACJ,CAAC,CAAC,CAAC;IACL,CAAC;CACF;AAxFD,QAAA,qBAAA,GAAA,sBAwFC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5227, "column": 0}, "map": {"version": 3, "file": "util.js", "sourceRoot": "", "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/node_modules/%40opentelemetry/otlp-grpc-exporter-base/src/util.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { diag } from '@opentelemetry/api';\nimport { getEnv } from '@opentelemetry/core';\nimport * as path from 'path';\nimport { URL } from 'url';\nimport * as fs from 'fs';\nimport { CompressionAlgorithm } from '@opentelemetry/otlp-exporter-base';\nimport {\n  createInsecureCredentials,\n  createSslCredentials,\n} from './grpc-exporter-transport';\n\n// NOTE: do not change these type imports to actual imports. Doing so WILL break `@opentelemetry/instrumentation-http`,\n// as they'd be imported before the http/https modules can be wrapped.\nimport type { ChannelCredentials } from '@grpc/grpc-js';\n\nexport const DEFAULT_COLLECTOR_URL = 'http://localhost:4317';\n\nexport function validateAndNormalizeUrl(url: string): string {\n  const hasProtocol = url.match(/^([\\w]{1,8}):\\/\\//);\n  if (!hasProtocol) {\n    url = `https://${url}`;\n  }\n  const target = new URL(url);\n  if (target.protocol === 'unix:') {\n    return url;\n  }\n  if (target.pathname && target.pathname !== '/') {\n    diag.warn(\n      'URL path should not be set when using grpc, the path part of the URL will be ignored.'\n    );\n  }\n  if (target.protocol !== '' && !target.protocol?.match(/^(http)s?:$/)) {\n    diag.warn('URL protocol should be http(s)://. Using http://.');\n  }\n  return target.host;\n}\n\nexport function configureCredentials(\n  credentials: ChannelCredentials | undefined,\n  endpoint: string\n): ChannelCredentials {\n  let insecure: boolean;\n\n  if (credentials) {\n    return credentials;\n  } else if (endpoint.startsWith('https://')) {\n    insecure = false;\n  } else if (\n    endpoint.startsWith('http://') ||\n    endpoint === DEFAULT_COLLECTOR_URL\n  ) {\n    insecure = true;\n  } else {\n    insecure = getSecurityFromEnv();\n  }\n\n  if (insecure) {\n    return createInsecureCredentials();\n  } else {\n    return getCredentialsFromEnvironment();\n  }\n}\n\nfunction getSecurityFromEnv(): boolean {\n  const definedInsecure =\n    getEnv().OTEL_EXPORTER_OTLP_TRACES_INSECURE ||\n    getEnv().OTEL_EXPORTER_OTLP_INSECURE;\n\n  if (definedInsecure) {\n    return definedInsecure.toLowerCase() === 'true';\n  } else {\n    return false;\n  }\n}\n\n/**\n * Exported for testing\n */\nexport function getCredentialsFromEnvironment(): ChannelCredentials {\n  const rootCert = retrieveRootCert();\n  const privateKey = retrievePrivateKey();\n  const certChain = retrieveCertChain();\n\n  return createSslCredentials(rootCert, privateKey, certChain);\n}\n\nfunction retrieveRootCert(): Buffer | undefined {\n  const rootCertificate =\n    getEnv().OTEL_EXPORTER_OTLP_TRACES_CERTIFICATE ||\n    getEnv().OTEL_EXPORTER_OTLP_CERTIFICATE;\n\n  if (rootCertificate) {\n    try {\n      return fs.readFileSync(path.resolve(process.cwd(), rootCertificate));\n    } catch {\n      diag.warn('Failed to read root certificate file');\n      return undefined;\n    }\n  } else {\n    return undefined;\n  }\n}\n\nfunction retrievePrivateKey(): Buffer | undefined {\n  const clientKey =\n    getEnv().OTEL_EXPORTER_OTLP_TRACES_CLIENT_KEY ||\n    getEnv().OTEL_EXPORTER_OTLP_CLIENT_KEY;\n\n  if (clientKey) {\n    try {\n      return fs.readFileSync(path.resolve(process.cwd(), clientKey));\n    } catch {\n      diag.warn('Failed to read client certificate private key file');\n      return undefined;\n    }\n  } else {\n    return undefined;\n  }\n}\n\nfunction retrieveCertChain(): Buffer | undefined {\n  const clientChain =\n    getEnv().OTEL_EXPORTER_OTLP_TRACES_CLIENT_CERTIFICATE ||\n    getEnv().OTEL_EXPORTER_OTLP_CLIENT_CERTIFICATE;\n\n  if (clientChain) {\n    try {\n      return fs.readFileSync(path.resolve(process.cwd(), clientChain));\n    } catch {\n      diag.warn('Failed to read client certificate chain file');\n      return undefined;\n    }\n  } else {\n    return undefined;\n  }\n}\n\nexport function configureCompression(\n  compression: CompressionAlgorithm | undefined\n): CompressionAlgorithm {\n  if (compression != null) {\n    return compression;\n  }\n\n  const envCompression =\n    getEnv().OTEL_EXPORTER_OTLP_TRACES_COMPRESSION ||\n    getEnv().OTEL_EXPORTER_OTLP_COMPRESSION;\n\n  if (envCompression === 'gzip') {\n    return CompressionAlgorithm.GZIP;\n  } else if (envCompression === 'none') {\n    return CompressionAlgorithm.NONE;\n  }\n\n  diag.warn(\n    'Unknown compression \"' + envCompression + '\", falling back to \"none\"'\n  );\n  return CompressionAlgorithm.NONE;\n}\n"], "names": [], "mappings": "AA6G0C;;AA7G1C;;;;;;;;;;;;;;GAcG;;;;AAEH,MAAA,sCAA0C;AAC1C,MAAA,wCAA6C;AAC7C,MAAA,uBAA6B;AAC7B,MAAA,uBAA0B;AAC1B,MAAA,mBAAyB;;;;;AACzB,MAAA,oEAAyE;AACzE,MAAA,iEAGmC;AAMtB,QAAA,qBAAqB,GAAG,uBAAuB,CAAC;AAE7D,SAAgB,uBAAuB,CAAC,GAAW;;IACjD,MAAM,WAAW,GAAG,GAAG,CAAC,KAAK,CAAC,mBAAmB,CAAC,CAAC;IACnD,IAAI,CAAC,WAAW,EAAE;QAChB,GAAG,GAAG,CAAA,QAAA,EAAW,GAAG,EAAE,CAAC;KACxB;IACD,MAAM,MAAM,GAAG,IAAI,MAAA,GAAG,CAAC,GAAG,CAAC,CAAC;IAC5B,IAAI,MAAM,CAAC,QAAQ,KAAK,OAAO,EAAE;QAC/B,OAAO,GAAG,CAAC;KACZ;IACD,IAAI,MAAM,CAAC,QAAQ,IAAI,MAAM,CAAC,QAAQ,KAAK,GAAG,EAAE;QAC9C,MAAA,IAAI,CAAC,IAAI,CACP,uFAAuF,CACxF,CAAC;KACH;IACD,IAAI,MAAM,CAAC,QAAQ,KAAK,EAAE,IAAI,CAAC,CAAA,CAAA,KAAA,MAAM,CAAC,QAAQ,MAAA,QAAA,OAAA,KAAA,IAAA,KAAA,IAAA,GAAE,KAAK,CAAC,aAAa,CAAC,CAAA,EAAE;QACpE,MAAA,IAAI,CAAC,IAAI,CAAC,mDAAmD,CAAC,CAAC;KAChE;IACD,OAAO,MAAM,CAAC,IAAI,CAAC;AACrB,CAAC;AAlBD,QAAA,uBAAA,GAAA,wBAkBC;AAED,SAAgB,oBAAoB,CAClC,WAA2C,EAC3C,QAAgB;IAEhB,IAAI,QAAiB,CAAC;IAEtB,IAAI,WAAW,EAAE;QACf,OAAO,WAAW,CAAC;KACpB,MAAM,IAAI,QAAQ,CAAC,UAAU,CAAC,UAAU,CAAC,EAAE;QAC1C,QAAQ,GAAG,KAAK,CAAC;KAClB,MAAM,IACL,QAAQ,CAAC,UAAU,CAAC,SAAS,CAAC,IAC9B,QAAQ,KAAK,QAAA,qBAAqB,EAClC;QACA,QAAQ,GAAG,IAAI,CAAC;KACjB,MAAM;QACL,QAAQ,GAAG,kBAAkB,EAAE,CAAC;KACjC;IAED,IAAI,QAAQ,EAAE;QACZ,OAAO,CAAA,GAAA,0BAAA,yBAAyB,GAAE,CAAC;KACpC,MAAM;QACL,OAAO,6BAA6B,EAAE,CAAC;KACxC;AACH,CAAC;AAxBD,QAAA,oBAAA,GAAA,qBAwBC;AAED,SAAS,kBAAkB;IACzB,MAAM,eAAe,GACnB,CAAA,GAAA,OAAA,MAAM,GAAE,CAAC,kCAAkC,IAC3C,CAAA,GAAA,OAAA,MAAM,GAAE,CAAC,2BAA2B,CAAC;IAEvC,IAAI,eAAe,EAAE;QACnB,OAAO,eAAe,CAAC,WAAW,EAAE,KAAK,MAAM,CAAC;KACjD,MAAM;QACL,OAAO,KAAK,CAAC;KACd;AACH,CAAC;AAED;;GAEG,CACH,SAAgB,6BAA6B;IAC3C,MAAM,QAAQ,GAAG,gBAAgB,EAAE,CAAC;IACpC,MAAM,UAAU,GAAG,kBAAkB,EAAE,CAAC;IACxC,MAAM,SAAS,GAAG,iBAAiB,EAAE,CAAC;IAEtC,OAAO,CAAA,GAAA,0BAAA,oBAAoB,EAAC,QAAQ,EAAE,UAAU,EAAE,SAAS,CAAC,CAAC;AAC/D,CAAC;AAND,QAAA,6BAAA,GAAA,8BAMC;AAED,SAAS,gBAAgB;IACvB,MAAM,eAAe,GACnB,CAAA,GAAA,OAAA,MAAM,GAAE,CAAC,qCAAqC,IAC9C,CAAA,GAAA,OAAA,MAAM,GAAE,CAAC,8BAA8B,CAAC;IAE1C,IAAI,eAAe,EAAE;QACnB,IAAI;YACF,OAAO,EAAE,CAAC,YAAY,CAAC,IAAI,CAAC,OAAO,4KAAQ,CAAC,GAAG,EAAE,EAAE,eAAe,CAAC,CAAC,CAAC;SACtE,CAAC,OAAA,IAAM;YACN,MAAA,IAAI,CAAC,IAAI,CAAC,sCAAsC,CAAC,CAAC;YAClD,OAAO,SAAS,CAAC;SAClB;KACF,MAAM;QACL,OAAO,SAAS,CAAC;KAClB;AACH,CAAC;AAED,SAAS,kBAAkB;IACzB,MAAM,SAAS,GACb,CAAA,GAAA,OAAA,MAAM,GAAE,CAAC,oCAAoC,IAC7C,CAAA,GAAA,OAAA,MAAM,GAAE,CAAC,6BAA6B,CAAC;IAEzC,IAAI,SAAS,EAAE;QACb,IAAI;YACF,OAAO,EAAE,CAAC,YAAY,CAAC,IAAI,CAAC,OAAO,kKAAC,UAAO,CAAC,GAAG,EAAE,EAAE,SAAS,CAAC,CAAC,CAAC;SAChE,CAAC,OAAA,IAAM;YACN,MAAA,IAAI,CAAC,IAAI,CAAC,oDAAoD,CAAC,CAAC;YAChE,OAAO,SAAS,CAAC;SAClB;KACF,MAAM;QACL,OAAO,SAAS,CAAC;KAClB;AACH,CAAC;AAED,SAAS,iBAAiB;IACxB,MAAM,WAAW,GACf,CAAA,GAAA,OAAA,MAAM,GAAE,CAAC,4CAA4C,IACrD,CAAA,GAAA,OAAA,MAAM,GAAE,CAAC,qCAAqC,CAAC;IAEjD,IAAI,WAAW,EAAE;QACf,IAAI;YACF,OAAO,EAAE,CAAC,YAAY,CAAC,IAAI,CAAC,OAAO,kKAAC,UAAO,CAAC,GAAG,EAAE,EAAE,WAAW,CAAC,CAAC,CAAC;SAClE,CAAC,OAAA,IAAM;YACN,MAAA,IAAI,CAAC,IAAI,CAAC,8CAA8C,CAAC,CAAC;YAC1D,OAAO,SAAS,CAAC;SAClB;KACF,MAAM;QACL,OAAO,SAAS,CAAC;KAClB;AACH,CAAC;AAED,SAAgB,oBAAoB,CAClC,WAA6C;IAE7C,IAAI,WAAW,IAAI,IAAI,EAAE;QACvB,OAAO,WAAW,CAAC;KACpB;IAED,MAAM,cAAc,GAClB,CAAA,GAAA,OAAA,MAAM,GAAE,CAAC,qCAAqC,IAC9C,CAAA,GAAA,OAAA,MAAM,GAAE,CAAC,8BAA8B,CAAC;IAE1C,IAAI,cAAc,KAAK,MAAM,EAAE;QAC7B,OAAO,qBAAA,oBAAoB,CAAC,IAAI,CAAC;KAClC,MAAM,IAAI,cAAc,KAAK,MAAM,EAAE;QACpC,OAAO,qBAAA,oBAAoB,CAAC,IAAI,CAAC;KAClC;IAED,MAAA,IAAI,CAAC,IAAI,CACP,uBAAuB,GAAG,cAAc,GAAG,2BAA2B,CACvE,CAAC;IACF,OAAO,qBAAA,oBAAoB,CAAC,IAAI,CAAC;AACnC,CAAC;AArBD,QAAA,oBAAA,GAAA,qBAqBC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5371, "column": 0}, "map": {"version": 3, "file": "OTLPGRPCExporterNodeBase.js", "sourceRoot": "", "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/node_modules/%40opentelemetry/otlp-grpc-exporter-base/src/OTLPGRPCExporterNodeBase.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { diag } from '@opentelemetry/api';\nimport { GRPCQueueItem, OTLPGRPCExporterConfigNode } from './types';\nimport { baggageUtils, getEnv } from '@opentelemetry/core';\nimport {\n  CompressionAlgorithm,\n  OTLPExporterBase,\n  OTLPExporterError,\n} from '@opentelemetry/otlp-exporter-base';\nimport {\n  createEmptyMetadata,\n  GrpcExporterTransport,\n} from './grpc-exporter-transport';\nimport { configureCompression, configureCredentials } from './util';\nimport { ISerializer } from '@opentelemetry/otlp-transformer';\nimport { IExporterTransport } from './exporter-transport';\n\n/**\n * OTLP Exporter abstract base class\n */\nexport abstract class OTLPGRPCExporterNodeBase<\n  ExportItem,\n  ServiceResponse,\n> extends OTLPExporterBase<OTLPGRPCExporterConfigNode, ExportItem> {\n  grpcQueue: GRPCQueueItem<ExportItem>[] = [];\n  compression: CompressionAlgorithm;\n  private _transport: IExporterTransport;\n  private _serializer: ISerializer<ExportItem[], ServiceResponse>;\n\n  constructor(\n    config: OTLPGRPCExporterConfigNode = {},\n    signalSpecificMetadata: Record<string, string>,\n    grpcName: string,\n    grpcPath: string,\n    serializer: ISerializer<ExportItem[], ServiceResponse>\n  ) {\n    super(config);\n    this._serializer = serializer;\n    if (config.headers) {\n      diag.warn('Headers cannot be set when using grpc');\n    }\n    const nonSignalSpecificMetadata = baggageUtils.parseKeyPairsIntoRecord(\n      getEnv().OTEL_EXPORTER_OTLP_HEADERS\n    );\n    const rawMetadata = Object.assign(\n      {},\n      nonSignalSpecificMetadata,\n      signalSpecificMetadata\n    );\n\n    let credentialProvider = () => {\n      return configureCredentials(undefined, this.getUrlFromConfig(config));\n    };\n\n    if (config.credentials != null) {\n      const credentials = config.credentials;\n      credentialProvider = () => {\n        return credentials;\n      };\n    }\n\n    // Ensure we don't modify the original.\n    const configMetadata = config.metadata?.clone();\n    const metadataProvider = () => {\n      const metadata = configMetadata ?? createEmptyMetadata();\n      for (const [key, value] of Object.entries(rawMetadata)) {\n        // only override with env var data if the key has no values.\n        // not using Metadata.merge() as it will keep both values.\n        if (metadata.get(key).length < 1) {\n          metadata.set(key, value);\n        }\n      }\n\n      return metadata;\n    };\n\n    this.compression = configureCompression(config.compression);\n    this._transport = new GrpcExporterTransport({\n      address: this.getDefaultUrl(config),\n      compression: this.compression,\n      credentials: credentialProvider,\n      grpcName: grpcName,\n      grpcPath: grpcPath,\n      metadata: metadataProvider,\n      timeoutMillis: this.timeoutMillis,\n    });\n  }\n\n  onInit() {\n    // Intentionally left empty; nothing to do.\n  }\n\n  override onShutdown() {\n    this._transport.shutdown();\n  }\n\n  send(\n    objects: ExportItem[],\n    onSuccess: () => void,\n    onError: (error: OTLPExporterError) => void\n  ): void {\n    if (this._shutdownOnce.isCalled) {\n      diag.debug('Shutdown already started. Cannot send objects');\n      return;\n    }\n\n    const data = this._serializer.serializeRequest(objects);\n\n    if (data == null) {\n      onError(new Error('Could not serialize message'));\n      return;\n    }\n\n    const promise = this._transport.send(data).then(response => {\n      if (response.status === 'success') {\n        onSuccess();\n        return;\n      }\n      if (response.status === 'failure' && response.error) {\n        onError(response.error);\n      }\n      onError(new OTLPExporterError('Export failed with unknown error'));\n    }, onError);\n\n    this._sendingPromises.push(promise);\n    const popPromise = () => {\n      const index = this._sendingPromises.indexOf(promise);\n      this._sendingPromises.splice(index, 1);\n    };\n    promise.then(popPromise, popPromise);\n  }\n\n  abstract getUrlFromConfig(config: OTLPGRPCExporterConfigNode): string;\n}\n"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG;;;;AAEH,MAAA,sCAA0C;AAE1C,MAAA,wCAA2D;AAC3D,MAAA,oEAI2C;AAC3C,MAAA,iEAGmC;AACnC,MAAA,2BAAoE;AAIpE;;GAEG,CACH,MAAsB,wBAGpB,SAAQ,qBAAA,gBAAwD;IAMhE,YACE,SAAqC,CAAA,CAAE,EACvC,sBAA8C,EAC9C,QAAgB,EAChB,QAAgB,EAChB,UAAsD,CAAA;;QAEtD,KAAK,CAAC,MAAM,CAAC,CAAC;QAZhB,IAAA,CAAA,SAAS,GAAgC,EAAE,CAAC;QAa1C,IAAI,CAAC,WAAW,GAAG,UAAU,CAAC;QAC9B,IAAI,MAAM,CAAC,OAAO,EAAE;YAClB,MAAA,IAAI,CAAC,IAAI,CAAC,uCAAuC,CAAC,CAAC;SACpD;QACD,MAAM,yBAAyB,GAAG,OAAA,YAAY,CAAC,uBAAuB,CACpE,CAAA,GAAA,OAAA,MAAM,GAAE,CAAC,0BAA0B,CACpC,CAAC;QACF,MAAM,WAAW,GAAG,MAAM,CAAC,MAAM,CAC/B,CAAA,CAAE,EACF,yBAAyB,EACzB,sBAAsB,CACvB,CAAC;QAEF,IAAI,kBAAkB,GAAG,GAAG,EAAE;YAC5B,OAAO,CAAA,GAAA,OAAA,oBAAoB,EAAC,SAAS,EAAE,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,CAAC,CAAC;QACxE,CAAC,CAAC;QAEF,IAAI,MAAM,CAAC,WAAW,IAAI,IAAI,EAAE;YAC9B,MAAM,WAAW,GAAG,MAAM,CAAC,WAAW,CAAC;YACvC,kBAAkB,GAAG,GAAG,EAAE;gBACxB,OAAO,WAAW,CAAC;YACrB,CAAC,CAAC;SACH;QAED,uCAAuC;QACvC,MAAM,cAAc,GAAG,CAAA,KAAA,MAAM,CAAC,QAAQ,MAAA,QAAA,OAAA,KAAA,IAAA,KAAA,IAAA,GAAE,KAAK,EAAE,CAAC;QAChD,MAAM,gBAAgB,GAAG,GAAG,EAAE;YAC5B,MAAM,QAAQ,GAAG,cAAc,KAAA,QAAd,cAAc,KAAA,KAAA,IAAd,cAAc,GAAI,CAAA,GAAA,0BAAA,mBAAmB,GAAE,CAAC;YACzD,KAAK,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,WAAW,CAAC,CAAE;gBACtD,4DAA4D;gBAC5D,0DAA0D;gBAC1D,IAAI,QAAQ,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,MAAM,GAAG,CAAC,EAAE;oBAChC,QAAQ,CAAC,GAAG,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;iBAC1B;aACF;YAED,OAAO,QAAQ,CAAC;QAClB,CAAC,CAAC;QAEF,IAAI,CAAC,WAAW,GAAG,CAAA,GAAA,OAAA,oBAAoB,EAAC,MAAM,CAAC,WAAW,CAAC,CAAC;QAC5D,IAAI,CAAC,UAAU,GAAG,IAAI,0BAAA,qBAAqB,CAAC;YAC1C,OAAO,EAAE,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC;YACnC,WAAW,EAAE,IAAI,CAAC,WAAW;YAC7B,WAAW,EAAE,kBAAkB;YAC/B,QAAQ,EAAE,QAAQ;YAClB,QAAQ,EAAE,QAAQ;YAClB,QAAQ,EAAE,gBAAgB;YAC1B,aAAa,EAAE,IAAI,CAAC,aAAa;SAClC,CAAC,CAAC;IACL,CAAC;IAED,MAAM,GAAA;IACJ,2CAA2C;IAC7C,CAAC;IAEQ,UAAU,GAAA;QACjB,IAAI,CAAC,UAAU,CAAC,QAAQ,EAAE,CAAC;IAC7B,CAAC;IAED,IAAI,CACF,OAAqB,EACrB,SAAqB,EACrB,OAA2C,EAAA;QAE3C,IAAI,IAAI,CAAC,aAAa,CAAC,QAAQ,EAAE;YAC/B,MAAA,IAAI,CAAC,KAAK,CAAC,+CAA+C,CAAC,CAAC;YAC5D,OAAO;SACR;QAED,MAAM,IAAI,GAAG,IAAI,CAAC,WAAW,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAC;QAExD,IAAI,IAAI,IAAI,IAAI,EAAE;YAChB,OAAO,CAAC,IAAI,KAAK,CAAC,6BAA6B,CAAC,CAAC,CAAC;YAClD,OAAO;SACR;QAED,MAAM,OAAO,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,IAAI,EAAC,QAAQ,CAAC,EAAE;YACzD,IAAI,QAAQ,CAAC,MAAM,KAAK,SAAS,EAAE;gBACjC,SAAS,EAAE,CAAC;gBACZ,OAAO;aACR;YACD,IAAI,QAAQ,CAAC,MAAM,KAAK,SAAS,IAAI,QAAQ,CAAC,KAAK,EAAE;gBACnD,OAAO,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;aACzB;YACD,OAAO,CAAC,IAAI,qBAAA,iBAAiB,CAAC,kCAAkC,CAAC,CAAC,CAAC;QACrE,CAAC,EAAE,OAAO,CAAC,CAAC;QAEZ,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QACpC,MAAM,UAAU,GAAG,GAAG,EAAE;YACtB,MAAM,KAAK,GAAG,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;YACrD,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;QACzC,CAAC,CAAC;QACF,OAAO,CAAC,IAAI,CAAC,UAAU,EAAE,UAAU,CAAC,CAAC;IACvC,CAAC;CAGF;AAjHD,QAAA,wBAAA,GAAA,yBAiHC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5481, "column": 0}, "map": {"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/node_modules/%40opentelemetry/otlp-grpc-exporter-base/src/index.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nexport { OTLPGRPCExporterNodeBase } from './OTLPGRPCExporterNodeBase';\nexport { OTLPGRPCExporterConfigNode } from './types';\nexport { DEFAULT_COLLECTOR_URL, validateAndNormalizeUrl } from './util';\n"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG;;;;AAEH,IAAA,mEAAsE;AAA7D,OAAA,cAAA,CAAA,SAAA,4BAAA;IAAA,YAAA;IAAA,KAAA;QAAA,OAAA,2BAAA,wBAAwB;IAAA;AAAA,GAAA;AAEjC,IAAA,2BAAwE;AAA/D,OAAA,cAAA,CAAA,SAAA,yBAAA;IAAA,YAAA;IAAA,KAAA;QAAA,OAAA,OAAA,qBAAqB;IAAA;AAAA,GAAA;AAAE,OAAA,cAAA,CAAA,SAAA,2BAAA;IAAA,YAAA;IAAA,KAAA;QAAA,OAAA,OAAA,uBAAuB;IAAA;AAAA,GAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5525, "column": 0}, "map": {"version": 3, "file": "version.js", "sourceRoot": "", "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/node_modules/%40opentelemetry/exporter-trace-otlp-grpc/src/version.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n// this is autogenerated file, see scripts/version-update.js\nexport const VERSION = '0.52.1';\n"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG;;;;AAEH,4DAA4D;AAC/C,QAAA,OAAO,GAAG,QAAQ,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5551, "column": 0}, "map": {"version": 3, "file": "OTLPTraceExporter.js", "sourceRoot": "", "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/node_modules/%40opentelemetry/exporter-trace-otlp-grpc/src/OTLPTraceExporter.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { ReadableSpan, SpanExporter } from '@opentelemetry/sdk-trace-base';\nimport { baggageUtils, getEnv } from '@opentelemetry/core';\nimport {\n  OTLPGRPCExporterConfigNode,\n  OTLPGRPCExporterNodeBase,\n  validateAndNormalizeUrl,\n  DEFAULT_COLLECTOR_URL,\n} from '@opentelemetry/otlp-grpc-exporter-base';\nimport {\n  IExportTraceServiceResponse,\n  ProtobufTraceSerializer,\n} from '@opentelemetry/otlp-transformer';\nimport { VERSION } from './version';\n\nconst USER_AGENT = {\n  'User-Agent': `OTel-OTLP-Exporter-JavaScript/${VERSION}`,\n};\n\n/**\n * OTLP Trace Exporter for Node\n */\nexport class OTLPTraceExporter\n  extends OTLPGRPCExporterNodeBase<ReadableSpan, IExportTraceServiceResponse>\n  implements SpanExporter\n{\n  constructor(config: OTLPGRPCExporterConfigNode = {}) {\n    const signalSpecificMetadata = {\n      ...USER_AGENT,\n      ...baggageUtils.parseKeyPairsIntoRecord(\n        getEnv().OTEL_EXPORTER_OTLP_TRACES_HEADERS\n      ),\n    };\n    super(\n      config,\n      signalSpecificMetadata,\n      'TraceExportService',\n      '/opentelemetry.proto.collector.trace.v1.TraceService/Export',\n      ProtobufTraceSerializer\n    );\n  }\n\n  getDefaultUrl(config: OTLPGRPCExporterConfigNode) {\n    return validateAndNormalizeUrl(this.getUrlFromConfig(config));\n  }\n\n  getUrlFromConfig(config: OTLPGRPCExporterConfigNode): string {\n    if (typeof config.url === 'string') {\n      return config.url;\n    }\n\n    return (\n      getEnv().OTEL_EXPORTER_OTLP_TRACES_ENDPOINT ||\n      getEnv().OTEL_EXPORTER_OTLP_ENDPOINT ||\n      DEFAULT_COLLECTOR_URL\n    );\n  }\n}\n"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG;;;;AAGH,MAAA,wCAA2D;AAC3D,MAAA,8EAKgD;AAChD,MAAA,gEAGyC;AACzC,MAAA,iCAAoC;AAEpC,MAAM,UAAU,GAAG;IACjB,YAAY,EAAE,CAAA,8BAAA,EAAiC,UAAA,OAAO,EAAE;CACzD,CAAC;AAEF;;GAEG,CACH,MAAa,iBACX,SAAQ,0BAAA,wBAAmE;IAG3E,YAAY,SAAqC,CAAA,CAAE,CAAA;QACjD,MAAM,sBAAsB,GAAA,OAAA,MAAA,CAAA,OAAA,MAAA,CAAA,CAAA,GACvB,UAAU,GACV,OAAA,YAAY,CAAC,uBAAuB,CACrC,CAAA,GAAA,OAAA,MAAM,GAAE,CAAC,iCAAiC,CAC3C,CACF,CAAC;QACF,KAAK,CACH,MAAM,EACN,sBAAsB,EACtB,oBAAoB,EACpB,6DAA6D,EAC7D,mBAAA,uBAAuB,CACxB,CAAC;IACJ,CAAC;IAED,aAAa,CAAC,MAAkC,EAAA;QAC9C,OAAO,CAAA,GAAA,0BAAA,uBAAuB,EAAC,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,CAAC,CAAC;IAChE,CAAC;IAED,gBAAgB,CAAC,MAAkC,EAAA;QACjD,IAAI,OAAO,MAAM,CAAC,GAAG,KAAK,QAAQ,EAAE;YAClC,OAAO,MAAM,CAAC,GAAG,CAAC;SACnB;QAED,OAAO,AACL,CAAA,GAAA,OAAA,MAAM,GAAE,CAAC,kCAAkC,IAC3C,CAAA,GAAA,OAAA,MAAM,GAAE,CAAC,2BAA2B,IACpC,0BAAA,qBAAqB,CACtB,CAAC;IACJ,CAAC;CACF;AAnCD,QAAA,iBAAA,GAAA,kBAmCC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5600, "column": 0}, "map": {"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/node_modules/%40opentelemetry/exporter-trace-otlp-grpc/src/index.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nexport * from './OTLPTraceExporter';\n"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG;;;;;;;;;;;;;;;;;;AAEH,iKAAA,SAAoC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5640, "column": 0}, "map": {"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/node_modules/%40opentelemetry/exporter-zipkin/src/index.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nexport * from './platform';\nexport { ExporterConfig } from './types';\nexport * from './zipkin';\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5672, "column": 0}, "map": {"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/node_modules/%40opentelemetry/exporter-zipkin/src/platform/browser/index.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nexport * from './util';\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5703, "column": 0}, "map": {"version": 3, "file": "util.js", "sourceRoot": "", "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/node_modules/%40opentelemetry/exporter-zipkin/src/platform/browser/util.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { diag } from '@opentelemetry/api';\nimport {\n  ExportResult,\n  ExportResultCode,\n  globalErrorHandler,\n} from '@opentelemetry/core';\nimport * as zipkinTypes from '../../types';\n\n/**\n * Prepares send function that will send spans to the remote Zipkin service.\n * @param urlStr - url to send spans\n * @param headers - headers\n * send\n */\nexport function prepareSend(\n  urlStr: string,\n  headers?: Record<string, string>\n): zipkinTypes.SendFn {\n  let xhrHeaders: Record<string, string>;\n  const useBeacon = typeof navigator.sendBeacon === 'function' && !headers;\n  if (headers) {\n    xhrHeaders = {\n      Accept: 'application/json',\n      'Content-Type': 'application/json',\n      ...headers,\n    };\n  }\n\n  /**\n   * Send spans to the remote Zipkin service.\n   */\n  return function send(\n    zipkinSpans: zipkinTypes.Span[],\n    done: (result: ExportResult) => void\n  ) {\n    if (zipkinSpans.length === 0) {\n      diag.debug('Zipkin send with empty spans');\n      return done({ code: ExportResultCode.SUCCESS });\n    }\n    const payload = JSON.stringify(zipkinSpans);\n    if (useBeacon) {\n      sendWithBeacon(payload, done, urlStr);\n    } else {\n      sendWithXhr(payload, done, urlStr, xhrHeaders);\n    }\n  };\n}\n\n/**\n * Sends data using beacon\n * @param data\n * @param done\n * @param urlStr\n */\nfunction sendWithBeacon(\n  data: string,\n  done: (result: ExportResult) => void,\n  urlStr: string\n) {\n  if (navigator.sendBeacon(urlStr, data)) {\n    diag.debug('sendBeacon - can send', data);\n    done({ code: ExportResultCode.SUCCESS });\n  } else {\n    done({\n      code: ExportResultCode.FAILED,\n      error: new Error(`sendBeacon - cannot send ${data}`),\n    });\n  }\n}\n\n/**\n * Sends data using XMLHttpRequest\n * @param data\n * @param done\n * @param urlStr\n * @param xhrHeaders\n */\nfunction sendWithXhr(\n  data: string,\n  done: (result: ExportResult) => void,\n  urlStr: string,\n  xhrHeaders: Record<string, string> = {}\n) {\n  const xhr = new XMLHttpRequest();\n  xhr.open('POST', urlStr);\n  Object.entries(xhrHeaders).forEach(([k, v]) => {\n    xhr.setRequestHeader(k, v);\n  });\n\n  xhr.onreadystatechange = () => {\n    if (xhr.readyState === XMLHttpRequest.DONE) {\n      const statusCode = xhr.status || 0;\n      diag.debug(`Zipkin response status code: ${statusCode}, body: ${data}`);\n\n      if (xhr.status >= 200 && xhr.status < 400) {\n        return done({ code: ExportResultCode.SUCCESS });\n      } else {\n        return done({\n          code: ExportResultCode.FAILED,\n          error: new Error(\n            `Got unexpected status code from zipkin: ${xhr.status}`\n          ),\n        });\n      }\n    }\n  };\n\n  xhr.onerror = msg => {\n    globalErrorHandler(new Error(`Zipkin request error: ${msg}`));\n    return done({ code: ExportResultCode.FAILED });\n  };\n\n  // Issue request to remote service\n  diag.debug(`Zipkin request payload: ${data}`);\n  xhr.send(data);\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;AAEH,OAAO,EAAE,IAAI,EAAE,MAAM,oBAAoB,CAAC;;AAC1C,OAAO,EAEL,gBAAgB,EAChB,kBAAkB,GACnB,MAAM,qBAAqB,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASvB,SAAU,WAAW,CACzB,MAAc,EACd,OAAgC;IAEhC,IAAI,UAAkC,CAAC;IACvC,IAAM,SAAS,GAAG,OAAO,SAAS,CAAC,UAAU,KAAK,UAAU,IAAI,CAAC,OAAO,CAAC;IACzE,IAAI,OAAO,EAAE;QACX,UAAU,GAAA,SAAA;YACR,MAAM,EAAE,kBAAkB;YAC1B,cAAc,EAAE,kBAAkB;QAAA,GAC/B,OAAO,CACX,CAAC;KACH;IAED;;OAEG,CACH,OAAO,SAAS,IAAI,CAClB,WAA+B,EAC/B,IAAoC;QAEpC,IAAI,WAAW,CAAC,MAAM,KAAK,CAAC,EAAE;qLAC5B,OAAI,CAAC,KAAK,CAAC,8BAA8B,CAAC,CAAC;YAC3C,OAAO,IAAI,CAAC;gBAAE,IAAI,sOAAE,mBAAgB,CAAC,OAAO;YAAA,CAAE,CAAC,CAAC;SACjD;QACD,IAAM,OAAO,GAAG,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC;QAC5C,IAAI,SAAS,EAAE;YACb,cAAc,CAAC,OAAO,EAAE,IAAI,EAAE,MAAM,CAAC,CAAC;SACvC,MAAM;YACL,WAAW,CAAC,OAAO,EAAE,IAAI,EAAE,MAAM,EAAE,UAAU,CAAC,CAAC;SAChD;IACH,CAAC,CAAC;AACJ,CAAC;AAED;;;;;GAKG,CACH,SAAS,cAAc,CACrB,IAAY,EACZ,IAAoC,EACpC,MAAc;IAEd,IAAI,SAAS,CAAC,UAAU,CAAC,MAAM,EAAE,IAAI,CAAC,EAAE;iLACtC,OAAI,CAAC,KAAK,CAAC,uBAAuB,EAAE,IAAI,CAAC,CAAC;QAC1C,IAAI,CAAC;YAAE,IAAI,sOAAE,mBAAgB,CAAC,OAAO;QAAA,CAAE,CAAC,CAAC;KAC1C,MAAM;QACL,IAAI,CAAC;YACH,IAAI,sOAAE,mBAAgB,CAAC,MAAM;YAC7B,KAAK,EAAE,IAAI,KAAK,CAAC,8BAA4B,IAAM,CAAC;SACrD,CAAC,CAAC;KACJ;AACH,CAAC;AAED;;;;;;GAMG,CACH,SAAS,WAAW,CAClB,IAAY,EACZ,IAAoC,EACpC,MAAc,EACd,UAAuC;IAAvC,IAAA,eAAA,KAAA,GAAA;QAAA,aAAA,CAAA,CAAuC;IAAA;IAEvC,IAAM,GAAG,GAAG,IAAI,cAAc,EAAE,CAAC;IACjC,GAAG,CAAC,IAAI,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;IACzB,MAAM,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC,OAAO,CAAC,SAAC,EAAM;YAAN,KAAA,OAAA,IAAA,EAAM,EAAL,CAAC,GAAA,EAAA,CAAA,EAAA,EAAE,CAAC,GAAA,EAAA,CAAA,EAAA;QACvC,GAAG,CAAC,gBAAgB,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IAC7B,CAAC,CAAC,CAAC;IAEH,GAAG,CAAC,kBAAkB,GAAG;QACvB,IAAI,GAAG,CAAC,UAAU,KAAK,cAAc,CAAC,IAAI,EAAE;YAC1C,IAAM,UAAU,GAAG,GAAG,CAAC,MAAM,IAAI,CAAC,CAAC;oLACnC,QAAI,CAAC,KAAK,CAAC,kCAAgC,UAAU,GAAA,aAAW,IAAM,CAAC,CAAC;YAExE,IAAI,GAAG,CAAC,MAAM,IAAI,GAAG,IAAI,GAAG,CAAC,MAAM,GAAG,GAAG,EAAE;gBACzC,OAAO,IAAI,CAAC;oBAAE,IAAI,qOAAE,oBAAgB,CAAC,OAAO;gBAAA,CAAE,CAAC,CAAC;aACjD,MAAM;gBACL,OAAO,IAAI,CAAC;oBACV,IAAI,sOAAE,mBAAgB,CAAC,MAAM;oBAC7B,KAAK,EAAE,IAAI,KAAK,CACd,6CAA2C,GAAG,CAAC,MAAQ,CACxD;iBACF,CAAC,CAAC;aACJ;SACF;IACH,CAAC,CAAC;IAEF,GAAG,CAAC,OAAO,GAAG,SAAA,GAAG;wQACf,qBAAA,AAAkB,EAAC,IAAI,KAAK,CAAC,2BAAyB,GAAK,CAAC,CAAC,CAAC;QAC9D,OAAO,IAAI,CAAC;YAAE,IAAI,sOAAE,mBAAgB,CAAC,MAAM;QAAA,CAAE,CAAC,CAAC;IACjD,CAAC,CAAC;IAEF,kCAAkC;6KAClC,OAAI,CAAC,KAAK,CAAC,6BAA2B,IAAM,CAAC,CAAC;IAC9C,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AACjB,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5868, "column": 0}, "map": {"version": 3, "file": "types.js", "sourceRoot": "", "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/node_modules/%40opentelemetry/exporter-zipkin/src/types.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { ExportResult } from '@opentelemetry/core';\n\n/**\n * Exporter config\n */\nexport interface ExporterConfig {\n  headers?: Record<string, string>;\n  serviceName?: string;\n  url?: string;\n  // Optional mapping overrides for OpenTelemetry status code and description.\n  statusCodeTagName?: string;\n  statusDescriptionTagName?: string;\n  getExportRequestHeaders?: () => Record<string, string> | undefined;\n}\n\n/**\n * Zipkin Span\n * @see https://github.com/openzipkin/zipkin-api/blob/master/zipkin2-api.yaml\n */\nexport interface Span {\n  /**\n   * Trace identifier, set on all spans within it.\n   */\n  traceId: string;\n  /**\n   * The logical operation this span represents in lowercase (e.g. rpc method).\n   * Leave absent if unknown.\n   */\n  name: string;\n  /**\n   * The parent span ID or absent if this the root span in a trace.\n   */\n  parentId?: string;\n  /**\n   * Unique 64bit identifier for this operation within the trace.\n   */\n  id: string;\n  /**\n   * When present, kind clarifies timestamp, duration and remoteEndpoint.\n   * When absent, the span is local or incomplete.\n   */\n  kind?: SpanKind;\n  /**\n   * Epoch microseconds of the start of this span, possibly absent if\n   * incomplete.\n   */\n  timestamp: number;\n  /**\n   * Duration in microseconds of the critical path, if known.\n   */\n  duration: number;\n  /**\n   * True is a request to store this span even if it overrides sampling policy.\n   * This is true when the `X-B3-Flags` header has a value of 1.\n   */\n  debug?: boolean;\n  /**\n   * True if we are contributing to a span started by another tracer (ex on a\n   * different host).\n   */\n  shared?: boolean;\n  /**\n   * The host that recorded this span, primarily for query by service name.\n   */\n  localEndpoint: Endpoint;\n  /**\n   * Associates events that explain latency with the time they happened.\n   */\n  annotations?: Annotation[];\n  /**\n   * Tags give your span context for search, viewing and analysis.\n   */\n  tags: Tags;\n  /**\n   * TODO: `remoteEndpoint`, do we need to support it?\n   * When an RPC (or messaging) span, indicates the other side of the\n   * connection.\n   */\n}\n\n/**\n * Associates an event that explains latency with a timestamp.\n * Unlike log statements, annotations are often codes. Ex. \"ws\" for WireSend\n * Zipkin v1 core annotations such as \"cs\" and \"sr\" have been replaced with\n * Span.Kind, which interprets timestamp and duration.\n */\nexport interface Annotation {\n  /**\n   * Epoch microseconds of this event.\n   * For example, 1502787600000000 corresponds to 2017-08-15 09:00 UTC\n   */\n  timestamp: number;\n  /**\n   * Usually a short tag indicating an event, like \"error\"\n   * While possible to add larger data, such as garbage collection details, low\n   * cardinality event names both keep the size of spans down and also are easy\n   * to search against.\n   */\n  value: string;\n}\n\n/**\n * The network context of a node in the service graph.\n */\nexport interface Endpoint {\n  /**\n   * Lower-case label of this node in the service graph, such as \"favstar\".\n   * Leave absent if unknown.\n   * This is a primary label for trace lookup and aggregation, so it should be\n   * intuitive and consistent. Many use a name from service discovery.\n   */\n  serviceName?: string;\n  /**\n   * The text representation of the primary IPv4 address associated with this\n   * connection. Ex. ************** Absent if unknown.\n   */\n  ipv4?: string;\n  /**\n   * The text representation of the primary IPv6 address associated with a\n   * connection. Ex. 2001:db8::c001 Absent if unknown.\n   * Prefer using the ipv4 field for mapped addresses.\n   */\n  port?: number;\n}\n\n/**\n * Adds context to a span, for search, viewing and analysis.\n * For example, a key \"your_app.version\" would let you lookup traces by version.\n * A tag \"sql.query\" isn't searchable, but it can help in debugging when viewing\n * a trace.\n */\nexport interface Tags {\n  [tagKey: string]: unknown;\n}\n\n/**\n * When present, kind clarifies timestamp, duration and remoteEndpoint. When\n * absent, the span is local or incomplete. Unlike client and server, there\n * is no direct critical path latency relationship between producer and\n * consumer spans.\n * `CLIENT`\n *   timestamp is the moment a request was sent to the server.\n *   duration is the delay until a response or an error was received.\n *   remoteEndpoint is the server.\n * `SERVER`\n *   timestamp is the moment a client request was received.\n *   duration is the delay until a response was sent or an error.\n *   remoteEndpoint is the client.\n * `PRODUCER`\n *   timestamp is the moment a message was sent to a destination.\n *   duration is the delay sending the message, such as batching.\n *   remoteEndpoint is the broker.\n * `CONSUMER`\n *   timestamp is the moment a message was received from an origin.\n *   duration is the delay consuming the message, such as from backlog.\n *   remoteEndpoint - Represents the broker. Leave serviceName absent if unknown.\n */\nexport enum SpanKind {\n  CLIENT = 'CLIENT',\n  SERVER = 'SERVER',\n  CONSUMER = 'CONSUMER',\n  PRODUCER = 'PRODUCER',\n}\n\n/**\n * interface for function that will send zipkin spans\n */\nexport type SendFunction = (\n  zipkinSpans: Span[],\n  done: (result: ExportResult) => void\n) => void;\n\nexport type GetHeaders = () => Record<string, string> | undefined;\n\nexport type SendFn = (\n  zipkinSpans: Span[],\n  done: (result: ExportResult) => void\n) => void;\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG,CAyIH;;;;;;;;;;;;;;;;;;;;;GAqBG;;;AACH,IAAY,QAKX;AALD,CAAA,SAAY,QAAQ;IAClB,QAAA,CAAA,SAAA,GAAA,QAAiB,CAAA;IACjB,QAAA,CAAA,SAAA,GAAA,QAAiB,CAAA;IACjB,QAAA,CAAA,WAAA,GAAA,UAAqB,CAAA;IACrB,QAAA,CAAA,WAAA,GAAA,UAAqB,CAAA;AACvB,CAAC,EALW,QAAQ,IAAA,CAAR,QAAQ,GAAA,CAAA,CAAA,GAKnB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5919, "column": 0}, "map": {"version": 3, "file": "transform.js", "sourceRoot": "", "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/node_modules/%40opentelemetry/exporter-zipkin/src/transform.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport * as api from '@opentelemetry/api';\nimport { ReadableSpan, TimedEvent } from '@opentelemetry/sdk-trace-base';\nimport { hrTimeToMicroseconds } from '@opentelemetry/core';\nimport * as zipkinTypes from './types';\n\nconst ZIPKIN_SPAN_KIND_MAPPING = {\n  [api.SpanKind.CLIENT]: zipkinTypes.SpanKind.CLIENT,\n  [api.SpanKind.SERVER]: zipkinTypes.SpanKind.SERVER,\n  [api.SpanKind.CONSUMER]: zipkinTypes.SpanKind.CONSUMER,\n  [api.SpanKind.PRODUCER]: zipkinTypes.SpanKind.PRODUCER,\n  // When absent, the span is local.\n  [api.SpanKind.INTERNAL]: undefined,\n};\n\nexport const defaultStatusCodeTagName = 'otel.status_code';\nexport const defaultStatusErrorTagName = 'error';\n\n/**\n * Translate OpenTelemetry ReadableSpan to ZipkinSpan format\n * @param span Span to be translated\n */\nexport function toZipkinSpan(\n  span: ReadableSpan,\n  serviceName: string,\n  statusCodeTagName: string,\n  statusErrorTagName: string\n): zipkinTypes.Span {\n  const zipkinSpan: zipkinTypes.Span = {\n    traceId: span.spanContext().traceId,\n    parentId: span.parentSpanId,\n    name: span.name,\n    id: span.spanContext().spanId,\n    kind: ZIPKIN_SPAN_KIND_MAPPING[span.kind],\n    timestamp: hrTimeToMicroseconds(span.startTime),\n    duration: Math.round(hrTimeToMicroseconds(span.duration)),\n    localEndpoint: { serviceName },\n    tags: _toZipkinTags(span, statusCodeTagName, statusErrorTagName),\n    annotations: span.events.length\n      ? _toZipkinAnnotations(span.events)\n      : undefined,\n  };\n\n  return zipkinSpan;\n}\n\n/** Converts OpenTelemetry Span properties to Zipkin Tags format. */\nexport function _toZipkinTags(\n  {\n    attributes,\n    resource,\n    status,\n    droppedAttributesCount,\n    droppedEventsCount,\n    droppedLinksCount,\n  }: ReadableSpan,\n  statusCodeTagName: string,\n  statusErrorTagName: string\n): zipkinTypes.Tags {\n  const tags: { [key: string]: string } = {};\n  for (const key of Object.keys(attributes)) {\n    tags[key] = String(attributes[key]);\n  }\n  if (status.code !== api.SpanStatusCode.UNSET) {\n    tags[statusCodeTagName] = String(api.SpanStatusCode[status.code]);\n  }\n  if (status.code === api.SpanStatusCode.ERROR && status.message) {\n    tags[statusErrorTagName] = status.message;\n  }\n  /* Add droppedAttributesCount as a tag */\n  if (droppedAttributesCount) {\n    tags['otel.dropped_attributes_count'] = String(droppedAttributesCount);\n  }\n\n  /* Add droppedEventsCount as a tag */\n  if (droppedEventsCount) {\n    tags['otel.dropped_events_count'] = String(droppedEventsCount);\n  }\n\n  /* Add droppedLinksCount as a tag */\n  if (droppedLinksCount) {\n    tags['otel.dropped_links_count'] = String(droppedLinksCount);\n  }\n\n  Object.keys(resource.attributes).forEach(\n    name => (tags[name] = String(resource.attributes[name]))\n  );\n\n  return tags;\n}\n\n/**\n * Converts OpenTelemetry Events to Zipkin Annotations format.\n */\nexport function _toZipkinAnnotations(\n  events: TimedEvent[]\n): zipkinTypes.Annotation[] {\n  return events.map(event => ({\n    timestamp: Math.round(hrTimeToMicroseconds(event.time)),\n    value: event.name,\n  }));\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;;;;;;AAEH,OAAO,KAAK,GAAG,MAAM,oBAAoB,CAAC;AAE1C,OAAO,EAAE,oBAAoB,EAAE,MAAM,qBAAqB,CAAC;AAC3D,OAAO,KAAK,WAAW,MAAM,SAAS,CAAC;;;;;;;;;;;;;;;;;;;AAEvC,IAAM,wBAAwB,GAAA,CAAA,KAAA,CAAA,GAC5B,EAAA,CAAC,GAAG,CAAC,uLAAQ,CAAC,MAAM,CAAA,qLAAG,WAAW,AAAS,CAAR,AAAS,MAAM,EAClD,EAAA,iLAAC,GAAG,CAAC,OAAQ,CAAC,MAAM,CAAA,qLAAG,WAAW,AAAS,CAAR,AAAS,MAAM,EAClD,EAAA,iLAAC,GAAG,CAAC,OAAQ,CAAC,QAAQ,CAAA,qLAAG,WAAW,AAAS,CAAR,AAAS,QAAQ,EACtD,EAAA,iLAAC,GAAG,CAAC,OAAQ,CAAC,QAAQ,CAAA,qLAAG,WAAW,AAAS,CAAR,AAAS,QAAQ,EACtD,kCAAkC;AAClC,EAAA,iLAAC,GAAG,CAAC,OAAQ,CAAC,QAAQ,CAAA,GAAG,SAAS,KACnC,CAAC;AAEK,IAAM,wBAAwB,GAAG,kBAAkB,CAAC;AACpD,IAAM,yBAAyB,GAAG,OAAO,CAAC;AAM3C,SAAU,YAAY,CAC1B,IAAkB,EAClB,WAAmB,EACnB,iBAAyB,EACzB,kBAA0B;IAE1B,IAAM,UAAU,GAAqB;QACnC,OAAO,EAAE,IAAI,CAAC,WAAW,EAAE,CAAC,OAAO;QACnC,QAAQ,EAAE,IAAI,CAAC,YAAY;QAC3B,IAAI,EAAE,IAAI,CAAC,IAAI;QACf,EAAE,EAAE,IAAI,CAAC,WAAW,EAAE,CAAC,MAAM;QAC7B,IAAI,EAAE,wBAAwB,CAAC,IAAI,CAAC,IAAI,CAAC;QACzC,SAAS,4OAAE,uBAAA,AAAoB,EAAC,IAAI,CAAC,SAAS,CAAC;QAC/C,QAAQ,EAAE,IAAI,CAAC,KAAK,2OAAC,uBAAA,AAAoB,EAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QACzD,aAAa,EAAE;YAAE,WAAW,EAAA,WAAA;QAAA,CAAE;QAC9B,IAAI,EAAE,aAAa,CAAC,IAAI,EAAE,iBAAiB,EAAE,kBAAkB,CAAC;QAChE,WAAW,EAAE,IAAI,CAAC,MAAM,CAAC,MAAM,GAC3B,oBAAoB,CAAC,IAAI,CAAC,MAAM,CAAC,GACjC,SAAS;KACd,CAAC;IAEF,OAAO,UAAU,CAAC;AACpB,CAAC;AAGK,SAAU,aAAa,CAC3B,EAOe,EACf,iBAAyB,EACzB,kBAA0B;;QARxB,UAAU,GAAA,GAAA,UAAA,EACV,QAAQ,GAAA,GAAA,QAAA,EACR,MAAM,GAAA,GAAA,MAAA,EACN,sBAAsB,GAAA,GAAA,sBAAA,EACtB,kBAAkB,GAAA,GAAA,kBAAA,EAClB,iBAAiB,GAAA,GAAA,iBAAA;IAKnB,IAAM,IAAI,GAA8B,CAAA,CAAE,CAAC;;QAC3C,IAAkB,IAAA,KAAA,SAAA,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,CAAA,EAAA,KAAA,GAAA,IAAA,EAAA,EAAA,CAAA,GAAA,IAAA,EAAA,KAAA,GAAA,IAAA,GAAE;YAAtC,IAAM,GAAG,GAAA,GAAA,KAAA;YACZ,IAAI,CAAC,GAAG,CAAC,GAAG,MAAM,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC;SACrC;;;;;;;;;;;;IACD,IAAI,MAAM,CAAC,IAAI,kLAAK,GAAG,CAAC,aAAc,CAAC,KAAK,EAAE;QAC5C,IAAI,CAAC,iBAAiB,CAAC,GAAG,MAAM,6KAAC,GAAG,CAAC,cAAc,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC;KACnE;IACD,IAAI,MAAM,CAAC,IAAI,kLAAK,GAAG,CAAC,aAAc,CAAC,KAAK,IAAI,MAAM,CAAC,OAAO,EAAE;QAC9D,IAAI,CAAC,kBAAkB,CAAC,GAAG,MAAM,CAAC,OAAO,CAAC;KAC3C;IACD,uCAAA,EAAyC,CACzC,IAAI,sBAAsB,EAAE;QAC1B,IAAI,CAAC,+BAA+B,CAAC,GAAG,MAAM,CAAC,sBAAsB,CAAC,CAAC;KACxE;IAED,mCAAA,EAAqC,CACrC,IAAI,kBAAkB,EAAE;QACtB,IAAI,CAAC,2BAA2B,CAAC,GAAG,MAAM,CAAC,kBAAkB,CAAC,CAAC;KAChE;IAED,kCAAA,EAAoC,CACpC,IAAI,iBAAiB,EAAE;QACrB,IAAI,CAAC,0BAA0B,CAAC,GAAG,MAAM,CAAC,iBAAiB,CAAC,CAAC;KAC9D;IAED,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC,OAAO,CACtC,SAAA,IAAI;QAAI,OAAA,AAAC,IAAI,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC,QAAQ,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC;IAAhD,CAAgD,CACzD,CAAC;IAEF,OAAO,IAAI,CAAC;AACd,CAAC;AAKK,SAAU,oBAAoB,CAClC,MAAoB;IAEpB,OAAO,MAAM,CAAC,GAAG,CAAC,SAAA,KAAK;QAAI,OAAA,AAAC;YAC1B,SAAS,EAAE,IAAI,CAAC,KAAK,2OAAC,uBAAA,AAAoB,EAAC,KAAK,CAAC,IAAI,CAAC,CAAC;YACvD,KAAK,EAAE,KAAK,CAAC,IAAI;SAClB,CAAC;IAHyB,CAGzB,CAAC,CAAC;AACN,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 6037, "column": 0}, "map": {"version": 3, "file": "utils.js", "sourceRoot": "", "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/node_modules/%40opentelemetry/exporter-zipkin/src/utils.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nimport { GetHeaders } from './types';\n\nexport function prepareGetHeaders(\n  getExportRequestHeaders: GetHeaders\n): () => Record<string, string> | undefined {\n  return function () {\n    return getExportRequestHeaders();\n  };\n}\n"], "names": [], "mappings": ";;;AAiBM,SAAU,iBAAiB,CAC/B,uBAAmC;IAEnC,OAAO;QACL,OAAO,uBAAuB,EAAE,CAAC;IACnC,CAAC,CAAC;AACJ,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 6051, "column": 0}, "map": {"version": 3, "file": "zipkin.js", "sourceRoot": "", "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/node_modules/%40opentelemetry/exporter-zipkin/src/zipkin.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { diag } from '@opentelemetry/api';\nimport { ExportResult, ExportResultCode, getEnv } from '@opentelemetry/core';\nimport { SpanExporter, ReadableSpan } from '@opentelemetry/sdk-trace-base';\nimport { prepareSend } from './platform/index';\nimport * as zipkinTypes from './types';\nimport {\n  toZipkinSpan,\n  defaultStatusCodeTagName,\n  defaultStatusErrorTagName,\n} from './transform';\nimport { SEMRESATTRS_SERVICE_NAME } from '@opentelemetry/semantic-conventions';\nimport { prepareGetHeaders } from './utils';\n\n/**\n * Zipkin Exporter\n */\nexport class ZipkinExporter implements SpanExporter {\n  private readonly DEFAULT_SERVICE_NAME = 'OpenTelemetry Service';\n  private readonly _statusCodeTagName: string;\n  private readonly _statusDescriptionTagName: string;\n  private _urlStr: string;\n  private _send: zipkinTypes.SendFunction;\n  private _getHeaders: zipkinTypes.GetHeaders | undefined;\n  private _serviceName?: string;\n  private _isShutdown: boolean;\n  private _sendingPromises: Promise<unknown>[] = [];\n\n  constructor(config: zipkinTypes.ExporterConfig = {}) {\n    this._urlStr = config.url || getEnv().OTEL_EXPORTER_ZIPKIN_ENDPOINT;\n    this._send = prepareSend(this._urlStr, config.headers);\n    this._serviceName = config.serviceName;\n    this._statusCodeTagName =\n      config.statusCodeTagName || defaultStatusCodeTagName;\n    this._statusDescriptionTagName =\n      config.statusDescriptionTagName || defaultStatusErrorTagName;\n    this._isShutdown = false;\n    if (typeof config.getExportRequestHeaders === 'function') {\n      this._getHeaders = prepareGetHeaders(config.getExportRequestHeaders);\n    } else {\n      // noop\n      this._beforeSend = function () {};\n    }\n  }\n\n  /**\n   * Export spans.\n   */\n  export(\n    spans: ReadableSpan[],\n    resultCallback: (result: ExportResult) => void\n  ): void {\n    const serviceName = String(\n      this._serviceName ||\n        spans[0].resource.attributes[SEMRESATTRS_SERVICE_NAME] ||\n        this.DEFAULT_SERVICE_NAME\n    );\n\n    diag.debug('Zipkin exporter export');\n    if (this._isShutdown) {\n      setTimeout(() =>\n        resultCallback({\n          code: ExportResultCode.FAILED,\n          error: new Error('Exporter has been shutdown'),\n        })\n      );\n      return;\n    }\n    const promise = new Promise<void>(resolve => {\n      this._sendSpans(spans, serviceName, result => {\n        resolve();\n        resultCallback(result);\n      });\n    });\n\n    this._sendingPromises.push(promise);\n    const popPromise = () => {\n      const index = this._sendingPromises.indexOf(promise);\n      this._sendingPromises.splice(index, 1);\n    };\n    promise.then(popPromise, popPromise);\n  }\n\n  /**\n   * Shutdown exporter. Noop operation in this exporter.\n   */\n  shutdown(): Promise<void> {\n    diag.debug('Zipkin exporter shutdown');\n    this._isShutdown = true;\n    return this.forceFlush();\n  }\n\n  /**\n   * Exports any pending spans in exporter\n   */\n  forceFlush(): Promise<void> {\n    return new Promise((resolve, reject) => {\n      Promise.all(this._sendingPromises).then(() => {\n        resolve();\n      }, reject);\n    });\n  }\n\n  /**\n   * if user defines getExportRequestHeaders in config then this will be called\n   * every time before send, otherwise it will be replaced with noop in\n   * constructor\n   * @default noop\n   */\n  private _beforeSend() {\n    if (this._getHeaders) {\n      this._send = prepareSend(this._urlStr, this._getHeaders());\n    }\n  }\n\n  /**\n   * Transform spans and sends to Zipkin service.\n   */\n  private _sendSpans(\n    spans: ReadableSpan[],\n    serviceName: string,\n    done?: (result: ExportResult) => void\n  ) {\n    const zipkinSpans = spans.map(span =>\n      toZipkinSpan(\n        span,\n        String(\n          span.attributes[SEMRESATTRS_SERVICE_NAME] ||\n            span.resource.attributes[SEMRESATTRS_SERVICE_NAME] ||\n            serviceName\n        ),\n        this._statusCodeTagName,\n        this._statusDescriptionTagName\n      )\n    );\n    this._beforeSend();\n    return this._send(zipkinSpans, (result: ExportResult) => {\n      if (done) {\n        return done(result);\n      }\n    });\n  }\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;AAEH,OAAO,EAAE,IAAI,EAAE,MAAM,oBAAoB,CAAC;AAC1C,OAAO,EAAgB,gBAAgB,EAAE,MAAM,EAAE,MAAM,qBAAqB,CAAC;;AAE7E,OAAO,EAAE,WAAW,EAAE,MAAM,kBAAkB,CAAC;AAE/C,OAAO,EACL,YAAY,EACZ,wBAAwB,EACxB,yBAAyB,GAC1B,MAAM,aAAa,CAAC;AACrB,OAAO,EAAE,wBAAwB,EAAE,MAAM,qCAAqC,CAAC;AAC/E,OAAO,EAAE,iBAAiB,EAAE,MAAM,SAAS,CAAC;;;;;;;AAE5C;;GAEG,CACH,IAAA,iBAAA;IAWE,SAAA,eAAY,MAAuC;QAAvC,IAAA,WAAA,KAAA,GAAA;YAAA,SAAA,CAAA,CAAuC;QAAA;QAVlC,IAAA,CAAA,oBAAoB,GAAG,uBAAuB,CAAC;QAQxD,IAAA,CAAA,gBAAgB,GAAuB,EAAE,CAAC;QAGhD,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC,GAAG,KAAI,sQAAA,AAAM,EAAE,EAAC,6BAA6B,CAAC;QACpE,IAAI,CAAC,KAAK,+MAAG,cAAA,AAAW,EAAC,IAAI,CAAC,OAAO,EAAE,MAAM,CAAC,OAAO,CAAC,CAAC;QACvD,IAAI,CAAC,YAAY,GAAG,MAAM,CAAC,WAAW,CAAC;QACvC,IAAI,CAAC,kBAAkB,GACrB,MAAM,CAAC,iBAAiB,0LAAI,2BAAwB,CAAC;QACvD,IAAI,CAAC,yBAAyB,GAC5B,MAAM,CAAC,wBAAwB,0LAAI,4BAAyB,CAAC;QAC/D,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC;QACzB,IAAI,OAAO,MAAM,CAAC,uBAAuB,KAAK,UAAU,EAAE;YACxD,IAAI,CAAC,WAAW,yLAAG,oBAAA,AAAiB,EAAC,MAAM,CAAC,uBAAuB,CAAC,CAAC;SACtE,MAAM;YACL,OAAO;YACP,IAAI,CAAC,WAAW,GAAG,YAAa,CAAC,CAAC;SACnC;IACH,CAAC;IAED;;OAEG,CACH,eAAA,SAAA,CAAA,MAAM,GAAN,SACE,KAAqB,EACrB,cAA8C;QAFhD,IAAA,QAAA,IAAA,CAiCC;QA7BC,IAAM,WAAW,GAAG,MAAM,CACxB,IAAI,CAAC,YAAY,IACf,KAAK,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,UAAU,CAAC,4SAAwB,CAAC,IACtD,IAAI,CAAC,oBAAoB,CAC5B,CAAC;iLAEF,OAAI,CAAC,KAAK,CAAC,wBAAwB,CAAC,CAAC;QACrC,IAAI,IAAI,CAAC,WAAW,EAAE;YACpB,UAAU,CAAC;gBACT,OAAA,cAAc,CAAC;oBACb,IAAI,EAAE,uPAAgB,CAAC,MAAM;oBAC7B,KAAK,EAAE,IAAI,KAAK,CAAC,4BAA4B,CAAC;iBAC/C,CAAC;YAHF,CAGE,CACH,CAAC;YACF,OAAO;SACR;QACD,IAAM,OAAO,GAAG,IAAI,OAAO,CAAO,SAAA,OAAO;YACvC,KAAI,CAAC,UAAU,CAAC,KAAK,EAAE,WAAW,EAAE,SAAA,MAAM;gBACxC,OAAO,EAAE,CAAC;gBACV,cAAc,CAAC,MAAM,CAAC,CAAC;YACzB,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QACpC,IAAM,UAAU,GAAG;YACjB,IAAM,KAAK,GAAG,KAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;YACrD,KAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;QACzC,CAAC,CAAC;QACF,OAAO,CAAC,IAAI,CAAC,UAAU,EAAE,UAAU,CAAC,CAAC;IACvC,CAAC;IAED;;OAEG,CACH,eAAA,SAAA,CAAA,QAAQ,GAAR;iLACE,OAAI,CAAC,KAAK,CAAC,0BAA0B,CAAC,CAAC;QACvC,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;QACxB,OAAO,IAAI,CAAC,UAAU,EAAE,CAAC;IAC3B,CAAC;IAED;;OAEG,CACH,eAAA,SAAA,CAAA,UAAU,GAAV;QAAA,IAAA,QAAA,IAAA,CAMC;QALC,OAAO,IAAI,OAAO,CAAC,SAAC,OAAO,EAAE,MAAM;YACjC,OAAO,CAAC,GAAG,CAAC,KAAI,CAAC,gBAAgB,CAAC,CAAC,IAAI,CAAC;gBACtC,OAAO,EAAE,CAAC;YACZ,CAAC,EAAE,MAAM,CAAC,CAAC;QACb,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;;;;OAKG,CACK,eAAA,SAAA,CAAA,WAAW,GAAnB;QACE,IAAI,IAAI,CAAC,WAAW,EAAE;YACpB,IAAI,CAAC,KAAK,+MAAG,cAAA,AAAW,EAAC,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,WAAW,EAAE,CAAC,CAAC;SAC5D;IACH,CAAC;IAED;;OAEG,CACK,eAAA,SAAA,CAAA,UAAU,GAAlB,SACE,KAAqB,EACrB,WAAmB,EACnB,IAAqC;QAHvC,IAAA,QAAA,IAAA,CAuBC;QAlBC,IAAM,WAAW,GAAG,KAAK,CAAC,GAAG,CAAC,SAAA,IAAI;YAChC,iMAAA,eAAA,AAAY,EACV,IAAI,EACJ,MAAM,CACJ,IAAI,CAAC,UAAU,kRAAC,2BAAwB,CAAC,IACvC,IAAI,CAAC,QAAQ,CAAC,UAAU,kRAAC,2BAAwB,CAAC,IAClD,WAAW,CACd,EACD,KAAI,CAAC,kBAAkB,EACvB,KAAI,CAAC,yBAAyB,CAC/B;QATD,CASC,CACF,CAAC;QACF,IAAI,CAAC,WAAW,EAAE,CAAC;QACnB,OAAO,IAAI,CAAC,KAAK,CAAC,WAAW,EAAE,SAAC,MAAoB;YAClD,IAAI,IAAI,EAAE;gBACR,OAAO,IAAI,CAAC,MAAM,CAAC,CAAC;aACrB;QACH,CAAC,CAAC,CAAC;IACL,CAAC;IACH,OAAA,cAAC;AAAD,CAAC,AA7HD,IA6HC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 6206, "column": 0}, "map": {"version": 3, "file": "TracerProviderWithEnvExporter.js", "sourceRoot": "", "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/node_modules/%40opentelemetry/sdk-node/src/TracerProviderWithEnvExporter.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { diag } from '@opentelemetry/api';\nimport { getEnv, getEnvWithoutDefaults } from '@opentelemetry/core';\nimport {\n  ConsoleSpanExporter,\n  SpanExporter,\n  BatchSpanProcessor,\n  SimpleSpanProcessor,\n  SDKRegistrationConfig,\n  SpanProcessor,\n} from '@opentelemetry/sdk-trace-base';\nimport {\n  NodeTracerConfig,\n  NodeTracerProvider,\n} from '@opentelemetry/sdk-trace-node';\nimport { OTLPTraceExporter as OTLPProtoTraceExporter } from '@opentelemetry/exporter-trace-otlp-proto';\nimport { OTLPTraceExporter as OTLPHttpTraceExporter } from '@opentelemetry/exporter-trace-otlp-http';\nimport { OTLPTraceExporter as OTLPGrpcTraceExporter } from '@opentelemetry/exporter-trace-otlp-grpc';\nimport { ZipkinExporter } from '@opentelemetry/exporter-zipkin';\n\nexport class TracerProviderWithEnvExporters extends NodeTracerProvider {\n  private _configuredExporters: SpanExporter[] = [];\n  private _spanProcessors: SpanProcessor[] | undefined;\n  private _hasSpanProcessors: boolean = false;\n\n  static configureOtlp(): SpanExporter {\n    const protocol = this.getOtlpProtocol();\n\n    switch (protocol) {\n      case 'grpc':\n        return new OTLPGrpcTraceExporter();\n      case 'http/json':\n        return new OTLPHttpTraceExporter();\n      case 'http/protobuf':\n        return new OTLPProtoTraceExporter();\n      default:\n        diag.warn(\n          `Unsupported OTLP traces protocol: ${protocol}. Using http/protobuf.`\n        );\n        return new OTLPProtoTraceExporter();\n    }\n  }\n\n  static getOtlpProtocol(): string {\n    const parsedEnvValues = getEnvWithoutDefaults();\n\n    return (\n      parsedEnvValues.OTEL_EXPORTER_OTLP_TRACES_PROTOCOL ??\n      parsedEnvValues.OTEL_EXPORTER_OTLP_PROTOCOL ??\n      getEnv().OTEL_EXPORTER_OTLP_TRACES_PROTOCOL ??\n      getEnv().OTEL_EXPORTER_OTLP_PROTOCOL\n    );\n  }\n\n  private static configureJaeger() {\n    // The JaegerExporter does not support being required in bundled\n    // environments. By delaying the require statement to here, we only crash when\n    // the exporter is actually used in such an environment.\n    try {\n      // eslint-disable-next-line @typescript-eslint/no-var-requires\n      const { JaegerExporter } = require('@opentelemetry/exporter-jaeger');\n      return new JaegerExporter();\n    } catch (e) {\n      throw new Error(\n        `Could not instantiate JaegerExporter. This could be due to the JaegerExporter's lack of support for bundling. If possible, use @opentelemetry/exporter-trace-otlp-proto instead. Original Error: ${e}`\n      );\n    }\n  }\n\n  protected static override _registeredExporters = new Map<\n    string,\n    () => SpanExporter\n  >([\n    ['otlp', () => this.configureOtlp()],\n    ['zipkin', () => new ZipkinExporter()],\n    ['jaeger', () => this.configureJaeger()],\n    ['console', () => new ConsoleSpanExporter()],\n  ]);\n\n  public constructor(config: NodeTracerConfig = {}) {\n    super(config);\n    let traceExportersList = this.filterBlanksAndNulls(\n      Array.from(new Set(getEnv().OTEL_TRACES_EXPORTER.split(',')))\n    );\n\n    if (traceExportersList[0] === 'none') {\n      diag.warn(\n        'OTEL_TRACES_EXPORTER contains \"none\". SDK will not be initialized.'\n      );\n    } else if (traceExportersList.length === 0) {\n      diag.warn('OTEL_TRACES_EXPORTER is empty. Using default otlp exporter.');\n\n      traceExportersList = ['otlp'];\n      this.createExportersFromList(traceExportersList);\n\n      this._spanProcessors = this.configureSpanProcessors(\n        this._configuredExporters\n      );\n      this._spanProcessors.forEach(processor => {\n        this.addSpanProcessor(processor);\n      });\n    } else {\n      if (\n        traceExportersList.length > 1 &&\n        traceExportersList.includes('none')\n      ) {\n        diag.warn(\n          'OTEL_TRACES_EXPORTER contains \"none\" along with other exporters. Using default otlp exporter.'\n        );\n        traceExportersList = ['otlp'];\n      }\n\n      this.createExportersFromList(traceExportersList);\n\n      if (this._configuredExporters.length > 0) {\n        this._spanProcessors = this.configureSpanProcessors(\n          this._configuredExporters\n        );\n        this._spanProcessors.forEach(processor => {\n          this.addSpanProcessor(processor);\n        });\n      } else {\n        diag.warn(\n          'Unable to set up trace exporter(s) due to invalid exporter and/or protocol values.'\n        );\n      }\n    }\n  }\n\n  override addSpanProcessor(spanProcessor: SpanProcessor) {\n    super.addSpanProcessor(spanProcessor);\n    this._hasSpanProcessors = true;\n  }\n\n  override register(config?: SDKRegistrationConfig) {\n    if (this._hasSpanProcessors) {\n      super.register(config);\n    }\n  }\n\n  private createExportersFromList(exporterList: string[]) {\n    exporterList.forEach(exporterName => {\n      const exporter = this._getSpanExporter(exporterName);\n      if (exporter) {\n        this._configuredExporters.push(exporter);\n      } else {\n        diag.warn(`Unrecognized OTEL_TRACES_EXPORTER value: ${exporterName}.`);\n      }\n    });\n  }\n\n  private configureSpanProcessors(\n    exporters: SpanExporter[]\n  ): (BatchSpanProcessor | SimpleSpanProcessor)[] {\n    return exporters.map(exporter => {\n      if (exporter instanceof ConsoleSpanExporter) {\n        return new SimpleSpanProcessor(exporter);\n      } else {\n        return new BatchSpanProcessor(exporter);\n      }\n    });\n  }\n\n  private filterBlanksAndNulls(list: string[]): string[] {\n    return list.map(item => item.trim()).filter(s => s !== 'null' && s !== '');\n  }\n}\n"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG;;;;;AAEH,MAAA,sCAA0C;AAC1C,MAAA,wCAAoE;AACpE,MAAA,4DAOuC;AACvC,MAAA,4DAGuC;AACvC,MAAA,kFAAuG;AACvG,MAAA,gFAAqG;AACrG,MAAA,gFAAqG;AACrG,MAAA,8DAAgE;AAEhE,MAAa,8BAA+B,SAAQ,iBAAA,kBAAkB;IA2DpE,YAAmB,SAA2B,CAAA,CAAE,CAAA;QAC9C,KAAK,CAAC,MAAM,CAAC,CAAC;QA3DR,IAAA,CAAA,oBAAoB,GAAmB,EAAE,CAAC;QAE1C,IAAA,CAAA,kBAAkB,GAAY,KAAK,CAAC;QA0D1C,IAAI,kBAAkB,GAAG,IAAI,CAAC,oBAAoB,CAChD,KAAK,CAAC,IAAI,CAAC,IAAI,GAAG,CAAC,CAAA,GAAA,OAAA,MAAM,GAAE,CAAC,oBAAoB,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAC9D,CAAC;QAEF,IAAI,kBAAkB,CAAC,CAAC,CAAC,KAAK,MAAM,EAAE;YACpC,MAAA,IAAI,CAAC,IAAI,CACP,oEAAoE,CACrE,CAAC;SACH,MAAM,IAAI,kBAAkB,CAAC,MAAM,KAAK,CAAC,EAAE;YAC1C,MAAA,IAAI,CAAC,IAAI,CAAC,6DAA6D,CAAC,CAAC;YAEzE,kBAAkB,GAAG;gBAAC,MAAM;aAAC,CAAC;YAC9B,IAAI,CAAC,uBAAuB,CAAC,kBAAkB,CAAC,CAAC;YAEjD,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,uBAAuB,CACjD,IAAI,CAAC,oBAAoB,CAC1B,CAAC;YACF,IAAI,CAAC,eAAe,CAAC,OAAO,EAAC,SAAS,CAAC,EAAE;gBACvC,IAAI,CAAC,gBAAgB,CAAC,SAAS,CAAC,CAAC;YACnC,CAAC,CAAC,CAAC;SACJ,MAAM;YACL,IACE,kBAAkB,CAAC,MAAM,GAAG,CAAC,IAC7B,kBAAkB,CAAC,QAAQ,CAAC,MAAM,CAAC,EACnC;gBACA,MAAA,IAAI,CAAC,IAAI,CACP,+FAA+F,CAChG,CAAC;gBACF,kBAAkB,GAAG;oBAAC,MAAM;iBAAC,CAAC;aAC/B;YAED,IAAI,CAAC,uBAAuB,CAAC,kBAAkB,CAAC,CAAC;YAEjD,IAAI,IAAI,CAAC,oBAAoB,CAAC,MAAM,GAAG,CAAC,EAAE;gBACxC,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,uBAAuB,CACjD,IAAI,CAAC,oBAAoB,CAC1B,CAAC;gBACF,IAAI,CAAC,eAAe,CAAC,OAAO,EAAC,SAAS,CAAC,EAAE;oBACvC,IAAI,CAAC,gBAAgB,CAAC,SAAS,CAAC,CAAC;gBACnC,CAAC,CAAC,CAAC;aACJ,MAAM;gBACL,MAAA,IAAI,CAAC,IAAI,CACP,oFAAoF,CACrF,CAAC;aACH;SACF;IACH,CAAC;IAtGD,MAAM,CAAC,aAAa,GAAA;QAClB,MAAM,QAAQ,GAAG,IAAI,CAAC,eAAe,EAAE,CAAC;QAExC,OAAQ,QAAQ,EAAE;YAChB,KAAK,MAAM;gBACT,OAAO,IAAI,2BAAA,iBAAqB,EAAE,CAAC;YACrC,KAAK,WAAW;gBACd,OAAO,IAAI,2BAAA,iBAAqB,EAAE,CAAC;YACrC,KAAK,eAAe;gBAClB,OAAO,IAAI,4BAAA,iBAAsB,EAAE,CAAC;YACtC;gBACE,MAAA,IAAI,CAAC,IAAI,CACP,CAAA,kCAAA,EAAqC,QAAQ,CAAA,sBAAA,CAAwB,CACtE,CAAC;gBACF,OAAO,IAAI,4BAAA,iBAAsB,EAAE,CAAC;SACvC;IACH,CAAC;IAED,MAAM,CAAC,eAAe,GAAA;;QACpB,MAAM,eAAe,GAAG,CAAA,GAAA,OAAA,qBAAqB,GAAE,CAAC;QAEhD,OAAO,AACL,CAAA,KAAA,CAAA,KAAA,CAAA,KAAA,eAAe,CAAC,kCAAkC,MAAA,QAAA,OAAA,KAAA,IAAA,KAClD,eAAe,CAAC,2BAA2B,MAAA,QAAA,OAAA,KAAA,IAAA,KAC3C,CAAA,GAAA,OAAA,MAAM,GAAE,CAAC,kCAAkC,MAAA,QAAA,OAAA,KAAA,IAAA,KAC3C,CAAA,GAAA,OAAA,MAAM,GAAE,CAAC,2BAA2B,CACrC,CAAC;IACJ,CAAC;IAEO,MAAM,CAAC,eAAe,GAAA;QAC5B,gEAAgE;QAChE,8EAA8E;QAC9E,wDAAwD;QACxD,IAAI;YACF,8DAA8D;YAC9D,MAAM,EAAE,cAAc,EAAE,GAAG,OAAO,CAAC,gCAAgC,CAAC,CAAC;;;;;YACrE,OAAO,IAAI,cAAc,EAAE,CAAC;SAC7B,CAAC,OAAO,CAAC,EAAE;YACV,MAAM,IAAI,KAAK,CACb,CAAA,iMAAA,EAAoM,CAAC,EAAE,CACxM,CAAC;SACH;IACH,CAAC;IA8DQ,gBAAgB,CAAC,aAA4B,EAAA;QACpD,KAAK,CAAC,gBAAgB,CAAC,aAAa,CAAC,CAAC;QACtC,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC;IACjC,CAAC;IAEQ,QAAQ,CAAC,MAA8B,EAAA;QAC9C,IAAI,IAAI,CAAC,kBAAkB,EAAE;YAC3B,KAAK,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;SACxB;IACH,CAAC;IAEO,uBAAuB,CAAC,YAAsB,EAAA;QACpD,YAAY,CAAC,OAAO,EAAC,YAAY,CAAC,EAAE;YAClC,MAAM,QAAQ,GAAG,IAAI,CAAC,gBAAgB,CAAC,YAAY,CAAC,CAAC;YACrD,IAAI,QAAQ,EAAE;gBACZ,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;aAC1C,MAAM;gBACL,MAAA,IAAI,CAAC,IAAI,CAAC,CAAA,yCAAA,EAA4C,YAAY,CAAA,CAAA,CAAG,CAAC,CAAC;aACxE;QACH,CAAC,CAAC,CAAC;IACL,CAAC;IAEO,uBAAuB,CAC7B,SAAyB,EAAA;QAEzB,OAAO,SAAS,CAAC,GAAG,EAAC,QAAQ,CAAC,EAAE;YAC9B,IAAI,QAAQ,YAAY,iBAAA,mBAAmB,EAAE;gBAC3C,OAAO,IAAI,iBAAA,mBAAmB,CAAC,QAAQ,CAAC,CAAC;aAC1C,MAAM;gBACL,OAAO,IAAI,iBAAA,kBAAkB,CAAC,QAAQ,CAAC,CAAC;aACzC;QACH,CAAC,CAAC,CAAC;IACL,CAAC;IAEO,oBAAoB,CAAC,IAAc,EAAA;QACzC,OAAO,IAAI,CAAC,GAAG,EAAC,IAAI,CAAC,EAAE,AAAC,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC,MAAM,EAAC,CAAC,CAAC,EAAE,AAAC,CAAC,KAAK,MAAM,IAAI,CAAC,KAAK,EAAE,CAAC,CAAC;IAC7E,CAAC;;AAjJH,QAAA,8BAAA,GAAA,+BAkJC;;AAjG2B,+BAAA,oBAAoB,GAAG,IAAI,GAAG,CAGtD;IACA;QAAC,MAAM;QAAE,GAAG,CAAG,CAAD,CAAK,CAAC,aAAa,EAAE;KAAC;IACpC;QAAC,QAAQ;QAAE,GAAG,CAAG,CAAD,GAAK,kBAAA,cAAc,EAAE;KAAC;IACtC;QAAC,QAAQ;QAAE,GAAG,CAAG,CAAD,CAAK,CAAC,eAAe,EAAE;KAAC;IACxC;QAAC,SAAS;QAAE,GAAG,CAAG,CAAD,GAAK,iBAAA,mBAAmB,EAAE;KAAC;CAC7C,CAAE,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 6362, "column": 0}, "map": {"version": 3, "file": "utils.js", "sourceRoot": "", "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/node_modules/%40opentelemetry/sdk-node/src/utils.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { diag } from '@opentelemetry/api';\nimport {\n  DetectorSync,\n  envDetectorSync,\n  hostDetectorSync,\n  osDetectorSync,\n  processDetectorSync,\n  serviceInstanceIdDetectorSync,\n} from '@opentelemetry/resources';\n\nconst RESOURCE_DETECTOR_ENVIRONMENT = 'env';\nconst RESOURCE_DETECTOR_HOST = 'host';\nconst RESOURCE_DETECTOR_OS = 'os';\nconst RESOURCE_DETECTOR_PROCESS = 'process';\nconst RESOURCE_DETECTOR_SERVICE_INSTANCE_ID = 'serviceinstance';\n\nexport function getResourceDetectorsFromEnv(): Array<DetectorSync> {\n  // When updating this list, make sure to also update the section `resourceDetectors` on README.\n  const resourceDetectors = new Map<string, DetectorSync>([\n    [RESOURCE_DETECTOR_ENVIRONMENT, envDetectorSync],\n    [RESOURCE_DETECTOR_HOST, hostDetectorSync],\n    [RESOURCE_DETECTOR_OS, osDetectorSync],\n    [RESOURCE_DETECTOR_SERVICE_INSTANCE_ID, serviceInstanceIdDetectorSync],\n    [RESOURCE_DETECTOR_PROCESS, processDetectorSync],\n  ]);\n\n  const resourceDetectorsFromEnv =\n    process.env.OTEL_NODE_RESOURCE_DETECTORS?.split(',') ?? ['all'];\n\n  if (resourceDetectorsFromEnv.includes('all')) {\n    return [...resourceDetectors.values()].flat();\n  }\n\n  if (resourceDetectorsFromEnv.includes('none')) {\n    return [];\n  }\n\n  return resourceDetectorsFromEnv.flatMap(detector => {\n    const resourceDetector = resourceDetectors.get(detector);\n    if (!resourceDetector) {\n      diag.error(\n        `Invalid resource detector \"${detector}\" specified in the environment variable OTEL_NODE_RESOURCE_DETECTORS`\n      );\n    }\n    return resourceDetector || [];\n  });\n}\n"], "names": [], "mappings": "AA2CI;;AA3CJ;;;;;;;;;;;;;;GAcG;;;;AAEH,MAAA,sCAA0C;AAC1C,MAAA,kDAOkC;AAElC,MAAM,6BAA6B,GAAG,KAAK,CAAC;AAC5C,MAAM,sBAAsB,GAAG,MAAM,CAAC;AACtC,MAAM,oBAAoB,GAAG,IAAI,CAAC;AAClC,MAAM,yBAAyB,GAAG,SAAS,CAAC;AAC5C,MAAM,qCAAqC,GAAG,iBAAiB,CAAC;AAEhE,SAAgB,2BAA2B;;IACzC,+FAA+F;IAC/F,MAAM,iBAAiB,GAAG,IAAI,GAAG,CAAuB;QACtD;YAAC,6BAA6B;YAAE,YAAA,eAAe;SAAC;QAChD;YAAC,sBAAsB;YAAE,YAAA,gBAAgB;SAAC;QAC1C;YAAC,oBAAoB;YAAE,YAAA,cAAc;SAAC;QACtC;YAAC,qCAAqC;YAAE,YAAA,6BAA6B;SAAC;QACtE;YAAC,yBAAyB;YAAE,YAAA,mBAAmB;SAAC;KACjD,CAAC,CAAC;IAEH,MAAM,wBAAwB,GAC5B,CAAA,KAAA,CAAA,gLAAO,CAAC,GAAG,CAAC,4BAA4B,MAAA,QAAA,OAAA,KAAA,IAAA,KAAA,IAAA,GAAE,KAAK,CAAC,GAAG,CAAC,MAAA,QAAA,OAAA,KAAA,IAAA,KAAI;QAAC,KAAK;KAAC,CAAC;IAElE,IAAI,wBAAwB,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE;QAC5C,OAAO,CAAC;eAAG,iBAAiB,CAAC,MAAM,EAAE;SAAC,CAAC,IAAI,EAAE,CAAC;KAC/C;IAED,IAAI,wBAAwB,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE;QAC7C,OAAO,EAAE,CAAC;KACX;IAED,OAAO,wBAAwB,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE;QACjD,MAAM,gBAAgB,GAAG,iBAAiB,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;QACzD,IAAI,CAAC,gBAAgB,EAAE;YACrB,MAAA,IAAI,CAAC,KAAK,CACR,CAAA,2BAAA,EAA8B,QAAQ,CAAA,oEAAA,CAAsE,CAC7G,CAAC;SACH;QACD,OAAO,gBAAgB,IAAI,EAAE,CAAC;IAChC,CAAC,CAAC,CAAC;AACL,CAAC;AA9BD,QAAA,2BAAA,GAAA,4BA8BC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 6439, "column": 0}, "map": {"version": 3, "file": "sdk.js", "sourceRoot": "", "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/node_modules/%40opentelemetry/sdk-node/src/sdk.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  ContextManager,\n  TextMapPropagator,\n  metrics,\n  diag,\n  DiagConsoleLogger,\n} from '@opentelemetry/api';\nimport { logs } from '@opentelemetry/api-logs';\nimport {\n  Instrumentation,\n  registerInstrumentations,\n} from '@opentelemetry/instrumentation';\nimport {\n  Detector,\n  DetectorSync,\n  detectResourcesSync,\n  envDetector,\n  hostDetector,\n  IResource,\n  processDetector,\n  Resource,\n  ResourceDetectionConfig,\n} from '@opentelemetry/resources';\nimport { LogRecordProcessor, LoggerProvider } from '@opentelemetry/sdk-logs';\nimport { MeterProvider, MetricReader, View } from '@opentelemetry/sdk-metrics';\nimport {\n  BatchSpanProcessor,\n  SpanProcessor,\n} from '@opentelemetry/sdk-trace-base';\nimport {\n  NodeTracerConfig,\n  NodeTracerProvider,\n} from '@opentelemetry/sdk-trace-node';\nimport { SEMRESATTRS_SERVICE_NAME } from '@opentelemetry/semantic-conventions';\nimport { NodeSDKConfiguration } from './types';\nimport { TracerProviderWithEnvExporters } from './TracerProviderWithEnvExporter';\nimport { getEnv, getEnvWithoutDefaults } from '@opentelemetry/core';\nimport { getResourceDetectorsFromEnv } from './utils';\n\n/** This class represents everything needed to register a fully configured OpenTelemetry Node.js SDK */\n\nexport type MeterProviderConfig = {\n  /**\n   * Reference to the MetricReader instance by the NodeSDK\n   */\n  reader?: MetricReader;\n  /**\n   * List of {@link View}s that should be passed to the MeterProvider\n   */\n  views?: View[];\n};\n\nexport type LoggerProviderConfig = {\n  /**\n   * Reference to the LoggerRecordProcessor instance by the NodeSDK\n   */\n  logRecordProcessor: LogRecordProcessor;\n};\n\nexport class NodeSDK {\n  private _tracerProviderConfig?: {\n    tracerConfig: NodeTracerConfig;\n    spanProcessors: SpanProcessor[];\n    contextManager?: ContextManager;\n    textMapPropagator?: TextMapPropagator;\n  };\n  private _loggerProviderConfig?: LoggerProviderConfig;\n  private _meterProviderConfig?: MeterProviderConfig;\n  private _instrumentations: Instrumentation[];\n\n  private _resource: IResource;\n  private _resourceDetectors: Array<Detector | DetectorSync>;\n\n  private _autoDetectResources: boolean;\n\n  private _tracerProvider?: NodeTracerProvider | TracerProviderWithEnvExporters;\n  private _loggerProvider?: LoggerProvider;\n  private _meterProvider?: MeterProvider;\n  private _serviceName?: string;\n  private _configuration?: Partial<NodeSDKConfiguration>;\n\n  private _disabled?: boolean;\n\n  /**\n   * Create a new NodeJS SDK instance\n   */\n  public constructor(configuration: Partial<NodeSDKConfiguration> = {}) {\n    const env = getEnv();\n    const envWithoutDefaults = getEnvWithoutDefaults();\n\n    if (env.OTEL_SDK_DISABLED) {\n      this._disabled = true;\n      // Functions with possible side-effects are set\n      // to no-op via the _disabled flag\n    }\n\n    // Default is INFO, use environment without defaults to check\n    // if the user originally set the environment variable.\n    if (envWithoutDefaults.OTEL_LOG_LEVEL) {\n      diag.setLogger(new DiagConsoleLogger(), {\n        logLevel: envWithoutDefaults.OTEL_LOG_LEVEL,\n      });\n    }\n\n    this._configuration = configuration;\n\n    this._resource = configuration.resource ?? new Resource({});\n    let defaultDetectors: (Detector | DetectorSync)[] = [];\n    if (process.env.OTEL_NODE_RESOURCE_DETECTORS != null) {\n      defaultDetectors = getResourceDetectorsFromEnv();\n    } else {\n      defaultDetectors = [envDetector, processDetector, hostDetector];\n    }\n\n    this._resourceDetectors =\n      configuration.resourceDetectors ?? defaultDetectors;\n\n    this._serviceName = configuration.serviceName;\n\n    this._autoDetectResources = configuration.autoDetectResources ?? true;\n\n    // If a tracer provider can be created from manual configuration, create it\n    if (\n      configuration.traceExporter ||\n      configuration.spanProcessor ||\n      configuration.spanProcessors\n    ) {\n      const tracerProviderConfig: NodeTracerConfig = {};\n\n      if (configuration.sampler) {\n        tracerProviderConfig.sampler = configuration.sampler;\n      }\n      if (configuration.spanLimits) {\n        tracerProviderConfig.spanLimits = configuration.spanLimits;\n      }\n      if (configuration.idGenerator) {\n        tracerProviderConfig.idGenerator = configuration.idGenerator;\n      }\n\n      if (configuration.spanProcessor) {\n        diag.warn(\n          \"The 'spanProcessor' option is deprecated. Please use 'spanProcessors' instead.\"\n        );\n      }\n\n      const spanProcessor =\n        configuration.spanProcessor ??\n        // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n        new BatchSpanProcessor(configuration.traceExporter!);\n\n      const spanProcessors = configuration.spanProcessors ?? [spanProcessor];\n\n      this._tracerProviderConfig = {\n        tracerConfig: tracerProviderConfig,\n        spanProcessors,\n        contextManager: configuration.contextManager,\n        textMapPropagator: configuration.textMapPropagator,\n      };\n    }\n\n    if (configuration.logRecordProcessor) {\n      this._loggerProviderConfig = {\n        logRecordProcessor: configuration.logRecordProcessor,\n      };\n    }\n\n    if (configuration.metricReader || configuration.views) {\n      const meterProviderConfig: MeterProviderConfig = {};\n      if (configuration.metricReader) {\n        meterProviderConfig.reader = configuration.metricReader;\n      }\n\n      if (configuration.views) {\n        meterProviderConfig.views = configuration.views;\n      }\n\n      this._meterProviderConfig = meterProviderConfig;\n    }\n\n    this._instrumentations = configuration.instrumentations?.flat() ?? [];\n  }\n\n  /**\n   * Call this method to construct SDK components and register them with the OpenTelemetry API.\n   */\n  public start(): void {\n    if (this._disabled) {\n      return;\n    }\n\n    registerInstrumentations({\n      instrumentations: this._instrumentations,\n    });\n\n    if (this._autoDetectResources) {\n      const internalConfig: ResourceDetectionConfig = {\n        detectors: this._resourceDetectors,\n      };\n\n      this._resource = this._resource.merge(\n        detectResourcesSync(internalConfig)\n      );\n    }\n\n    this._resource =\n      this._serviceName === undefined\n        ? this._resource\n        : this._resource.merge(\n            new Resource({\n              [SEMRESATTRS_SERVICE_NAME]: this._serviceName,\n            })\n          );\n\n    // if there is a tracerProviderConfig (traceExporter/spanProcessor was set manually) or the traceExporter is set manually, use NodeTracerProvider\n    const Provider = this._tracerProviderConfig\n      ? NodeTracerProvider\n      : TracerProviderWithEnvExporters;\n\n    // If the Provider is configured with Env Exporters, we need to check if the SDK had any manual configurations and set them here\n    const tracerProvider = new Provider({\n      ...this._configuration,\n      resource: this._resource,\n    });\n\n    this._tracerProvider = tracerProvider;\n\n    if (this._tracerProviderConfig) {\n      for (const spanProcessor of this._tracerProviderConfig.spanProcessors) {\n        tracerProvider.addSpanProcessor(spanProcessor);\n      }\n    }\n\n    tracerProvider.register({\n      contextManager:\n        this._tracerProviderConfig?.contextManager ??\n        // _tracerProviderConfig may be undefined if trace-specific settings are not provided - fall back to raw config\n        this._configuration?.contextManager,\n      propagator: this._tracerProviderConfig?.textMapPropagator,\n    });\n\n    if (this._loggerProviderConfig) {\n      const loggerProvider = new LoggerProvider({\n        resource: this._resource,\n      });\n      loggerProvider.addLogRecordProcessor(\n        this._loggerProviderConfig.logRecordProcessor\n      );\n\n      this._loggerProvider = loggerProvider;\n\n      logs.setGlobalLoggerProvider(loggerProvider);\n    }\n\n    if (this._meterProviderConfig) {\n      const readers: MetricReader[] = [];\n      if (this._meterProviderConfig.reader) {\n        readers.push(this._meterProviderConfig.reader);\n      }\n      const meterProvider = new MeterProvider({\n        resource: this._resource,\n        views: this._meterProviderConfig?.views ?? [],\n        readers: readers,\n      });\n\n      this._meterProvider = meterProvider;\n\n      metrics.setGlobalMeterProvider(meterProvider);\n\n      // TODO: This is a workaround to fix https://github.com/open-telemetry/opentelemetry-js/issues/3609\n      // If the MeterProvider is not yet registered when instrumentations are registered, all metrics are dropped.\n      // This code is obsolete once https://github.com/open-telemetry/opentelemetry-js/issues/3622 is implemented.\n      for (const instrumentation of this._instrumentations) {\n        instrumentation.setMeterProvider(metrics.getMeterProvider());\n      }\n    }\n  }\n\n  public shutdown(): Promise<void> {\n    const promises: Promise<unknown>[] = [];\n    if (this._tracerProvider) {\n      promises.push(this._tracerProvider.shutdown());\n    }\n    if (this._loggerProvider) {\n      promises.push(this._loggerProvider.shutdown());\n    }\n    if (this._meterProvider) {\n      promises.push(this._meterProvider.shutdown());\n    }\n\n    return (\n      Promise.all(promises)\n        // return void instead of the array from Promise.all\n        .then(() => {})\n    );\n  }\n}\n"], "names": [], "mappings": "AA4HQ;;AA5HR;;;;;;;;;;;;;;GAcG;;;;AAEH,MAAA,sCAM4B;AAC5B,MAAA,gDAA+C;AAC/C,MAAA,8DAGwC;AACxC,MAAA,kDAUkC;AAClC,MAAA,gDAA6E;AAC7E,MAAA,sDAA+E;AAC/E,MAAA,4DAGuC;AACvC,MAAA,4DAGuC;AACvC,MAAA,wEAA+E;AAE/E,MAAA,6EAAiF;AACjF,MAAA,wCAAoE;AACpE,MAAA,6BAAsD;AAsBtD,MAAa,OAAO;IAwBlB;;OAEG,CACH,YAAmB,gBAA+C,CAAA,CAAE,CAAA;;QAClE,MAAM,GAAG,GAAG,CAAA,GAAA,OAAA,MAAM,GAAE,CAAC;QACrB,MAAM,kBAAkB,GAAG,CAAA,GAAA,OAAA,qBAAqB,GAAE,CAAC;QAEnD,IAAI,GAAG,CAAC,iBAAiB,EAAE;YACzB,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;QACtB,+CAA+C;QAC/C,kCAAkC;SACnC;QAED,6DAA6D;QAC7D,uDAAuD;QACvD,IAAI,kBAAkB,CAAC,cAAc,EAAE;YACrC,MAAA,IAAI,CAAC,SAAS,CAAC,IAAI,MAAA,iBAAiB,EAAE,EAAE;gBACtC,QAAQ,EAAE,kBAAkB,CAAC,cAAc;aAC5C,CAAC,CAAC;SACJ;QAED,IAAI,CAAC,cAAc,GAAG,aAAa,CAAC;QAEpC,IAAI,CAAC,SAAS,GAAG,CAAA,KAAA,aAAa,CAAC,QAAQ,MAAA,QAAA,OAAA,KAAA,IAAA,KAAI,IAAI,YAAA,QAAQ,CAAC,CAAA,CAAE,CAAC,CAAC;QAC5D,IAAI,gBAAgB,GAAgC,EAAE,CAAC;QACvD,+KAAW,CAAC,GAAG,CAAC,4BAA4B,IAAI,IAAI,EAAE;YACpD,gBAAgB,GAAG,CAAA,GAAA,QAAA,2BAA2B,GAAE,CAAC;SAClD,MAAM;YACL,gBAAgB,GAAG;gBAAC,YAAA,WAAW;gBAAE,YAAA,eAAe;gBAAE,YAAA,YAAY;aAAC,CAAC;SACjE;QAED,IAAI,CAAC,kBAAkB,GACrB,CAAA,KAAA,aAAa,CAAC,iBAAiB,MAAA,QAAA,OAAA,KAAA,IAAA,KAAI,gBAAgB,CAAC;QAEtD,IAAI,CAAC,YAAY,GAAG,aAAa,CAAC,WAAW,CAAC;QAE9C,IAAI,CAAC,oBAAoB,GAAG,CAAA,KAAA,aAAa,CAAC,mBAAmB,MAAA,QAAA,OAAA,KAAA,IAAA,KAAI,IAAI,CAAC;QAEtE,2EAA2E;QAC3E,IACE,aAAa,CAAC,aAAa,IAC3B,aAAa,CAAC,aAAa,IAC3B,aAAa,CAAC,cAAc,EAC5B;YACA,MAAM,oBAAoB,GAAqB,CAAA,CAAE,CAAC;YAElD,IAAI,aAAa,CAAC,OAAO,EAAE;gBACzB,oBAAoB,CAAC,OAAO,GAAG,aAAa,CAAC,OAAO,CAAC;aACtD;YACD,IAAI,aAAa,CAAC,UAAU,EAAE;gBAC5B,oBAAoB,CAAC,UAAU,GAAG,aAAa,CAAC,UAAU,CAAC;aAC5D;YACD,IAAI,aAAa,CAAC,WAAW,EAAE;gBAC7B,oBAAoB,CAAC,WAAW,GAAG,aAAa,CAAC,WAAW,CAAC;aAC9D;YAED,IAAI,aAAa,CAAC,aAAa,EAAE;gBAC/B,MAAA,IAAI,CAAC,IAAI,CACP,gFAAgF,CACjF,CAAC;aACH;YAED,MAAM,aAAa,GACjB,CAAA,KAAA,aAAa,CAAC,aAAa,MAAA,QAAA,OAAA,KAAA,IAAA,KAC3B,oEAAoE;YACpE,IAAI,iBAAA,kBAAkB,CAAC,aAAa,CAAC,aAAc,CAAC,CAAC;YAEvD,MAAM,cAAc,GAAG,CAAA,KAAA,aAAa,CAAC,cAAc,MAAA,QAAA,OAAA,KAAA,IAAA,KAAI;gBAAC,aAAa;aAAC,CAAC;YAEvE,IAAI,CAAC,qBAAqB,GAAG;gBAC3B,YAAY,EAAE,oBAAoB;gBAClC,cAAc;gBACd,cAAc,EAAE,aAAa,CAAC,cAAc;gBAC5C,iBAAiB,EAAE,aAAa,CAAC,iBAAiB;aACnD,CAAC;SACH;QAED,IAAI,aAAa,CAAC,kBAAkB,EAAE;YACpC,IAAI,CAAC,qBAAqB,GAAG;gBAC3B,kBAAkB,EAAE,aAAa,CAAC,kBAAkB;aACrD,CAAC;SACH;QAED,IAAI,aAAa,CAAC,YAAY,IAAI,aAAa,CAAC,KAAK,EAAE;YACrD,MAAM,mBAAmB,GAAwB,CAAA,CAAE,CAAC;YACpD,IAAI,aAAa,CAAC,YAAY,EAAE;gBAC9B,mBAAmB,CAAC,MAAM,GAAG,aAAa,CAAC,YAAY,CAAC;aACzD;YAED,IAAI,aAAa,CAAC,KAAK,EAAE;gBACvB,mBAAmB,CAAC,KAAK,GAAG,aAAa,CAAC,KAAK,CAAC;aACjD;YAED,IAAI,CAAC,oBAAoB,GAAG,mBAAmB,CAAC;SACjD;QAED,IAAI,CAAC,iBAAiB,GAAG,CAAA,KAAA,CAAA,KAAA,aAAa,CAAC,gBAAgB,MAAA,QAAA,OAAA,KAAA,IAAA,KAAA,IAAA,GAAE,IAAI,EAAE,MAAA,QAAA,OAAA,KAAA,IAAA,KAAI,EAAE,CAAC;IACxE,CAAC;IAED;;OAEG,CACI,KAAK,GAAA;;QACV,IAAI,IAAI,CAAC,SAAS,EAAE;YAClB,OAAO;SACR;QAED,CAAA,GAAA,kBAAA,wBAAwB,EAAC;YACvB,gBAAgB,EAAE,IAAI,CAAC,iBAAiB;SACzC,CAAC,CAAC;QAEH,IAAI,IAAI,CAAC,oBAAoB,EAAE;YAC7B,MAAM,cAAc,GAA4B;gBAC9C,SAAS,EAAE,IAAI,CAAC,kBAAkB;aACnC,CAAC;YAEF,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,KAAK,CACnC,CAAA,GAAA,YAAA,mBAAmB,EAAC,cAAc,CAAC,CACpC,CAAC;SACH;QAED,IAAI,CAAC,SAAS,GACZ,IAAI,CAAC,YAAY,KAAK,SAAS,GAC3B,IAAI,CAAC,SAAS,GACd,IAAI,CAAC,SAAS,CAAC,KAAK,CAClB,IAAI,YAAA,QAAQ,CAAC;YACX,CAAC,uBAAA,wBAAwB,CAAC,EAAE,IAAI,CAAC,YAAY;SAC9C,CAAC,CACH,CAAC;QAER,iJAAiJ;QACjJ,MAAM,QAAQ,GAAG,IAAI,CAAC,qBAAqB,GACvC,iBAAA,kBAAkB,GAClB,gCAAA,8BAA8B,CAAC;QAEnC,gIAAgI;QAChI,MAAM,cAAc,GAAG,IAAI,QAAQ,CAAA,OAAA,MAAA,CAAA,OAAA,MAAA,CAAA,CAAA,GAC9B,IAAI,CAAC,cAAc,GAAA;YACtB,QAAQ,EAAE,IAAI,CAAC,SAAS;QAAA,GACxB,CAAC;QAEH,IAAI,CAAC,eAAe,GAAG,cAAc,CAAC;QAEtC,IAAI,IAAI,CAAC,qBAAqB,EAAE;YAC9B,KAAK,MAAM,aAAa,IAAI,IAAI,CAAC,qBAAqB,CAAC,cAAc,CAAE;gBACrE,cAAc,CAAC,gBAAgB,CAAC,aAAa,CAAC,CAAC;aAChD;SACF;QAED,cAAc,CAAC,QAAQ,CAAC;YACtB,cAAc,EACZ,CAAA,KAAA,CAAA,KAAA,IAAI,CAAC,qBAAqB,MAAA,QAAA,OAAA,KAAA,IAAA,KAAA,IAAA,GAAE,cAAc,MAAA,QAAA,OAAA,KAAA,IAAA,KAC1C,+GAA+G;YAC/G,CAAA,KAAA,IAAI,CAAC,cAAc,MAAA,QAAA,OAAA,KAAA,IAAA,KAAA,IAAA,GAAE,cAAc;YACrC,UAAU,EAAE,CAAA,KAAA,IAAI,CAAC,qBAAqB,MAAA,QAAA,OAAA,KAAA,IAAA,KAAA,IAAA,GAAE,iBAAiB;SAC1D,CAAC,CAAC;QAEH,IAAI,IAAI,CAAC,qBAAqB,EAAE;YAC9B,MAAM,cAAc,GAAG,IAAI,WAAA,cAAc,CAAC;gBACxC,QAAQ,EAAE,IAAI,CAAC,SAAS;aACzB,CAAC,CAAC;YACH,cAAc,CAAC,qBAAqB,CAClC,IAAI,CAAC,qBAAqB,CAAC,kBAAkB,CAC9C,CAAC;YAEF,IAAI,CAAC,eAAe,GAAG,cAAc,CAAC;YAEtC,WAAA,IAAI,CAAC,uBAAuB,CAAC,cAAc,CAAC,CAAC;SAC9C;QAED,IAAI,IAAI,CAAC,oBAAoB,EAAE;YAC7B,MAAM,OAAO,GAAmB,EAAE,CAAC;YACnC,IAAI,IAAI,CAAC,oBAAoB,CAAC,MAAM,EAAE;gBACpC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,oBAAoB,CAAC,MAAM,CAAC,CAAC;aAChD;YACD,MAAM,aAAa,GAAG,IAAI,cAAA,aAAa,CAAC;gBACtC,QAAQ,EAAE,IAAI,CAAC,SAAS;gBACxB,KAAK,EAAE,CAAA,KAAA,CAAA,KAAA,IAAI,CAAC,oBAAoB,MAAA,QAAA,OAAA,KAAA,IAAA,KAAA,IAAA,GAAE,KAAK,MAAA,QAAA,OAAA,KAAA,IAAA,KAAI,EAAE;gBAC7C,OAAO,EAAE,OAAO;aACjB,CAAC,CAAC;YAEH,IAAI,CAAC,cAAc,GAAG,aAAa,CAAC;YAEpC,MAAA,OAAO,CAAC,sBAAsB,CAAC,aAAa,CAAC,CAAC;YAE9C,mGAAmG;YACnG,4GAA4G;YAC5G,4GAA4G;YAC5G,KAAK,MAAM,eAAe,IAAI,IAAI,CAAC,iBAAiB,CAAE;gBACpD,eAAe,CAAC,gBAAgB,CAAC,MAAA,OAAO,CAAC,gBAAgB,EAAE,CAAC,CAAC;aAC9D;SACF;IACH,CAAC;IAEM,QAAQ,GAAA;QACb,MAAM,QAAQ,GAAuB,EAAE,CAAC;QACxC,IAAI,IAAI,CAAC,eAAe,EAAE;YACxB,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,QAAQ,EAAE,CAAC,CAAC;SAChD;QACD,IAAI,IAAI,CAAC,eAAe,EAAE;YACxB,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,QAAQ,EAAE,CAAC,CAAC;SAChD;QACD,IAAI,IAAI,CAAC,cAAc,EAAE;YACvB,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,QAAQ,EAAE,CAAC,CAAC;SAC/C;QAED,OAAO,AACL,OAAO,CAAC,GAAG,CAAC,QAAQ,CAClB,AADmB,oDACiC;SACnD,IAAI,CAAC,GAAG,EAAE,AAAE,CAAC,CAAC,CAClB,CAAC;IACJ,CAAC;CACF;AA5OD,QAAA,OAAA,GAAA,QA4OC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 6634, "column": 0}, "map": {"version": 3, "file": "types.js", "sourceRoot": "", "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/node_modules/%40opentelemetry/sdk-node/src/types.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport type { ContextManager } from '@opentelemetry/api';\nimport { TextMapPropagator } from '@opentelemetry/api';\nimport { Instrumentation } from '@opentelemetry/instrumentation';\nimport { Detector, DetectorSync, IResource } from '@opentelemetry/resources';\nimport { LogRecordProcessor } from '@opentelemetry/sdk-logs';\nimport { MetricReader, View } from '@opentelemetry/sdk-metrics';\nimport {\n  Sampler,\n  SpanExporter,\n  SpanLimits,\n  SpanProcessor,\n  IdGenerator,\n} from '@opentelemetry/sdk-trace-base';\n\nexport interface NodeSDKConfiguration {\n  autoDetectResources: boolean;\n  contextManager: ContextManager;\n  textMapPropagator: TextMapPropagator;\n  logRecordProcessor: LogRecordProcessor;\n  metricReader: MetricReader;\n  views: View[];\n  instrumentations: (Instrumentation | Instrumentation[])[];\n  resource: IResource;\n  resourceDetectors: Array<Detector | DetectorSync>;\n  sampler: Sampler;\n  serviceName?: string;\n  /** @deprecated use spanProcessors instead*/\n  spanProcessor?: SpanProcessor;\n  spanProcessors?: SpanProcessor[];\n  traceExporter: SpanExporter;\n  spanLimits: SpanLimits;\n  idGenerator: IdGenerator;\n}\n"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 6657, "column": 0}, "map": {"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/node_modules/%40opentelemetry/sdk-node/src/index.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nexport * as api from '@opentelemetry/api';\nexport * as contextBase from '@opentelemetry/api';\nexport * as core from '@opentelemetry/core';\nexport * as logs from '@opentelemetry/sdk-logs';\nexport * as metrics from '@opentelemetry/sdk-metrics';\nexport * as node from '@opentelemetry/sdk-trace-node';\nexport * as resources from '@opentelemetry/resources';\nexport * as tracing from '@opentelemetry/sdk-trace-base';\nexport * from './sdk';\nexport * from './types';\n"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG;;;;;;;;;;;;;;;;;;;AAEH,QAAA,GAAA,iCAA0C;AAC1C,QAAA,WAAA,iCAAkD;AAClD,QAAA,IAAA,kCAA4C;AAC5C,QAAA,IAAA,sCAAgD;AAChD,QAAA,OAAA,yCAAsD;AACtD,QAAA,IAAA,4CAAsD;AACtD,QAAA,SAAA,uCAAsD;AACtD,QAAA,OAAA,4CAAyD;AACzD,mIAAA,SAAsB;AACtB,qIAAA,SAAwB", "ignoreList": [0], "debugId": null}}]}